<?php

/**
 * This script updates all confirmation dialogs in the codebase
 * from using JavaScript confirm() to using our custom modal system.
 */

// Directory to search in
$searchDir = __DIR__ . '/resources/views';

// Function to recursively find all Blade files
function findBladeFiles($dir) {
    $files = [];
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($iterator as $file) {
        if ($file->isFile() && $file->getExtension() === 'php') {
            $files[] = $file->getPathname();
        }
    }
    
    return $files;
}

// Function to update confirmation dialogs in a file
function updateConfirmations($filePath) {
    $content = file_get_contents($filePath);
    $originalContent = $content;
    
    // Pattern to match onclick="return confirm('...')" or onsubmit="return confirm('...')"
    $pattern = '/(onclick|onsubmit)="return confirm\([\'"](.+?)[\'"]\)"/';
    
    // Replace with data-confirm attribute
    $content = preg_replace($pattern, 'data-confirm="$2"', $content);
    
    // Save the file if changes were made
    if ($content !== $originalContent) {
        file_put_contents($filePath, $content);
        return true;
    }
    
    return false;
}

// Find all Blade files
$bladeFiles = findBladeFiles($searchDir);

// Update confirmation dialogs in each file
$updatedFiles = 0;
foreach ($bladeFiles as $file) {
    if (updateConfirmations($file)) {
        echo "Updated: " . str_replace(__DIR__ . '/', '', $file) . PHP_EOL;
        $updatedFiles++;
    }
}

echo PHP_EOL . "Updated $updatedFiles files." . PHP_EOL;
