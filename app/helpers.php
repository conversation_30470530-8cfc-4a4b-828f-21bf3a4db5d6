<?php

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Schema;

if (!function_exists('setting')) {
    /**
     * Get a setting value by key.
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function setting($key, $default = null)
    {
        // Check if the settings table exists
        if (!Schema::hasTable('settings')) {
            return $default;
        }

        // Try to get from cache first
        if (Cache::has('setting_' . $key)) {
            return Cache::get('setting_' . $key);
        }

        // Get from database
        $setting = Setting::where('key', $key)->first();

        if (!$setting) {
            return $default;
        }

        // Cache the result for future use
        Cache::put('setting_' . $key, $setting->value, now()->addHours(24));

        return $setting->value;
    }
}

if (!function_exists('admin_contact')) {
    /**
     * Get admin contact information.
     *
     * @param string $type (phone, whatsapp, email, telegram)
     * @param mixed $default
     * @return mixed
     */
    function admin_contact(string $type, $default = null)
    {
        return setting('admin_' . $type, $default);
    }
}

if (!function_exists('payment_instructions')) {
    /**
     * Get payment instructions.
     *
     * @return string
     */
    function payment_instructions()
    {
        return setting('payment_instructions', 'Please contact our admin team for payment processing instructions.');
    }
}

if (!function_exists('request_confirmation_message')) {
    /**
     * Get request confirmation message.
     *
     * @return string
     */
    function request_confirmation_message()
    {
        return setting('request_confirmation_message', 'Your request has been submitted successfully. Our admin team will contact you shortly.');
    }
}
