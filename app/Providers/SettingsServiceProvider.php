<?php

namespace App\Providers;

use App\Models\Setting;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Schema;

class SettingsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Only load settings if the settings table exists
        if (Schema::hasTable('settings')) {
            // Load all public settings
            $settings = Setting::where('is_public', true)->pluck('value', 'key')->toArray();

            // Share settings with all views
            View::share('settings', $settings);
        }
    }
}
