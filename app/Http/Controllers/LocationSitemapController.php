<?php

namespace App\Http\Controllers;

use App\Models\Location;
use App\Models\Escort;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class LocationSitemapController extends Controller
{
    /**
     * Generate location sitemap.
     */
    public function index()
    {
        $locations = Location::active()
            ->orderBy('type')
            ->orderBy('name')
            ->get();

        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        // Add locations index page
        $xml .= $this->addUrl(route('locations.index'), now(), 'weekly', '0.8');

        // Add individual location pages
        foreach ($locations as $location) {
            // Get escort count for priority calculation
            $escortCount = Escort::whereHas('locations', function($query) use ($location) {
                $query->where('location_id', $location->id);
            })->count();

            // Calculate priority based on location type and escort count
            $priority = $this->calculatePriority($location, $escortCount);
            
            // Calculate change frequency based on location type
            $changefreq = $this->getChangeFrequency($location);

            $xml .= $this->addUrl(
                route('locations.show', $location->slug),
                $location->updated_at,
                $changefreq,
                $priority
            );
        }

        $xml .= '</urlset>';

        return response($xml, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600'
        ]);
    }

    /**
     * Generate location sitemap index.
     */
    public function sitemapIndex()
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        // Add main location sitemap
        $xml .= $this->addSitemapUrl(route('sitemap.locations'), now());

        // Add country-specific sitemaps
        $countries = Location::active()->ofType('country')->get();
        foreach ($countries as $country) {
            $xml .= $this->addSitemapUrl(
                route('sitemap.locations.country', $country->slug),
                $country->updated_at
            );
        }

        $xml .= '</sitemapindex>';

        return response($xml, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600'
        ]);
    }

    /**
     * Generate country-specific location sitemap.
     */
    public function countryLocationSitemap($countrySlug)
    {
        $country = Location::active()
            ->ofType('country')
            ->where('slug', $countrySlug)
            ->firstOrFail();

        // Get all locations in this country
        $locations = Location::active()
            ->where(function($query) use ($country) {
                $query->where('id', $country->id)
                      ->orWhere('parent_id', $country->id)
                      ->orWhereHas('parent', function($q) use ($country) {
                          $q->where('parent_id', $country->id);
                      });
            })
            ->orderBy('type')
            ->orderBy('name')
            ->get();

        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        foreach ($locations as $location) {
            // Get escort count for priority calculation
            $escortCount = Escort::whereHas('locations', function($query) use ($location) {
                $query->where('location_id', $location->id);
            })->count();

            $priority = $this->calculatePriority($location, $escortCount);
            $changefreq = $this->getChangeFrequency($location);

            $xml .= $this->addUrl(
                route('locations.show', $location->slug),
                $location->updated_at,
                $changefreq,
                $priority
            );
        }

        $xml .= '</urlset>';

        return response($xml, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600'
        ]);
    }

    /**
     * Add URL to sitemap.
     */
    private function addUrl($url, $lastmod, $changefreq, $priority)
    {
        $xml = "  <url>\n";
        $xml .= "    <loc>" . htmlspecialchars($url) . "</loc>\n";
        $xml .= "    <lastmod>" . $lastmod->format('Y-m-d\TH:i:s\Z') . "</lastmod>\n";
        $xml .= "    <changefreq>{$changefreq}</changefreq>\n";
        $xml .= "    <priority>{$priority}</priority>\n";
        $xml .= "  </url>\n";
        
        return $xml;
    }

    /**
     * Add sitemap URL to sitemap index.
     */
    private function addSitemapUrl($url, $lastmod)
    {
        $xml = "  <sitemap>\n";
        $xml .= "    <loc>" . htmlspecialchars($url) . "</loc>\n";
        $xml .= "    <lastmod>" . $lastmod->format('Y-m-d\TH:i:s\Z') . "</lastmod>\n";
        $xml .= "  </sitemap>\n";
        
        return $xml;
    }

    /**
     * Calculate priority based on location type and escort count.
     */
    private function calculatePriority($location, $escortCount)
    {
        $basePriority = match($location->type) {
            'country' => 0.9,
            'city' => 0.8,
            'area' => 0.7,
            default => 0.6
        };

        // Boost priority based on escort count
        if ($escortCount > 50) {
            $basePriority = min(1.0, $basePriority + 0.1);
        } elseif ($escortCount > 20) {
            $basePriority = min(1.0, $basePriority + 0.05);
        } elseif ($escortCount > 10) {
            $basePriority = min(1.0, $basePriority + 0.02);
        }

        return number_format($basePriority, 1);
    }

    /**
     * Get change frequency based on location type.
     */
    private function getChangeFrequency($location)
    {
        return match($location->type) {
            'country' => 'monthly',
            'city' => 'weekly',
            'area' => 'weekly',
            default => 'monthly'
        };
    }
}
