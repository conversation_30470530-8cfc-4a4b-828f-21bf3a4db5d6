<?php

namespace App\Http\Controllers;

use Illuminate\Http\Response;

class RobotsController extends Controller
{
    /**
     * Generate robots.txt file
     */
    public function index(): Response
    {
        $content = $this->generateRobotsTxt();

        return response($content)
            ->header('Content-Type', 'text/plain');
    }

    /**
     * Generate robots.txt content
     */
    private function generateRobotsTxt(): string
    {
        $baseUrl = config('app.url');

        return "User-agent: *
Allow: /

# Allow search engines to crawl public pages
Allow: /escorts
Allow: /agencies
Allow: /locations
Allow: /contact

# Disallow private/admin areas
Disallow: /admin
Disallow: /dashboard
Disallow: /login
Disallow: /register
Disallow: /password
Disallow: /profile
Disallow: /api

# Disallow search and filter URLs to prevent duplicate content
Disallow: /*?search=
Disallow: /*?filter=
Disallow: /*?sort=
Disallow: /*?page=
Disallow: /*?location=
Disallow: /*?service=
Disallow: /*?age=
Disallow: /*?rate=

# Allow specific search engine bots
User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /

User-agent: Slurp
Allow: /

# Crawl delay for general bots
Crawl-delay: 1

# Sitemap location
Sitemap: {$baseUrl}/sitemap.xml

# Additional sitemaps
Sitemap: {$baseUrl}/sitemap/pages.xml
Sitemap: {$baseUrl}/sitemap/escorts.xml
Sitemap: {$baseUrl}/sitemap/agencies.xml
Sitemap: {$baseUrl}/sitemap/locations.xml
Sitemap: {$baseUrl}/sitemap/images.xml";
    }
}
