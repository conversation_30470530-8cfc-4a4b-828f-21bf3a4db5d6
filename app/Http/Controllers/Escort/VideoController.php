<?php

namespace App\Http\Controllers\Escort;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Models\EscortVideo;

class VideoController extends Controller
{
    /**
     * Display a listing of the escort's videos.
     */
    public function index()
    {
        $user = Auth::user();
        $escort = $user->escort;
        $videos = $escort->videos()->orderBy('sort_order')->get();

        return view('escort.videos.index', compact('escort', 'videos'));
    }

    /**
     * Store a newly created video in storage.
     */
    public function store(Request $request)
    {
        $escort = Auth::user()->escort;

        // Check if escort already has a video (limit to 1)
        if ($escort->videos()->count() >= 1) {
            return redirect()->route('escort.videos')->with('error', 'You can only upload 1 video. Please delete your existing video first.');
        }

        $request->validate([
            'video' => 'required|mimes:mp4,mov,avi,wmv,flv,webm|max:51200', // 50MB max
        ]);

        // Handle file upload
        $file = $request->file('video');
        $path = $file->store('escorts/' . $escort->id . '/videos', 'public');

        // Get file information
        $fileSize = $file->getSize();
        $mimeType = $file->getMimeType();

        // Create video record (always set as main since only 1 video allowed)
        $video = EscortVideo::create([
            'escort_id' => $escort->id,
            'video_path' => $path,
            'path' => $path, // For compatibility
            'is_main' => true, // Always main since only 1 video allowed
            'is_primary' => true, // Always primary since only 1 video allowed
            'file_size' => $fileSize,
            'mime_type' => $mimeType,
            'sort_order' => 1,
        ]);

        return redirect()->route('escort.videos')->with('success', 'Video uploaded successfully.');
    }

    /**
     * Remove the specified video from storage.
     */
    public function destroy($id)
    {
        $escort = Auth::user()->escort;
        $video = EscortVideo::where('id', $id)
            ->where('escort_id', $escort->id)
            ->firstOrFail();

        // Delete file from storage
        Storage::disk('public')->delete($video->path);
        
        // Delete thumbnail if exists
        if ($video->thumbnail_path) {
            Storage::disk('public')->delete($video->thumbnail_path);
        }

        // Delete record
        $video->delete();

        return redirect()->route('escort.videos')->with('success', 'Video deleted successfully.');
    }


}
