<?php

namespace App\Http\Controllers\Escort;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Location;

class LocationController extends Controller
{
    /**
     * Display a listing of locations for the escort to select.
     */
    public function index()
    {
        $escort = Auth::user()->escort;
        $countries = Location::ofType('country')->orderBy('name')->get();
        $selectedLocations = $escort->locations->pluck('id')->toArray();
        
        return view('escort.locations.index', compact('escort', 'countries', 'selectedLocations'));
    }

    /**
     * Update the escort's locations.
     */
    public function update(Request $request)
    {
        $request->validate([
            'locations' => 'array',
            'locations.*' => 'exists:locations,id',
        ]);
        
        $escort = Auth::user()->escort;
        
        // Sync locations
        $escort->locations()->sync($request->locations ?? []);
        
        return redirect()->route('escort.locations')->with('success', 'Locations updated successfully.');
    }
}
