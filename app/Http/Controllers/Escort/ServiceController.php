<?php

namespace App\Http\Controllers\Escort;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Service;

class ServiceController extends Controller
{
    /**
     * Display a listing of services for the escort to select.
     */
    public function index()
    {
        $escort = Auth::user()->escort;
        $allServices = Service::orderBy('name')->get();
        $selectedServices = $escort->services->pluck('id')->toArray();
        
        return view('escort.services.index', compact('escort', 'allServices', 'selectedServices'));
    }

    /**
     * Update the escort's services.
     */
    public function update(Request $request)
    {
        $request->validate([
            'services' => 'array',
            'services.*' => 'exists:services,id',
        ]);
        
        $escort = Auth::user()->escort;
        
        // Sync services
        $escort->services()->sync($request->services ?? []);
        
        return redirect()->route('escort.services')->with('success', 'Services updated successfully.');
    }
}
