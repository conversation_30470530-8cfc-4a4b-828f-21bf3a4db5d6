<?php

namespace App\Http\Controllers\Escort;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\EscortStatusRequest;
use App\Models\StatusPricing;
use App\Models\Notification;
use App\Services\NotificationService;

class StatusRequestController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Display a listing of the escort's status requests.
     */
    public function index()
    {
        $escort = Auth::user()->escort;
        $statusRequests = $escort->statusRequests()->latest()->get();

        return view('escort.status-requests.index', compact('escort', 'statusRequests'));
    }

    /**
     * Show the form for creating a new status request.
     */
    public function create()
    {
        $escort = Auth::user()->escort;

        // Check current status and pending requests
        $isVerified = $escort->is_verified;
        $isFeatured = $escort->is_featured && ($escort->featured_expires_at === null || $escort->featured_expires_at > now());

        // Check if there are any pending requests
        $pendingVerification = $escort->statusRequests()
            ->where('request_type', 'verification')
            ->where('status', 'pending')
            ->first();

        $pendingFeatured = $escort->statusRequests()
            ->where('request_type', 'featured')
            ->where('status', 'pending')
            ->first();

        // Get pricing information with error handling
        try {
            $verificationPricing = StatusPricing::verification()->active()->orderBy('duration')->get();
            $featuredPricing = StatusPricing::featured()->active()->orderBy('duration')->get();
        } catch (\Exception $e) {
            \Log::error('Error fetching pricing: ' . $e->getMessage());
            $verificationPricing = collect([]);
            $featuredPricing = collect([]);
        }

        // Get featured expiry information
        $featuredExpiresAt = $escort->featured_expires_at;

        return view('escort.status-requests.create', compact(
            'escort',
            'isVerified',
            'isFeatured',
            'pendingVerification',
            'pendingFeatured',
            'verificationPricing',
            'featuredPricing',
            'featuredExpiresAt'
        ));
    }

    /**
     * Store a newly created status request in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'request_type' => 'required|in:verification,featured',
            'duration' => 'required|in:day,week,month,annual',
        ]);

        $escort = Auth::user()->escort;

        // Enhanced conflict prevention checks
        if ($request->request_type === 'verification') {
            // Check if already verified
            if ($escort->is_verified) {
                return redirect()->back()->withErrors([
                    'request_type' => 'You are already verified. No need to request verification again.'
                ])->withInput();
            }

            // Check for pending verification request
            $existingRequest = $escort->statusRequests()
                ->where('request_type', 'verification')
                ->where('status', 'pending')
                ->first();

            if ($existingRequest) {
                return redirect()->back()->withErrors([
                    'request_type' => 'You already have a pending verification request submitted on ' . $existingRequest->created_at->format('M d, Y') . '.'
                ])->withInput();
            }
        }

        if ($request->request_type === 'featured') {
            // Check if already featured and not expired
            if ($escort->is_featured && ($escort->featured_expires_at === null || $escort->featured_expires_at > now())) {
                $expiryText = $escort->featured_expires_at ? ' until ' . $escort->featured_expires_at->format('M d, Y') : '';
                return redirect()->back()->withErrors([
                    'request_type' => 'You are already featured' . $expiryText . '. Wait for it to expire before requesting again.'
                ])->withInput();
            }

            // Check for pending featured request
            $existingRequest = $escort->statusRequests()
                ->where('request_type', 'featured')
                ->where('status', 'pending')
                ->first();

            if ($existingRequest) {
                return redirect()->back()->withErrors([
                    'request_type' => 'You already have a pending featured request submitted on ' . $existingRequest->created_at->format('M d, Y') . '.'
                ])->withInput();
            }
        }

        // Get the pricing for this request type and duration
        $pricing = StatusPricing::where('request_type', $request->request_type)
            ->where('duration', $request->duration)
            ->where('is_active', true)
            ->first();

        if (!$pricing) {
            return redirect()->back()->withErrors([
                'duration' => 'The selected duration is not available for this request type.'
            ])->withInput();
        }

        // Create the status request
        $statusRequest = new EscortStatusRequest();
        $statusRequest->escort_id = $escort->id;
        $statusRequest->request_type = $request->request_type;
        $statusRequest->duration = $request->duration;
        $statusRequest->price = $pricing->price;
        $statusRequest->save();

        // Notify admins about the new request
        $this->notificationService->notifyUserTypes(
            ['admin'],
            'created',
            'escort_status_request',
            $escort->name,
            [
                'escort_id' => $escort->id,
                'request_id' => $statusRequest->id,
                'request_type' => $request->request_type,
                'duration' => $request->duration,
                'price' => $pricing->price
            ]
        );

        // Show confirmation page
        return redirect()->route('escort.status-requests.confirm', $statusRequest->id);
    }

    /**
     * Display the confirmation page for a status request.
     */
    public function confirm($id)
    {
        $escort = Auth::user()->escort;
        $statusRequest = EscortStatusRequest::where('id', $id)
            ->where('escort_id', $escort->id)
            ->firstOrFail();

        // Get admin contact information from settings
        $adminPhone = \App\Models\Setting::where('key', 'admin_phone')->first()?->value ?? '+256 700 000000';

        return view('escort.status-requests.confirm', compact('escort', 'statusRequest', 'adminPhone'));
    }

    /**
     * Display the specified status request.
     */
    public function show($id)
    {
        $escort = Auth::user()->escort;
        $statusRequest = EscortStatusRequest::where('id', $id)
            ->where('escort_id', $escort->id)
            ->firstOrFail();

        return view('escort.status-requests.show', compact('escort', 'statusRequest'));
    }

    /**
     * Cancel the specified status request.
     */
    public function cancel($id)
    {
        $escort = Auth::user()->escort;
        $statusRequest = EscortStatusRequest::where('id', $id)
            ->where('escort_id', $escort->id)
            ->where('status', 'pending')
            ->firstOrFail();

        // Delete the request
        $statusRequest->delete();

        return redirect()->route('escort.status-requests.index')->with('success', 'Request cancelled successfully.');
    }
}
