<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Providers\RouteServiceProvider;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules;
use Illuminate\View\View;

class RegisteredUserController extends Controller
{
    /**
     * Display the registration view.
     */
    public function create(): View
    {
        return view('auth.register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'username' => ['required', 'string', 'max:255', 'unique:'.User::class],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
            'user_type' => ['required', 'string', 'in:escort,agency'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'terms' => ['required', 'accepted'],
        ]);

        $user = User::create([
            'username' => $request->username,
            'email' => $request->email,
            'user_type' => $request->user_type,
            'password' => Hash::make($request->password),
            'is_active' => true,
            'email_verified_at' => null, // Will be set when email is verified
        ]);

        // Create user profile
        $user->profile()->create([
            // Empty profile, will be filled later
        ]);

        // If user is an escort or agency, create the corresponding record
        if ($request->user_type === 'escort') {
            // Create a placeholder escort profile
            $user->escort()->create([
                'name' => $request->username,
                'gender' => 'female', // Default, can be updated later
                'date_of_birth' => now()->subYears(18), // Default, must be updated later
                'ethnicity' => 'Other',
                'hair_color' => 'Other',
                'hair_length' => 'Short',
                'height_cm' => 170,
                'weight_kg' => 60,
                'build' => 'Regular',
                'looks' => 'Average',
                'is_independent' => true,
                'is_new' => true,
                'slug' => Str::slug($request->username),
            ]);
        } elseif ($request->user_type === 'agency') {
            // Create a placeholder agency profile
            $user->agency()->create([
                'name' => $request->username . ' Agency',
                'description' => 'Agency description',
                'slug' => Str::slug($request->username . ' Agency'),
            ]);
        }

        event(new Registered($user));

        Auth::login($user);

        // Redirect to dashboard
        return redirect(RouteServiceProvider::HOME);
    }
}
