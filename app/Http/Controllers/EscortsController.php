<?php

namespace App\Http\Controllers;

use App\Models\Escort;
use App\Models\Location;
use App\Models\Service;
use App\Services\SeoService;
use Illuminate\Http\Request;

class EscortsController extends Controller
{
    protected $seoService;

    public function __construct(SeoService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Display a listing of escorts.
     */
    public function index(Request $request)
    {
        // Base query - only show active escorts
        $query = Escort::with(['primaryImage', 'rates', 'locations'])
            ->whereHas('user', function ($q) {
                $q->where('is_active', true);
            });

        // Apply filters if provided
        if ($request->has('gender')) {
            $query->where('gender', $request->gender);
        }

        if ($request->has('location_id')) {
            $query->whereHas('locations', function ($q) use ($request) {
                $q->where('location_id', $request->location_id);
            });
        }

        if ($request->has('service_id')) {
            $query->whereHas('services', function ($q) use ($request) {
                $q->where('service_id', $request->service_id);
            });
        }

        if ($request->has('incall') && $request->incall) {
            $query->where('incall_available', true);
        }

        if ($request->has('outcall') && $request->outcall) {
            $query->where('outcall_available', true);
        }

        // Get featured escorts for the top section - use the new scope with expiration check
        $featuredEscorts = Escort::with(['primaryImage', 'rates', 'locations'])
            ->featured()
            ->whereHas('user', function ($q) {
                $q->where('is_active', true);
            })
            ->latest()
            ->take(4)
            ->get();

        // Get the main list of escorts with pagination, prioritizing verified escorts
        $escorts = $query->orderBy('is_verified', 'desc')->latest()->paginate(12);

        // Get locations and services for filters with error handling
        try {
            $locations = Location::where('type', 'city')->orderBy('name')->get();
        } catch (\Exception $e) {
            $locations = collect();
        }

        try {
            $services = Service::orderBy('name')->get();
        } catch (\Exception $e) {
            $services = collect();
        }

        // Generate SEO meta data
        $locationFilter = $request->has('location_id') ?
            Location::find($request->location_id)?->name : null;
        $seoMeta = $this->seoService->getEscortsPageMeta($locationFilter);

        return view('escorts.index', compact('escorts', 'featuredEscorts', 'locations', 'services', 'seoMeta'));
    }

    /**
     * Display the specified escort.
     */
    public function show($slug)
    {
        $escort = Escort::with([
            'images' => function($query) {
                $query->orderBy('is_primary', 'desc')
                      ->orderBy('is_main', 'desc')
                      ->orderBy('sort_order', 'asc')
                      ->orderBy('id', 'asc');
            },
            'videos' => function($query) {
                $query->orderBy('is_primary', 'desc')
                      ->orderBy('is_main', 'desc')
                      ->orderBy('sort_order', 'asc')
                      ->orderBy('id', 'asc');
            },
            'services',
            'rates',
            'locations',
            'languages',
            'user.profile'
        ])
        ->where('slug', $slug)
        ->whereHas('user', function ($q) {
            $q->where('is_active', true);
        })
        ->firstOrFail();

        // Increment view count
        $escort->incrementViews();

        // Get similar escorts (same gender, location)
        $similarEscorts = Escort::with(['primaryImage'])
            ->where('id', '!=', $escort->id)
            ->where('gender', $escort->gender)
            ->whereHas('user', function ($q) {
                $q->where('is_active', true);
            });

        // Only filter by location if the escort has locations
        if ($escort->locations->count() > 0) {
            $similarEscorts->whereHas('locations', function ($query) use ($escort) {
                $query->whereIn('location_id', $escort->locations->pluck('id'));
            });
        }

        $similarEscorts = $similarEscorts->orderBy('is_verified', 'desc')->take(4)->get();

        // Generate SEO meta data and structured data
        $seoMeta = $this->seoService->getEscortProfileMeta($escort);
        $structuredData = $this->seoService->getEscortStructuredData($escort);
        $seoContent = $this->seoService->generateEscortSeoContent($escort);

        // Analytics data
        $analyticsPage = 'escort';
        $analyticsEntity = $escort;

        return view('escorts.show', compact('escort', 'similarEscorts', 'seoMeta', 'structuredData', 'seoContent', 'analyticsPage', 'analyticsEntity'));
    }

    /**
     * Search for escorts based on criteria.
     */
    public function search(Request $request)
    {
        return $this->index($request);
    }
}
