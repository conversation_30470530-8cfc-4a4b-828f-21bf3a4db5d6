<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;

class TermsController extends Controller
{
    /**
     * Display the terms of service page.
     */
    public function index()
    {
        return view('terms.index');
    }

    /**
     * Display the privacy policy page.
     */
    public function privacy()
    {
        return view('terms.privacy');
    }

    /**
     * Download the terms of service PDF.
     */
    public function downloadPdf()
    {
        $filePath = public_path('TERMS OF USE - Google Docs.pdf');

        if (!file_exists($filePath)) {
            abort(404, 'Terms of Service PDF not found.');
        }

        return response()->download($filePath, 'Get-Hot-Babes-Terms-of-Service.pdf');
    }

    /**
     * Display the terms PDF inline in browser.
     */
    public function viewPdf()
    {
        $filePath = public_path('TERMS OF USE - Google Docs.pdf');

        if (!file_exists($filePath)) {
            abort(404, 'Terms of Service PDF not found.');
        }

        return response()->file($filePath, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="Get-Hot-Babes-Terms-of-Service.pdf"'
        ]);
    }
}
