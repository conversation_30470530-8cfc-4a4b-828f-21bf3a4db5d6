<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\EscortStatusRequest;
use App\Models\AgencyStatusRequest;

class AllRequestsController extends Controller
{
    /**
     * Display a unified listing of all status requests.
     */
    public function index(Request $request)
    {
        // Get escort requests with proper error handling
        try {
            $escortRequests = EscortStatusRequest::with(['escort' => function($query) {
                    $query->with('user');
                }])
                ->select('id', 'escort_id', 'request_type', 'status', 'created_at', 'price', 'duration')
                ->selectRaw("'escort' as request_category")
                ->get()
                ->filter(function ($request) {
                    // Only include requests with valid escort and user relationships
                    return $request->escort &&
                           $request->escort->user &&
                           !empty($request->escort->name) &&
                           !empty($request->escort->user->email);
                })
                ->map(function ($request) {
                    return [
                        'id' => $request->id,
                        'category' => 'escort',
                        'user_name' => $request->escort->name ?? 'Unknown',
                        'user_id' => $request->escort_id,
                        'request_type' => $request->request_type,
                        'status' => $request->status,
                        'price' => $request->price,
                        'duration' => $request->duration,
                        'created_at' => $request->created_at,
                        'view_route' => route('admin.status-requests.show', $request->id),
                    ];
                });
        } catch (\Exception $e) {
            \Log::error('Error fetching escort requests: ' . $e->getMessage());
            $escortRequests = collect([]);
        }

        // Get agency requests with proper error handling
        try {
            $agencyRequests = AgencyStatusRequest::with(['agency' => function($query) {
                    $query->with('user');
                }])
                ->select('id', 'agency_id', 'request_type', 'status', 'created_at', 'price', 'duration')
                ->selectRaw("'agency' as request_category")
                ->get()
                ->filter(function ($request) {
                    // Only include requests with valid agency and user relationships
                    return $request->agency &&
                           $request->agency->user &&
                           !empty($request->agency->name) &&
                           !empty($request->agency->user->email);
                })
                ->map(function ($request) {
                    return [
                        'id' => $request->id,
                        'category' => 'agency',
                        'user_name' => $request->agency->name ?? 'Unknown',
                        'user_id' => $request->agency_id,
                        'request_type' => $request->request_type,
                        'status' => $request->status,
                        'price' => $request->price,
                        'duration' => $request->duration,
                        'created_at' => $request->created_at,
                        'view_route' => route('admin.agency-status-requests.show', $request->id),
                    ];
                });
        } catch (\Exception $e) {
            \Log::error('Error fetching agency requests: ' . $e->getMessage());
            $agencyRequests = collect([]);
        }

        // Combine and sort all requests
        $allRequests = $escortRequests->concat($agencyRequests)
            ->sortByDesc('created_at');

        // Apply filters
        if ($request->has('status') && $request->status) {
            $allRequests = $allRequests->where('status', $request->status);
        }

        if ($request->has('category') && $request->category) {
            $allRequests = $allRequests->where('category', $request->category);
        }

        if ($request->has('request_type') && $request->request_type) {
            $allRequests = $allRequests->where('request_type', $request->request_type);
        }

        // Convert to paginated collection
        $perPage = 15;
        $currentPage = $request->get('page', 1);
        $offset = ($currentPage - 1) * $perPage;

        $paginatedRequests = $allRequests->slice($offset, $perPage)->values();
        $total = $allRequests->count();

        // Create pagination manually
        $pagination = new \Illuminate\Pagination\LengthAwarePaginator(
            $paginatedRequests,
            $total,
            $perPage,
            $currentPage,
            [
                'path' => $request->url(),
                'pageName' => 'page',
            ]
        );

        $pagination->appends($request->query());

        return view('admin.all-requests.index', compact('pagination'));
    }
}
