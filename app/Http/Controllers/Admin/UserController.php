<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use App\Services\AdminNotificationService;

class UserController extends Controller
{
    /**
     * The admin notification service instance.
     */
    protected $adminNotificationService;

    /**
     * Create a new controller instance.
     *
     * @param AdminNotificationService $adminNotificationService
     * @return void
     */
    public function __construct(AdminNotificationService $adminNotificationService)
    {
        $this->adminNotificationService = $adminNotificationService;
    }

    /**
     * Display a listing of all users.
     */
    public function index(Request $request)
    {
        $query = User::with(['profile', 'escort']);

        // Apply filters
        if ($request->has('user_type') && $request->user_type) {
            $query->where('user_type', $request->user_type);
        }

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('username', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $users = $query->latest()->paginate(20);

        return view('admin.users.index', compact('users'));
    }

    /**
     * Show the form for creating a new user.
     */
    public function create()
    {
        return view('admin.users.create');
    }

    /**
     * Store a newly created user in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'username' => 'required|string|max:255|unique:users,username',
            'email' => 'required|string|email|max:255|unique:users,email',
            'user_type' => 'required|in:admin,escort,agency,regular',
            'is_active' => 'boolean',
            'password' => 'required|string|min:8|confirmed',
        ]);

        // Hash the password
        $validated['password'] = Hash::make($validated['password']);

        // Create the user
        $user = User::create($validated);

        // Create user profile
        $user->profile()->create([
            // Empty profile, will be filled later
        ]);

        // If user is an escort or agency, create the corresponding record
        if ($validated['user_type'] === 'escort') {
            // Create a placeholder escort profile
            $user->escort()->create([
                'name' => $validated['username'],
                'gender' => 'female', // Default, can be updated later
                'date_of_birth' => now()->subYears(18), // Default, must be updated later
                'ethnicity' => 'Other',
                'hair_color' => 'Other',
                'hair_length' => 'Short',
                'height_cm' => 170,
                'weight_kg' => 60,
                'build' => 'Regular',
                'looks' => 'Average',
                'is_independent' => true,
                'is_new' => true,
                'slug' => \Illuminate\Support\Str::slug($validated['username']),
            ]);
        } elseif ($validated['user_type'] === 'agency') {
            // Create a placeholder agency profile
            $user->agency()->create([
                'name' => $validated['username'] . ' Agency',
                'description' => 'Agency description',
                'slug' => \Illuminate\Support\Str::slug($validated['username'] . ' Agency'),
            ]);
        }

        // Create notification for admins about the new user
        $this->adminNotificationService->notifyUserTypes(
            ['admin'],
            'created',
            'user',
            $validated['username'],
            [
                'user_type' => $validated['user_type'],
                'email' => $validated['email']
            ]
        );

        return redirect()->route('admin.users')->with('success', 'User created successfully.');
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit($id)
    {
        $user = User::with(['profile', 'escort', 'agency'])->findOrFail($id);

        return view('admin.users.edit', compact('user'));
    }

    /**
     * Update the specified user in storage.
     */
    public function update(Request $request, $id)
    {
        $user = User::findOrFail($id);

        $validated = $request->validate([
            'username' => 'required|string|max:255|unique:users,username,' . $id,
            'email' => 'required|string|email|max:255|unique:users,email,' . $id,
            'is_active' => 'boolean',
            'password' => 'nullable|string|min:8|confirmed',
        ]);

        // Only update password if provided
        if (isset($validated['password']) && $validated['password']) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        $user->update($validated);

        // Create notification for all users about the user update
        $this->adminNotificationService->notifyUserTypes(
            ['admin'],
            'updated',
            'user',
            $validated['username'],
            [
                'email' => $validated['email']
            ]
        );

        // If this is the user being updated, notify them directly
        if ($user->id !== auth()->id()) {
            $this->adminNotificationService->notifyUser(
                $user,
                'updated',
                'your account',
                '',
                ['updated_by_admin' => auth()->user()->username]
            );
        }

        return redirect()->route('admin.users')->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified user from storage.
     */
    public function destroy($id)
    {
        $user = User::findOrFail($id);

        // Prevent deleting admin users
        if ($user->isAdmin()) {
            return redirect()->route('admin.users')->with('error', 'Cannot delete admin users.');
        }

        $username = $user->username;
        $userType = $user->user_type;
        $email = $user->email;

        $user->delete();

        // Create notification for admins about the deleted user
        $this->adminNotificationService->notifyUserTypes(
            ['admin'],
            'deleted',
            'user',
            $username,
            [
                'user_type' => $userType,
                'email' => $email
            ]
        );

        return redirect()->route('admin.users')->with('success', 'User deleted successfully.');
    }
}
