<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Location;
use Illuminate\Support\Str;

class LocationController extends Controller
{
    /**
     * Display a listing of all locations.
     */
    public function index(Request $request)
    {
        $query = Location::with('parent');

        // Apply filters
        if ($request->has('type') && $request->type) {
            $query->where('type', $request->type);
        }

        if ($request->has('parent_id') && $request->parent_id) {
            $query->where('parent_id', $request->parent_id);
        }

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where('name', 'like', "%{$search}%");
        }

        // Order by type hierarchy and then by name
        $locations = $query->orderByRaw("FIELD(type, 'country', 'state', 'city', 'area')")
                          ->orderBy('name')
                          ->paginate(50); // Increased pagination to show more locations

        $parentLocations = Location::whereIn('type', ['country', 'state', 'city'])->orderBy('name')->get();

        // Get summary statistics
        $stats = [
            'total' => Location::count(),
            'countries' => Location::where('type', 'country')->count(),
            'states' => Location::where('type', 'state')->count(),
            'cities' => Location::where('type', 'city')->count(),
            'areas' => Location::where('type', 'area')->count(),
        ];

        return view('admin.locations.index', compact('locations', 'parentLocations', 'stats'));
    }

    /**
     * Show the form for creating a new location.
     */
    public function create()
    {
        $parentLocations = Location::whereIn('type', ['country', 'state'])->orderBy('name')->get();

        return view('admin.locations.create', compact('parentLocations'));
    }

    /**
     * Store a newly created location in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:country,state,city,area',
            'parent_id' => 'nullable|exists:locations,id',
        ]);

        // Generate slug
        $validated['slug'] = Str::slug($validated['name']);

        // Create the location
        $location = Location::create($validated);

        return redirect()->route('admin.locations')->with('success', 'Location created successfully.');
    }

    /**
     * Show the form for editing the specified location.
     */
    public function edit($id)
    {
        $location = Location::findOrFail($id);
        $parentLocations = Location::whereIn('type', ['country', 'state'])
            ->where('id', '!=', $id)
            ->orderBy('name')
            ->get();

        return view('admin.locations.edit', compact('location', 'parentLocations'));
    }

    /**
     * Update the specified location in storage.
     */
    public function update(Request $request, $id)
    {
        $location = Location::findOrFail($id);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:country,state,city,area',
            'parent_id' => 'nullable|exists:locations,id',
        ]);

        // Generate slug if name changed
        if ($location->name !== $validated['name']) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        // Prevent circular references
        if ($validated['parent_id'] == $id) {
            $validated['parent_id'] = null;
        }

        $location->update($validated);

        return redirect()->route('admin.locations')->with('success', 'Location updated successfully.');
    }

    /**
     * Remove the specified location from storage.
     */
    public function destroy($id)
    {
        $location = Location::findOrFail($id);

        // Check if location has children
        if ($location->children()->exists()) {
            return redirect()->route('admin.locations')->with('error', 'Cannot delete location with child locations.');
        }

        $location->delete();

        return redirect()->route('admin.locations')->with('success', 'Location deleted successfully.');
    }
}
