<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;

class AnalyticsConfigController extends Controller
{
    /**
     * Display analytics configuration page
     */
    public function index()
    {
        $analyticsSettings = [
            'google_analytics_id' => Setting::getValue('google_analytics_id', ''),
            'google_analytics_measurement_id' => Setting::getValue('google_analytics_measurement_id', ''),
            'google_search_console_verification' => Setting::getValue('google_search_console_verification', ''),
            'google_search_console_property' => Setting::getValue('google_search_console_property', ''),
            'facebook_pixel_id' => Setting::getValue('facebook_pixel_id', ''),
            'hotjar_id' => Setting::getValue('hotjar_id', ''),
            'bing_webmaster_verification' => Setting::getValue('bing_webmaster_verification', ''),
            'analytics_enabled' => Setting::getValue('analytics_enabled', false),
        ];

        $setupStatus = $this->getSetupStatus($analyticsSettings);

        return view('admin.analytics.config', compact('analyticsSettings', 'setupStatus'));
    }

    /**
     * Update analytics configuration
     */
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'google_analytics_id' => 'nullable|string|max:255',
            'google_analytics_measurement_id' => 'nullable|string|max:255',
            'google_search_console_verification' => 'nullable|string|max:255',
            'google_search_console_property' => 'nullable|url|max:255',
            'facebook_pixel_id' => 'nullable|string|max:255',
            'hotjar_id' => 'nullable|string|max:255',
            'bing_webmaster_verification' => 'nullable|string|max:255',
            'analytics_enabled' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Update all analytics settings
            $settings = [
                'google_analytics_id' => $request->input('google_analytics_id'),
                'google_analytics_measurement_id' => $request->input('google_analytics_measurement_id'),
                'google_search_console_verification' => $request->input('google_search_console_verification'),
                'google_search_console_property' => $request->input('google_search_console_property'),
                'facebook_pixel_id' => $request->input('facebook_pixel_id'),
                'hotjar_id' => $request->input('hotjar_id'),
                'bing_webmaster_verification' => $request->input('bing_webmaster_verification'),
                'analytics_enabled' => $request->boolean('analytics_enabled'),
            ];

            foreach ($settings as $key => $value) {
                Setting::setValue($key, $value);
            }

            // Clear cache to ensure new settings take effect
            Cache::forget('analytics_settings');

            return redirect()->back()->with('success', 'Analytics configuration updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update analytics configuration: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Test analytics configuration
     */
    public function test(Request $request)
    {
        $service = $request->input('service');
        $testResults = [];

        try {
            switch ($service) {
                case 'google_analytics':
                    $testResults = $this->testGoogleAnalytics();
                    break;
                case 'google_search_console':
                    $testResults = $this->testGoogleSearchConsole();
                    break;
                case 'facebook_pixel':
                    $testResults = $this->testFacebookPixel();
                    break;
                case 'hotjar':
                    $testResults = $this->testHotjar();
                    break;
                default:
                    $testResults = ['status' => 'error', 'message' => 'Unknown service'];
            }

            return response()->json($testResults);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Test failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Generate verification files
     */
    public function generateVerification(Request $request)
    {
        $service = $request->input('service');
        $verificationCode = $request->input('verification_code');

        try {
            switch ($service) {
                case 'google_search_console':
                    return $this->generateGoogleVerification($verificationCode);
                case 'bing_webmaster':
                    return $this->generateBingVerification($verificationCode);
                default:
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Unknown verification service'
                    ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to generate verification: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get setup status for all analytics services
     */
    private function getSetupStatus($settings): array
    {
        return [
            'google_analytics' => [
                'configured' => !empty($settings['google_analytics_id']),
                'status' => !empty($settings['google_analytics_id']) ? 'configured' : 'pending',
                'message' => !empty($settings['google_analytics_id']) ? 'Google Analytics is configured' : 'Add your Google Analytics ID',
            ],
            'google_search_console' => [
                'configured' => !empty($settings['google_search_console_verification']),
                'status' => !empty($settings['google_search_console_verification']) ? 'configured' : 'pending',
                'message' => !empty($settings['google_search_console_verification']) ? 'Search Console is configured' : 'Add verification code',
            ],
            'facebook_pixel' => [
                'configured' => !empty($settings['facebook_pixel_id']),
                'status' => !empty($settings['facebook_pixel_id']) ? 'configured' : 'pending',
                'message' => !empty($settings['facebook_pixel_id']) ? 'Facebook Pixel is configured' : 'Add your Pixel ID',
            ],
            'hotjar' => [
                'configured' => !empty($settings['hotjar_id']),
                'status' => !empty($settings['hotjar_id']) ? 'configured' : 'pending',
                'message' => !empty($settings['hotjar_id']) ? 'Hotjar is configured' : 'Add your Hotjar ID',
            ],
            'overall_progress' => $this->calculateOverallProgress($settings),
        ];
    }

    /**
     * Calculate overall setup progress
     */
    private function calculateOverallProgress($settings): int
    {
        $totalServices = 4;
        $configuredServices = 0;

        if (!empty($settings['google_analytics_id'])) $configuredServices++;
        if (!empty($settings['google_search_console_verification'])) $configuredServices++;
        if (!empty($settings['facebook_pixel_id'])) $configuredServices++;
        if (!empty($settings['hotjar_id'])) $configuredServices++;

        return round(($configuredServices / $totalServices) * 100);
    }

    /**
     * Test Google Analytics configuration
     */
    private function testGoogleAnalytics(): array
    {
        $gaId = Setting::getValue('google_analytics_id');
        
        if (empty($gaId)) {
            return ['status' => 'error', 'message' => 'Google Analytics ID not configured'];
        }

        // Basic validation of GA ID format
        if (!preg_match('/^G-[A-Z0-9]+$/', $gaId) && !preg_match('/^UA-\d+-\d+$/', $gaId)) {
            return ['status' => 'error', 'message' => 'Invalid Google Analytics ID format'];
        }

        return [
            'status' => 'success',
            'message' => 'Google Analytics ID format is valid',
            'details' => [
                'id' => $gaId,
                'type' => str_starts_with($gaId, 'G-') ? 'GA4' : 'Universal Analytics',
                'tracking_active' => true // Would check actual tracking in production
            ]
        ];
    }

    /**
     * Test Google Search Console configuration
     */
    private function testGoogleSearchConsole(): array
    {
        $verification = Setting::getValue('google_search_console_verification');
        
        if (empty($verification)) {
            return ['status' => 'error', 'message' => 'Google Search Console verification not configured'];
        }

        return [
            'status' => 'success',
            'message' => 'Google Search Console verification code is configured',
            'details' => [
                'verification_method' => 'HTML meta tag',
                'code_length' => strlen($verification),
                'verified' => true // Would check actual verification in production
            ]
        ];
    }

    /**
     * Test Facebook Pixel configuration
     */
    private function testFacebookPixel(): array
    {
        $pixelId = Setting::getValue('facebook_pixel_id');
        
        if (empty($pixelId)) {
            return ['status' => 'error', 'message' => 'Facebook Pixel ID not configured'];
        }

        // Basic validation of Pixel ID format
        if (!preg_match('/^\d+$/', $pixelId)) {
            return ['status' => 'error', 'message' => 'Invalid Facebook Pixel ID format'];
        }

        return [
            'status' => 'success',
            'message' => 'Facebook Pixel ID format is valid',
            'details' => [
                'pixel_id' => $pixelId,
                'events_configured' => ['PageView', 'ViewContent', 'Contact'],
                'active' => true
            ]
        ];
    }

    /**
     * Test Hotjar configuration
     */
    private function testHotjar(): array
    {
        $hotjarId = Setting::getValue('hotjar_id');
        
        if (empty($hotjarId)) {
            return ['status' => 'error', 'message' => 'Hotjar ID not configured'];
        }

        // Basic validation of Hotjar ID format
        if (!preg_match('/^\d+$/', $hotjarId)) {
            return ['status' => 'error', 'message' => 'Invalid Hotjar ID format'];
        }

        return [
            'status' => 'success',
            'message' => 'Hotjar ID format is valid',
            'details' => [
                'site_id' => $hotjarId,
                'tracking_active' => true,
                'features' => ['Heatmaps', 'Recordings', 'Surveys']
            ]
        ];
    }

    /**
     * Generate Google Search Console verification file
     */
    private function generateGoogleVerification($verificationCode): \Illuminate\Http\JsonResponse
    {
        if (empty($verificationCode)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Verification code is required'
            ]);
        }

        // The verification is handled via meta tag in the layout
        // Update the setting
        Setting::setValue('google_search_console_verification', $verificationCode);

        return response()->json([
            'status' => 'success',
            'message' => 'Google Search Console verification configured',
            'details' => [
                'method' => 'HTML meta tag',
                'location' => 'Added to site header automatically',
                'next_step' => 'Verify ownership in Google Search Console'
            ]
        ]);
    }

    /**
     * Generate Bing Webmaster verification
     */
    private function generateBingVerification($verificationCode): \Illuminate\Http\JsonResponse
    {
        if (empty($verificationCode)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Verification code is required'
            ]);
        }

        // Update the setting
        Setting::setValue('bing_webmaster_verification', $verificationCode);

        return response()->json([
            'status' => 'success',
            'message' => 'Bing Webmaster verification configured',
            'details' => [
                'method' => 'HTML meta tag',
                'location' => 'Added to site header automatically',
                'next_step' => 'Verify ownership in Bing Webmaster Tools'
            ]
        ]);
    }
}
