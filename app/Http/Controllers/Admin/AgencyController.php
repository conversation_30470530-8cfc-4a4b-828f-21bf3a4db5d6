<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Agency;
use App\Models\User;
use Illuminate\Support\Str;

class AgencyController extends Controller
{
    /**
     * Display a listing of all agencies.
     */
    public function index(Request $request)
    {
        $query = Agency::with('user');

        // Apply filters
        if ($request->has('is_verified') && $request->is_verified !== null) {
            $query->where('is_verified', $request->is_verified);
        }

        if ($request->has('is_premium') && $request->is_premium !== null) {
            $query->where('is_premium', $request->is_premium);
        }

        if ($request->has('is_featured') && $request->is_featured !== null) {
            $query->where('is_featured', $request->is_featured);
        }

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhereHas('user', function($uq) use ($search) {
                      $uq->where('username', 'like', "%{$search}%")
                         ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        $agencies = $query->latest()->paginate(20);

        return view('admin.agencies.index', compact('agencies'));
    }

    /**
     * Show the form for creating a new agency.
     */
    public function create()
    {
        $users = User::where('user_type', 'agency')
            ->whereDoesntHave('agency')
            ->orderBy('username')
            ->get();

        return view('admin.agencies.create', compact('users'));
    }

    /**
     * Store a newly created agency in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'nullable|exists:users,id',
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'website' => 'nullable|url|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string|max:255',
            'is_verified' => 'boolean',
            'is_premium' => 'boolean',
            'is_featured' => 'boolean',
        ]);

        // Generate slug
        $validated['slug'] = Str::slug($validated['name']);

        // Create the agency
        $agency = Agency::create($validated);

        return redirect()->route('admin.agencies')->with('success', 'Agency created successfully.');
    }

    /**
     * Show the form for editing the specified agency.
     */
    public function edit($id)
    {
        $agency = Agency::with('user')->findOrFail($id);
        $users = User::where('user_type', 'agency')
            ->where(function($query) use ($agency) {
                $query->whereDoesntHave('agency')
                    ->orWhereHas('agency', function($q) use ($agency) {
                        $q->where('id', $agency->id);
                    });
            })
            ->orderBy('username')
            ->get();

        return view('admin.agencies.edit', compact('agency', 'users'));
    }

    /**
     * Update the specified agency in storage.
     */
    public function update(Request $request, $id)
    {
        $agency = Agency::findOrFail($id);

        $validated = $request->validate([
            'user_id' => 'nullable|exists:users,id',
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'website' => 'nullable|url|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string|max:255',
            'is_verified' => 'boolean',
            'is_premium' => 'boolean',
            'is_featured' => 'boolean',
        ]);

        // Generate slug if name changed
        if ($agency->name !== $validated['name']) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        $agency->update($validated);

        return redirect()->route('admin.agencies')->with('success', 'Agency updated successfully.');
    }

    /**
     * Remove the specified agency from storage.
     */
    public function destroy($id)
    {
        $agency = Agency::findOrFail($id);
        $agency->delete();

        return redirect()->route('admin.agencies')->with('success', 'Agency deleted successfully.');
    }
}
