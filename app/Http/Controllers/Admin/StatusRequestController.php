<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\EscortStatusRequest;
use App\Models\Escort;
use App\Services\NotificationService;
use Carbon\Carbon;

class StatusRequestController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Display a listing of all status requests.
     */
    public function index(Request $request)
    {
        $query = EscortStatusRequest::with('escort');

        // Apply filters
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        if ($request->has('request_type') && $request->request_type) {
            $query->where('request_type', $request->request_type);
        }

        $statusRequests = $query->latest()->paginate(15);

        return view('admin.status-requests.index', compact('statusRequests'));
    }

    /**
     * Display the specified status request.
     */
    public function show($id)
    {
        $statusRequest = EscortStatusRequest::with('escort.user')->findOrFail($id);

        // Check if the escort and user relationships exist
        if (!$statusRequest->escort) {
            return redirect()->route('admin.all-requests.index')
                ->with('error', 'The escort associated with this request no longer exists.');
        }

        if (!$statusRequest->escort->user) {
            return redirect()->route('admin.all-requests.index')
                ->with('error', 'The user account associated with this escort no longer exists.');
        }

        return view('admin.status-requests.show', compact('statusRequest'));
    }

    /**
     * Approve the specified status request.
     */
    public function approve(Request $request, $id)
    {
        $statusRequest = EscortStatusRequest::with('escort')->findOrFail($id);

        // Only approve pending requests
        if ($statusRequest->status !== 'pending') {
            return redirect()->back()->withErrors([
                'status' => 'This request has already been ' . $statusRequest->status . '.'
            ]);
        }

        // Update the status request
        $statusRequest->status = 'approved';
        $statusRequest->admin_message = $request->admin_message;
        $statusRequest->approved_at = now();

        // Set expiration date based on duration
        if ($statusRequest->duration) {
            switch ($statusRequest->duration) {
                case 'day':
                    $statusRequest->expires_at = now()->addDay();
                    break;
                case 'week':
                    $statusRequest->expires_at = now()->addWeek();
                    break;
                case 'month':
                    $statusRequest->expires_at = now()->addMonth();
                    break;
                case 'annual':
                    $statusRequest->expires_at = now()->addYear();
                    break;
            }
        }

        $statusRequest->save();

        // Update the escort's status
        $escort = $statusRequest->escort;

        if ($statusRequest->request_type === 'verification') {
            $escort->is_verified = true;
        } elseif ($statusRequest->request_type === 'featured') {
            $escort->is_featured = true;
            // Set featured expiration if duration is specified
            if ($statusRequest->expires_at) {
                $escort->featured_expires_at = $statusRequest->expires_at;
            }
        }

        $escort->save();

        // Notify the escort
        $this->notificationService->create(
            $escort->user,
            'status_request_approved',
            ucfirst($statusRequest->request_type) . ' Request Approved',
            'Your request to be ' . $statusRequest->request_type . ' has been approved.',
            [
                'request_id' => $statusRequest->id,
                'request_type' => $statusRequest->request_type,
                'admin_message' => $statusRequest->admin_message,
                'expires_at' => $statusRequest->expires_at ? $statusRequest->expires_at->format('Y-m-d H:i:s') : null
            ]
        );

        return redirect()->route('admin.status-requests.index')->with('success', 'Request approved successfully.');
    }

    /**
     * Reject the specified status request.
     */
    public function reject(Request $request, $id)
    {
        $statusRequest = EscortStatusRequest::with('escort')->findOrFail($id);

        // Only reject pending requests
        if ($statusRequest->status !== 'pending') {
            return redirect()->back()->withErrors([
                'status' => 'This request has already been ' . $statusRequest->status . '.'
            ]);
        }

        // Update the status request
        $statusRequest->status = 'rejected';
        $statusRequest->admin_message = $request->admin_message;
        $statusRequest->save();

        // Notify the escort
        $this->notificationService->create(
            $statusRequest->escort->user,
            'status_request_rejected',
            ucfirst($statusRequest->request_type) . ' Request Rejected',
            'Your request to be ' . $statusRequest->request_type . ' has been rejected.',
            [
                'request_id' => $statusRequest->id,
                'request_type' => $statusRequest->request_type,
                'admin_message' => $statusRequest->admin_message
            ]
        );

        return redirect()->route('admin.status-requests.index')->with('success', 'Request rejected successfully.');
    }
}
