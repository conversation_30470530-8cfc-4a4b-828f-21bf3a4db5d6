<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\StatusPricing;

class StatusPricingController extends Controller
{
    /**
     * Display a listing of the status pricing.
     */
    public function index()
    {
        $verificationPricing = StatusPricing::verification()->orderBy('duration')->get();
        $featuredPricing = StatusPricing::featured()->orderBy('duration')->get();
        $agencyApprovalPricing = StatusPricing::agencyApproval()->orderBy('duration')->get();
        $agencyFeaturedPricing = StatusPricing::agencyFeatured()->orderBy('duration')->get();

        return view('admin.status-pricing.index', compact(
            'verificationPricing',
            'featuredPricing',
            'agencyApprovalPricing',
            'agencyFeaturedPricing'
        ));
    }

    /**
     * Show the form for creating a new status pricing.
     */
    public function create()
    {
        return view('admin.status-pricing.create');
    }

    /**
     * Store a newly created status pricing in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'request_type' => 'required|in:verification,featured,agency_approval,agency_featured',
            'duration' => 'required|in:day,week,month,annual',
            'price' => 'required|numeric|min:0',
        ]);

        StatusPricing::create([
            'request_type' => $request->request_type,
            'duration' => $request->duration,
            'price' => $request->price,
            'is_active' => true,
        ]);

        return redirect()->route('admin.status-pricing.index')->with('success', 'Pricing added successfully.');
    }

    /**
     * Show the form for editing the specified status pricing.
     */
    public function edit($id)
    {
        $pricing = StatusPricing::findOrFail($id);
        return view('admin.status-pricing.edit', compact('pricing'));
    }

    /**
     * Update the specified status pricing in storage.
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'price' => 'required|numeric|min:0',
            'is_active' => 'boolean',
        ]);

        $pricing = StatusPricing::findOrFail($id);
        $pricing->price = $request->price;
        $pricing->is_active = $request->has('is_active');
        $pricing->save();

        return redirect()->route('admin.status-pricing.index')->with('success', 'Pricing updated successfully.');
    }

    /**
     * Remove the specified status pricing from storage.
     */
    public function destroy($id)
    {
        $pricing = StatusPricing::findOrFail($id);
        $pricing->delete();

        return redirect()->route('admin.status-pricing.index')->with('success', 'Pricing deleted successfully.');
    }
}
