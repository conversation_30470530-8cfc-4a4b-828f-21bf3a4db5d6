<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Escort;
use App\Models\User;
use App\Models\Agency;
use Illuminate\Support\Str;

class EscortController extends Controller
{
    /**
     * Display a listing of all escorts.
     */
    public function index(Request $request)
    {
        $query = Escort::with(['user', 'agency']);

        // Apply filters
        if ($request->has('gender') && $request->gender) {
            $query->where('gender', $request->gender);
        }

        if ($request->has('is_verified') && $request->is_verified !== null) {
            $query->where('is_verified', $request->is_verified);
        }

        if ($request->has('is_featured') && $request->is_featured !== null) {
            $query->where('is_featured', $request->is_featured);
        }

        if ($request->has('agency_id') && $request->agency_id) {
            $query->where('agency_id', $request->agency_id);
        }

        if ($request->has('user_active') && $request->user_active !== null) {
            $query->whereHas('user', function($q) use ($request) {
                $q->where('is_active', $request->user_active);
            });
        }

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhereHas('user', function($uq) use ($search) {
                      $uq->where('username', 'like', "%{$search}%")
                         ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        $escorts = $query->latest()->paginate(20);
        $agencies = Agency::orderBy('name')->get();

        return view('admin.escorts.index', compact('escorts', 'agencies'));
    }

    /**
     * Show the form for creating a new escort.
     */
    public function create()
    {
        $agencies = Agency::orderBy('name')->get();
        return view('admin.escorts.create', compact('agencies'));
    }

    /**
     * Store a newly created escort in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'nullable|exists:users,id',
            'agency_id' => 'nullable|exists:agencies,id',
            'name' => 'required|string|max:255',
            'gender' => 'required|in:female,male,couple,gay,transsexual',
            'date_of_birth' => 'required|date|before:18 years ago',
            'ethnicity' => 'required|string',
            'hair_color' => 'required|string',
            'hair_length' => 'required|string',
            'bust_size' => 'nullable|string',
            'height_cm' => 'required|integer|min:140|max:220',
            'weight_kg' => 'required|integer|min:40|max:150',
            'build' => 'required|string',
            'looks' => 'required|string',
            'about' => 'required|string|min:50',
            'is_verified' => 'boolean',
            'is_featured' => 'boolean',
            'is_premium' => 'boolean',
            'is_independent' => 'boolean',
        ]);

        // Generate slug
        $validated['slug'] = Str::slug($validated['name']);

        // Set is_independent based on agency_id
        $validated['is_independent'] = empty($validated['agency_id']);

        $escort = Escort::create($validated);

        return redirect()->route('admin.escorts')->with('success', 'Escort created successfully.');
    }

    /**
     * Show the form for editing the specified escort.
     */
    public function edit($id)
    {
        $escort = Escort::with(['user', 'agency'])->findOrFail($id);
        $agencies = Agency::orderBy('name')->get();
        $users = User::where('user_type', 'escort')->orderBy('username')->get();

        return view('admin.escorts.edit', compact('escort', 'agencies', 'users'));
    }

    /**
     * Update the specified escort in storage.
     */
    public function update(Request $request, $id)
    {
        $escort = Escort::findOrFail($id);

        $validated = $request->validate([
            'user_id' => 'nullable|exists:users,id',
            'agency_id' => 'nullable|exists:agencies,id',
            'name' => 'required|string|max:255',
            'gender' => 'required|in:female,male,couple,gay,transsexual',
            'date_of_birth' => 'required|date|before:18 years ago',
            'ethnicity' => 'required|string',
            'hair_color' => 'required|string',
            'hair_length' => 'required|string',
            'bust_size' => 'nullable|string',
            'height_cm' => 'required|integer|min:140|max:220',
            'weight_kg' => 'required|integer|min:40|max:150',
            'build' => 'required|string',
            'looks' => 'required|string',
            'about' => 'required|string|min:50',
            'is_verified' => 'boolean',
            'is_featured' => 'boolean',
            'is_premium' => 'boolean',
            'is_independent' => 'boolean',
        ]);

        // Generate slug if name changed
        if ($escort->name !== $validated['name']) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        // Set is_independent based on agency_id
        $validated['is_independent'] = empty($validated['agency_id']);

        $escort->update($validated);

        return redirect()->route('admin.escorts')->with('success', 'Escort updated successfully.');
    }

    /**
     * Remove the specified escort from storage.
     */
    public function destroy($id)
    {
        $escort = Escort::findOrFail($id);
        $escort->delete();

        return redirect()->route('admin.escorts')->with('success', 'Escort deleted successfully.');
    }
}
