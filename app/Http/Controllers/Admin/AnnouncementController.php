<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\AdminNotificationService;

class AnnouncementController extends Controller
{
    /**
     * The admin notification service instance.
     */
    protected $adminNotificationService;

    /**
     * Create a new controller instance.
     *
     * @param AdminNotificationService $adminNotificationService
     * @return void
     */
    public function __construct(AdminNotificationService $adminNotificationService)
    {
        $this->adminNotificationService = $adminNotificationService;
    }

    /**
     * Show the form for creating a new announcement.
     */
    public function create()
    {
        return view('admin.announcements.create');
    }

    /**
     * Store a newly created announcement in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'user_types' => 'nullable|array',
            'user_types.*' => 'in:admin,escort,agency,regular',
        ]);

        // Create the announcement for all users or specific user types
        if (empty($validated['user_types'])) {
            // Send to all users
            $this->adminNotificationService->createAnnouncement(
                $validated['title'],
                $validated['message']
            );
        } else {
            // Send to specific user types
            $this->adminNotificationService->notifyUserTypes(
                $validated['user_types'],
                'announcement',
                '',
                $validated['title'],
                ['message' => $validated['message']]
            );
        }

        return redirect()->route('dashboard')->with('success', 'Announcement created and sent successfully.');
    }
}
