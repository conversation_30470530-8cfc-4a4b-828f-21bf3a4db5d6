<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class SettingController extends Controller
{
    /**
     * Display a listing of the settings.
     */
    public function index()
    {
        $generalSettings = Setting::where('group', 'general')->orderBy('key')->get();
        $contactSettings = Setting::where('group', 'contact')->orderBy('key')->get();
        $footerSettings = Setting::where('group', 'footer')->orderBy('key')->get();
        $socialSettings = Setting::where('group', 'social')->orderBy('key')->get();
        
        return view('admin.settings.index', compact(
            'generalSettings',
            'contactSettings',
            'footerSettings',
            'socialSettings'
        ));
    }

    /**
     * Update the specified setting in storage.
     */
    public function update(Request $request)
    {
        $validated = $request->validate([
            'settings' => 'required|array',
            'settings.*' => 'nullable|string',
        ]);

        foreach ($validated['settings'] as $key => $value) {
            $setting = Setting::where('key', $key)->first();
            
            if ($setting) {
                $setting->value = $value;
                $setting->save();
                
                // Clear the cache for this setting
                Cache::forget('setting_' . $key);
            }
        }

        return redirect()->route('admin.settings')->with('success', 'Settings updated successfully.');
    }
}
