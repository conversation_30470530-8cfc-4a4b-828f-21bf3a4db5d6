<?php

namespace App\Http\Controllers;

use App\Services\CityGuideService;
use App\Services\SafetyContentService;
use App\Services\ServiceContentService;
use App\Services\CompetitiveAnalysisService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ContentGenerationController extends Controller
{
    protected $cityGuideService;
    protected $safetyContentService;
    protected $serviceContentService;
    protected $competitiveAnalysisService;

    public function __construct(
        CityGuideService $cityGuideService,
        SafetyContentService $safetyContentService,
        ServiceContentService $serviceContentService,
        CompetitiveAnalysisService $competitiveAnalysisService
    ) {
        $this->cityGuideService = $cityGuideService;
        $this->safetyContentService = $safetyContentService;
        $this->serviceContentService = $serviceContentService;
        $this->competitiveAnalysisService = $competitiveAnalysisService;
    }

    /**
     * Dashboard for content generation overview
     */
    public function dashboard()
    {
        $contentStats = [
            'city_guides' => [
                'total_planned' => 20,
                'generated' => 0,
                'priority_cities' => $this->cityGuideService->getPriorityCities(),
            ],
            'safety_pages' => [
                'total_planned' => 50,
                'generated' => 0,
                'content_structure' => $this->safetyContentService->getSafetyContentStructure(),
            ],
            'service_pages' => [
                'total_planned' => 100,
                'generated' => 0,
                'high_priority' => $this->serviceContentService->getHighPriorityServicePages(),
            ],
            'competitive_analysis' => $this->competitiveAnalysisService->getCompetitorAnalysis(),
        ];

        return view('admin.content-generation.dashboard', compact('contentStats'));
    }

    /**
     * Generate Week 1-4: City Guides
     */
    public function generateCityGuides(): JsonResponse
    {
        try {
            $priorityCities = $this->cityGuideService->getPriorityCities();
            $generated = [];

            foreach ($priorityCities as $tier => $cities) {
                foreach ($cities as $cityName => $cityData) {
                    // Create location if it doesn't exist
                    $location = \App\Models\Location::firstOrCreate(
                        ['name' => $cityName],
                        [
                            'slug' => \Str::slug($cityName),
                            'type' => 'city',
                            'is_active' => true,
                            'description' => "Professional escort services in {$cityName}, Uganda",
                        ]
                    );

                    // Generate city guide content
                    $cityGuide = $this->cityGuideService->generateCityGuide($location);

                    $generated[] = [
                        'city' => $cityName,
                        'tier' => $tier,
                        'priority' => $cityData['priority'],
                        'location_id' => $location->id,
                        'guide_url' => route('locations.show', $location->slug),
                        'seo_data' => $cityGuide['meta_data'],
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'City guides generated successfully',
                'data' => [
                    'generated' => $generated,
                    'total' => count($generated),
                    'breakdown' => [
                        'tier_1' => count($priorityCities['tier_1']),
                        'tier_2' => count($priorityCities['tier_2']),
                        'tier_3' => count($priorityCities['tier_3']),
                        'tier_4' => count($priorityCities['tier_4']),
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate city guides: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate Week 5-8: Safety & Verification Pages
     */
    public function generateSafetyPages(): JsonResponse
    {
        try {
            $safetyPages = $this->safetyContentService->generateAllSafetyPages();
            $contentCalendar = $this->safetyContentService->getSafetyContentCalendar();

            // Here you would typically save these to a content management system
            // For now, we'll return the generated structure

            return response()->json([
                'success' => true,
                'message' => 'Safety pages generated successfully',
                'data' => [
                    'total_pages' => count($safetyPages),
                    'pages' => $safetyPages,
                    'content_calendar' => $contentCalendar,
                    'categories' => [
                        'client_safety' => 10,
                        'escort_safety' => 10,
                        'verification_system' => 10,
                        'legal_compliance' => 10,
                        'platform_safety' => 10,
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate safety pages: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate Week 9-12: Service-Specific Pages
     */
    public function generateServicePages(): JsonResponse
    {
        try {
            $servicePages = $this->serviceContentService->generateAllServicePages();
            $contentCalendar = $this->serviceContentService->getServiceContentCalendar();
            $highPriorityPages = $this->serviceContentService->getHighPriorityServicePages();

            return response()->json([
                'success' => true,
                'message' => 'Service pages generated successfully',
                'data' => [
                    'total_pages' => count($servicePages),
                    'high_priority_pages' => count($highPriorityPages),
                    'pages' => $servicePages,
                    'content_calendar' => $contentCalendar,
                    'breakdown' => [
                        'service_pages' => count(array_filter($servicePages, fn($p) => $p['type'] === 'service')),
                        'service_location_pages' => count(array_filter($servicePages, fn($p) => $p['type'] === 'service_location')),
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate service pages: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate all content (12-week plan)
     */
    public function generateAllContent(): JsonResponse
    {
        try {
            $results = [
                'city_guides' => $this->generateCityGuides()->getData(),
                'safety_pages' => $this->generateSafetyPages()->getData(),
                'service_pages' => $this->generateServicePages()->getData(),
            ];

            $totalPages = 
                $results['city_guides']->data->total +
                $results['safety_pages']->data->total_pages +
                $results['service_pages']->data->total_pages;

            return response()->json([
                'success' => true,
                'message' => '12-week content plan generated successfully',
                'data' => [
                    'total_pages' => $totalPages,
                    'breakdown' => $results,
                    'timeline' => [
                        'weeks_1_4' => 'City Guides (' . $results['city_guides']->data->total . ' pages)',
                        'weeks_5_8' => 'Safety Pages (' . $results['safety_pages']->data->total_pages . ' pages)',
                        'weeks_9_12' => 'Service Pages (' . $results['service_pages']->data->total_pages . ' pages)',
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate all content: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get competitive analysis and recommendations
     */
    public function getCompetitiveAnalysis(): JsonResponse
    {
        try {
            $analysis = $this->competitiveAnalysisService->getCompetitorAnalysis();
            $timeline = $this->competitiveAnalysisService->getRankingTimeline();
            $actions = $this->competitiveAnalysisService->getImmediateActions();

            return response()->json([
                'success' => true,
                'data' => [
                    'competitive_analysis' => $analysis,
                    'ranking_timeline' => $timeline,
                    'immediate_actions' => $actions,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get competitive analysis: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get content generation progress
     */
    public function getProgress(): JsonResponse
    {
        // This would typically check database for actual progress
        // For now, return mock progress data

        return response()->json([
            'success' => true,
            'data' => [
                'overall_progress' => 0,
                'phases' => [
                    'city_guides' => [
                        'status' => 'pending',
                        'progress' => 0,
                        'total_pages' => 20,
                        'completed_pages' => 0,
                        'timeline' => 'Weeks 1-4',
                    ],
                    'safety_pages' => [
                        'status' => 'pending',
                        'progress' => 0,
                        'total_pages' => 50,
                        'completed_pages' => 0,
                        'timeline' => 'Weeks 5-8',
                    ],
                    'service_pages' => [
                        'status' => 'pending',
                        'progress' => 0,
                        'total_pages' => 100,
                        'completed_pages' => 0,
                        'timeline' => 'Weeks 9-12',
                    ],
                ],
                'next_actions' => [
                    'Start with high-priority city guides (Kampala, Entebbe, Jinja)',
                    'Focus on safety verification content',
                    'Create service-location combinations',
                    'Implement internal linking strategy',
                ],
            ],
        ]);
    }

    /**
     * Export content plan as downloadable file
     */
    public function exportContentPlan()
    {
        try {
            $contentPlan = [
                'overview' => [
                    'total_pages' => 170,
                    'timeline' => '12 weeks',
                    'target' => 'Uganda escort market domination',
                ],
                'phases' => [
                    'phase_1' => [
                        'title' => 'City Guides (Weeks 1-4)',
                        'pages' => 20,
                        'cities' => $this->cityGuideService->getPriorityCities(),
                    ],
                    'phase_2' => [
                        'title' => 'Safety & Verification (Weeks 5-8)',
                        'pages' => 50,
                        'structure' => $this->safetyContentService->getSafetyContentStructure(),
                    ],
                    'phase_3' => [
                        'title' => 'Service Pages (Weeks 9-12)',
                        'pages' => 100,
                        'services' => $this->serviceContentService->getServiceContentStructure(),
                    ],
                ],
                'competitive_strategy' => $this->competitiveAnalysisService->getCompetitorAnalysis(),
            ];

            $filename = 'seo-content-plan-' . date('Y-m-d') . '.json';
            
            return response()->json($contentPlan)
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Content-Type', 'application/json');

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export content plan: ' . $e->getMessage(),
            ], 500);
        }
    }
}
