<?php

namespace App\Http\Controllers;

use App\Models\Location;
use App\Services\CityGuideService;
use App\Services\SeoService;
use Illuminate\Http\Request;

class CityGuideController extends Controller
{
    protected $cityGuideService;
    protected $seoService;

    public function __construct(CityGuideService $cityGuideService, SeoService $seoService)
    {
        $this->cityGuideService = $cityGuideService;
        $this->seoService = $seoService;
    }

    /**
     * Display city guides index
     */
    public function index()
    {
        $priorityCities = $this->cityGuideService->getPriorityCities();
        
        $seoMeta = [
            'title' => 'City Guides - Professional Escort Services Across Uganda',
            'description' => 'Comprehensive guides to escort services in major Ugandan cities. Find verified escorts in Kampala, Entebbe, Jinja, Mbarara and more.',
            'keywords' => 'Uganda city guides, escort services by city, Kampala escorts, Entebbe escorts, Jinja escorts',
        ];

        return view('city-guides.index', compact('priorityCities', 'seoMeta'));
    }

    /**
     * Display specific city guide
     */
    public function show(Location $location)
    {
        // Generate comprehensive city guide content
        $cityGuide = $this->cityGuideService->generateCityGuide($location);
        
        // Generate SEO meta data
        $seoMeta = $cityGuide['meta_data'];
        
        // Generate structured data
        $structuredData = $this->seoService->generateLocationStructuredData($location);
        
        return view('city-guides.show', compact('location', 'cityGuide', 'seoMeta', 'structuredData'));
    }

    /**
     * Generate all city guides (admin function)
     */
    public function generateAll()
    {
        $priorityCities = $this->cityGuideService->getPriorityCities();
        $generated = [];

        foreach ($priorityCities as $tier => $cities) {
            foreach ($cities as $cityName => $cityData) {
                $location = Location::where('name', $cityName)->first();
                
                if (!$location) {
                    // Create location if it doesn't exist
                    $location = Location::create([
                        'name' => $cityName,
                        'slug' => \Str::slug($cityName),
                        'type' => 'city',
                        'is_active' => true,
                        'description' => "Professional escort services in {$cityName}, Uganda",
                    ]);
                }

                $generated[] = [
                    'city' => $cityName,
                    'tier' => $tier,
                    'location_id' => $location->id,
                    'guide_url' => route('city-guides.show', $location->slug),
                ];
            }
        }

        return response()->json([
            'message' => 'City guides generated successfully',
            'generated' => $generated,
            'total' => count($generated),
        ]);
    }
}
