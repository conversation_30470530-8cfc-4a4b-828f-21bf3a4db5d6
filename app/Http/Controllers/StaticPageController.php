<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\SeoService;

class StaticPageController extends Controller
{
    protected $seoService;

    public function __construct(SeoService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Show the about page
     */
    public function about()
    {
        $seoMeta = [
            'title' => 'About Get Hot Babes | Premier Escort Platform in Uganda',
            'description' => 'Learn about Get Hot Babes, Uganda\'s premier escort platform connecting discerning clients with professional companions. Our mission, values, and commitment to safety.',
            'keywords' => 'about get hot babes, escort platform Uganda, professional companions, escort services Uganda, about us',
            'type' => 'website'
        ];

        return view('static.about', compact('seoMeta'));
    }

    /**
     * Show the safety guidelines page
     */
    public function safety()
    {
        $seoMeta = [
            'title' => 'Safety Guidelines | Secure Escort Services in Uganda',
            'description' => 'Comprehensive safety guidelines for clients and escorts. Learn about verification, secure meetings, and best practices for safe escort services in Uganda.',
            'keywords' => 'escort safety Uganda, safe escort services, verification process, meeting safety, escort guidelines',
            'type' => 'website'
        ];

        return view('static.safety', compact('seoMeta'));
    }

    /**
     * Show the help center page
     */
    public function help()
    {
        $seoMeta = [
            'title' => 'Help Center | Get Hot Babes Support & FAQ',
            'description' => 'Find answers to frequently asked questions about our escort platform. Get help with registration, verification, bookings, and more.',
            'keywords' => 'escort platform help, FAQ escorts Uganda, support get hot babes, escort booking help',
            'type' => 'website'
        ];

        return view('static.help', compact('seoMeta'));
    }

    /**
     * Show the verification process page
     */
    public function verification()
    {
        $seoMeta = [
            'title' => 'Verification Process | Verified Escorts in Uganda',
            'description' => 'Learn about our comprehensive verification process for escorts and agencies. Ensuring authenticity, safety, and quality on our platform.',
            'keywords' => 'escort verification Uganda, verified escorts, verification process, authentic escorts Uganda',
            'type' => 'website'
        ];

        return view('static.verification', compact('seoMeta'));
    }

    /**
     * Show the booking tips page
     */
    public function bookingTips()
    {
        $seoMeta = [
            'title' => 'Booking Tips | How to Book Escorts in Uganda Safely',
            'description' => 'Essential tips for booking escort services safely and professionally. Learn best practices for communication, meetings, and etiquette.',
            'keywords' => 'escort booking tips Uganda, how to book escorts, escort etiquette, professional escort services',
            'type' => 'website'
        ];

        return view('static.booking-tips', compact('seoMeta'));
    }

    /**
     * Show the services overview page
     */
    public function services()
    {
        $seoMeta = [
            'title' => 'Escort Services in Uganda | Professional Companion Services',
            'description' => 'Explore the range of professional escort and companion services available in Uganda. From dinner dates to travel companions and more.',
            'keywords' => 'escort services Uganda, companion services, dinner dates Uganda, travel companions, professional escorts',
            'type' => 'website'
        ];

        // Get popular services
        try {
            $services = \App\Models\Service::whereHas('escorts')
                ->withCount('escorts')
                ->orderBy('escorts_count', 'desc')
                ->get();
        } catch (\Exception $e) {
            // Fallback data if Service model or database issues
            $services = collect([
                (object)['name' => 'Dinner Dates', 'description' => 'Elegant dinner companions for special occasions'],
                (object)['name' => 'Travel Companions', 'description' => 'Professional travel companions for business or leisure'],
                (object)['name' => 'Event Escorts', 'description' => 'Sophisticated companions for social events'],
                (object)['name' => 'Business Meetings', 'description' => 'Professional companions for business functions'],
                (object)['name' => 'City Tours', 'description' => 'Knowledgeable local guides and companions'],
                (object)['name' => 'Private Parties', 'description' => 'Elegant companions for private gatherings'],
            ]);
        }

        return view('static.services', compact('seoMeta', 'services'));
    }

    /**
     * Show the blog/news page
     */
    public function blog()
    {
        $seoMeta = [
            'title' => 'Blog & News | Get Hot Babes Uganda Escort Platform',
            'description' => 'Stay updated with the latest news, tips, and insights from Uganda\'s premier escort platform. Industry updates and helpful guides.',
            'keywords' => 'escort blog Uganda, escort news, companion tips, escort industry Uganda',
            'type' => 'website'
        ];

        return view('static.blog', compact('seoMeta'));
    }

    /**
     * Show the city guides page
     */
    public function cityGuides()
    {
        $seoMeta = [
            'title' => 'City Guides | Escort Services by Location in Uganda',
            'description' => 'Comprehensive city guides for escort services across Uganda. Discover the best locations, venues, and local insights for each city.',
            'keywords' => 'Uganda city guides, escort locations, Kampala guide, Entebbe guide, city escort services',
            'type' => 'website'
        ];

        // Get popular locations
        try {
            $locations = \App\Models\Location::active()
                ->ofType('city')
                ->whereHas('escorts')
                ->withCount('escorts')
                ->orderBy('escorts_count', 'desc')
                ->take(12)
                ->get();
        } catch (\Exception $e) {
            $locations = collect([]);
        }

        return view('static.city-guides', compact('seoMeta', 'locations'));
    }
}
