<?php

namespace App\Http\Controllers;

use App\Services\PerformanceService;
use Illuminate\Http\Response;

class ServiceWorkerController extends Controller
{
    protected $performanceService;

    public function __construct(PerformanceService $performanceService)
    {
        $this->performanceService = $performanceService;
    }

    /**
     * Generate and serve the service worker
     */
    public function serviceWorker()
    {
        $serviceWorkerContent = $this->performanceService->generateServiceWorker();

        return response($serviceWorkerContent)
            ->header('Content-Type', 'application/javascript')
            ->header('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour
    }

    /**
     * Generate and serve the web app manifest
     */
    public function manifest()
    {
        $manifest = [
            'name' => 'Get Hot Babes - Professional Escorts Uganda',
            'short_name' => 'Get Hot Babes',
            'description' => 'Find verified professional escorts and companions in Uganda',
            'start_url' => '/',
            'display' => 'standalone',
            'background_color' => '#ffffff',
            'theme_color' => '#ec4899',
            'orientation' => 'portrait-primary',
            'scope' => '/',
            'lang' => 'en-UG',
            'categories' => ['lifestyle', 'social'],
            'icons' => [
                [
                    'src' => asset('images/icons/icon-72x72.png'),
                    'sizes' => '72x72',
                    'type' => 'image/png',
                    'purpose' => 'maskable any'
                ],
                [
                    'src' => asset('images/icons/icon-96x96.png'),
                    'sizes' => '96x96',
                    'type' => 'image/png',
                    'purpose' => 'maskable any'
                ],
                [
                    'src' => asset('images/icons/icon-128x128.png'),
                    'sizes' => '128x128',
                    'type' => 'image/png',
                    'purpose' => 'maskable any'
                ],
                [
                    'src' => asset('images/icons/icon-144x144.png'),
                    'sizes' => '144x144',
                    'type' => 'image/png',
                    'purpose' => 'maskable any'
                ],
                [
                    'src' => asset('images/icons/icon-152x152.png'),
                    'sizes' => '152x152',
                    'type' => 'image/png',
                    'purpose' => 'maskable any'
                ],
                [
                    'src' => asset('images/icons/icon-192x192.png'),
                    'sizes' => '192x192',
                    'type' => 'image/png',
                    'purpose' => 'maskable any'
                ],
                [
                    'src' => asset('images/icons/icon-384x384.png'),
                    'sizes' => '384x384',
                    'type' => 'image/png',
                    'purpose' => 'maskable any'
                ],
                [
                    'src' => asset('images/icons/icon-512x512.png'),
                    'sizes' => '512x512',
                    'type' => 'image/png',
                    'purpose' => 'maskable any'
                ]
            ],
            'shortcuts' => [
                [
                    'name' => 'Browse Escorts',
                    'short_name' => 'Escorts',
                    'description' => 'Browse verified escorts',
                    'url' => '/escorts',
                    'icons' => [
                        [
                            'src' => asset('images/icons/shortcut-escorts.png'),
                            'sizes' => '96x96'
                        ]
                    ]
                ],
                [
                    'name' => 'Find Agencies',
                    'short_name' => 'Agencies',
                    'description' => 'Find escort agencies',
                    'url' => '/agencies',
                    'icons' => [
                        [
                            'src' => asset('images/icons/shortcut-agencies.png'),
                            'sizes' => '96x96'
                        ]
                    ]
                ],
                [
                    'name' => 'Locations',
                    'short_name' => 'Locations',
                    'description' => 'Browse by location',
                    'url' => '/locations',
                    'icons' => [
                        [
                            'src' => asset('images/icons/shortcut-locations.png'),
                            'sizes' => '96x96'
                        ]
                    ]
                ]
            ]
        ];

        return response()->json($manifest)
            ->header('Cache-Control', 'public, max-age=86400'); // Cache for 24 hours
    }

    /**
     * Generate offline page
     */
    public function offline()
    {
        return view('offline');
    }
}
