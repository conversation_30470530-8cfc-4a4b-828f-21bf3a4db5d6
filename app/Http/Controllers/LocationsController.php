<?php

namespace App\Http\Controllers;

use App\Models\Location;
use App\Models\Escort;
use App\Services\SeoService;
use Illuminate\Http\Request;

class LocationsController extends Controller
{
    protected $seoService;

    public function __construct(SeoService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Display a listing of locations.
     */
    public function index(Request $request)
    {
        try {
            $search = $request->get('search');

            if ($search) {
                // Enhanced search functionality
                $countriesWithCities = $this->searchLocations($search);

                // Debug: Log search results
                \Log::info('Location search results', [
                    'search' => $search,
                    'results_count' => count($countriesWithCities)
                ]);

                // Generate SEO meta data for search
                $totalLocations = count($countriesWithCities);
                $totalCities = collect($countriesWithCities)->sum(function($country) {
                    return count($country['cities']);
                });
                $totalEscorts = collect($countriesWithCities)->sum(function($country) {
                    return collect($country['cities'])->sum('escort_count');
                });

                $seoMeta = [
                    'title' => "Search Results for '{$search}' - Escort Locations",
                    'description' => "Find escorts in {$search} and surrounding areas.",
                    'keywords' => "escorts {$search}, {$search} escorts, escort services {$search}",
                    'type' => 'website',
                    'canonical' => route('locations.index', ['search' => $search]),
                    'image' => asset('images/locations-og.jpg')
                ];
                $structuredData = [];
                $countries = collect(); // Empty collection for search results

                return view('locations.index', compact('countries', 'countriesWithCities', 'seoMeta', 'structuredData', 'totalLocations', 'totalCities', 'totalEscorts'));
            } else {
            // Get all countries with their cities and areas
            $countries = Location::active()
                ->ofType('country')
                ->orderBy('name')
                ->get();

            // Get cities for each country with their areas
            $countriesWithCities = [];
            foreach ($countries as $country) {
                $cities = Location::active()
                    ->ofType('city')
                    ->where('parent_id', $country->id)
                    ->orderBy('name')
                    ->get();

                $citiesWithAreas = [];
                foreach ($cities as $city) {
                    $areas = Location::active()
                        ->ofType('area')
                        ->where('parent_id', $city->id)
                        ->orderBy('name')
                        ->get();

                    $areaCount = Location::active()
                        ->ofType('area')
                        ->where('parent_id', $city->id)
                        ->count();

                    // Get escort count for this city (only active escorts)
                    $escortCount = Escort::whereHas('locations', function($query) use ($city) {
                        $query->where('location_id', $city->id);
                    })
                    ->verified()
                    ->whereHas('user', function ($q) {
                        $q->where('is_active', true);
                    })
                    ->count();

                    $citiesWithAreas[] = [
                        'city' => $city,
                        'areas' => $areas->take(6),
                        'area_count' => $areaCount,
                        'escort_count' => $escortCount
                    ];
                }

                // Only include countries that have cities with escorts
                if (!empty($citiesWithAreas)) {
                    $countriesWithCities[] = [
                        'country' => $country,
                        'cities' => $citiesWithAreas
                    ];
                }
            }

            // Generate comprehensive SEO meta data
            $totalLocations = count($countriesWithCities);
            $totalCities = collect($countriesWithCities)->sum(function($country) {
                return count($country['cities']);
            });
            $totalEscorts = collect($countriesWithCities)->sum(function($country) {
                return collect($country['cities'])->sum('escort_count');
            });

            if ($search) {
                $seoMeta = $this->seoService->getLocationIndexMeta($search);
            } else {
                $seoMeta = [
                    'title' => "Browse {$totalEscorts}+ Escorts by Location | {$totalCities} Cities in Uganda & East Africa",
                    'description' => "Find professional escorts across {$totalCities} cities in Uganda and East Africa. Browse {$totalEscorts}+ verified escorts in Kampala, Entebbe, Jinja, Mbarara and other major locations.",
                    'keywords' => 'escort locations Uganda, escorts by city, Kampala escorts, Entebbe escorts, Jinja escorts, Mbarara escorts, East Africa escort locations, Uganda cities escorts, verified escorts by location',
                    'type' => 'website',
                    'canonical' => route('locations.index'),
                    'image' => asset('images/locations-og.jpg')
                ];
            }

            // Generate structured data for locations directory
            $structuredData = [
                '@context' => 'https://schema.org',
                '@type' => 'CollectionPage',
                'name' => 'Escort Locations Directory',
                'description' => "Browse professional escort services across {$totalCities} cities in Uganda and East Africa",
                'url' => route('locations.index'),
                'mainEntity' => [
                    '@type' => 'ItemList',
                    'name' => 'Escort Service Locations',
                    'numberOfItems' => $totalCities,
                    'itemListElement' => collect($countriesWithCities)->flatMap(function($countryData, $index) {
                        return collect($countryData['cities'])->map(function($cityData, $cityIndex) use ($index) {
                            return [
                                '@type' => 'Place',
                                'position' => ($index * 100) + $cityIndex + 1,
                                'name' => $cityData['city']->name,
                                'url' => route('locations.show', $cityData['city']->slug),
                                'address' => [
                                    '@type' => 'PostalAddress',
                                    'addressLocality' => $cityData['city']->name,
                                    'addressCountry' => 'Uganda'
                                ],
                                'description' => "Professional escort services in {$cityData['city']->name} - {$cityData['escort_count']} verified escorts available"
                            ];
                        });
                    })->take(50)->values()->toArray()
                ]
            ];

            return view('locations.index', compact('countries', 'countriesWithCities', 'seoMeta', 'structuredData', 'totalLocations', 'totalCities', 'totalEscorts'));
            }
        } catch (\Exception $e) {
            // If there's an error (like table doesn't exist), show a placeholder page
            $countries = collect();
            $countriesWithCities = [];

            return view('locations.index', compact('countries', 'countriesWithCities'))
                ->with('error', 'Locations are currently being updated. Please check back soon.');
        }
    }

    /**
     * Display escorts for a specific location.
     */
    public function show($slug, Request $request)
    {
        try {
            $location = Location::active()
                ->where('slug', $slug)
                ->firstOrFail();

            // Get all child location IDs (including the current one)
            $locationIds = $this->getAllChildLocationIds($location);

        // Get escorts in this location and all child locations with filters
        $escortsQuery = Escort::whereHas('locations', function($query) use ($locationIds) {
                $query->whereIn('location_id', $locationIds);
            })
            ->verified()
            ->whereHas('user', function ($q) {
                $q->where('is_active', true);
            })
            ->with(['user', 'locations', 'rates', 'services', 'images']);

        // Apply filters if they exist
        if ($request->filled('gender')) {
            $escortsQuery->where('gender', $request->gender);
        }

        if ($request->filled('age')) {
            $ageRange = explode('-', $request->age);
            if (count($ageRange) == 2) {
                $escortsQuery->whereBetween('age', [$ageRange[0], $ageRange[1]]);
            } elseif (str_ends_with($request->age, '+')) {
                $minAge = (int) $request->age;
                $escortsQuery->where('age', '>=', $minAge);
            }
        }

        if ($request->filled('service')) {
            $escortsQuery->whereHas('services', function($query) use ($request) {
                $query->where('services.id', $request->service);
            });
        }

        // Apply sorting
        if ($request->filled('sort')) {
            switch ($request->sort) {
                case 'oldest':
                    $escortsQuery->oldest();
                    break;
                case 'price_low':
                    $escortsQuery->whereHas('rates', function($query) {
                        $query->orderBy('amount', 'asc');
                    });
                    break;
                case 'price_high':
                    $escortsQuery->whereHas('rates', function($query) {
                        $query->orderBy('amount', 'desc');
                    });
                    break;
                case 'rating':
                    $escortsQuery->orderBy('rating', 'desc');
                    break;
                default:
                    $escortsQuery->orderBy('is_verified', 'desc')->latest();
            }
        } else {
            $escortsQuery->orderBy('is_verified', 'desc')->latest();
        }

        $escorts = $escortsQuery->paginate(12)->appends($request->query());

        // Get child locations for filtering
        $childLocations = [];
        if ($location->type == 'country') {
            $childLocations = Location::active()
                ->ofType('city')
                ->where('parent_id', $location->id)
                ->orderBy('name')
                ->get();
        } elseif ($location->type == 'city') {
            $childLocations = Location::active()
                ->ofType('area')
                ->where('parent_id', $location->id)
                ->orderBy('name')
                ->get();
        }

        // Get popular services in this location
        $popularServices = \App\Models\Service::whereHas('escorts', function($query) use ($locationIds) {
                $query->whereHas('locations', function($locQuery) use ($locationIds) {
                    $locQuery->whereIn('location_id', $locationIds);
                });
            })
            ->withCount(['escorts' => function($query) use ($locationIds) {
                $query->whereHas('locations', function($locQuery) use ($locationIds) {
                    $locQuery->whereIn('location_id', $locationIds);
                });
            }])
            ->orderBy('escorts_count', 'desc')
            ->take(10)
            ->get();

        // Get upcoming tours to this location (placeholder - tours table doesn't exist yet)
        // $upcomingTours = \App\Models\Tour::where('location_id', $location->id)
        //     ->where('start_date', '>=', now())
        //     ->with('escort')
        //     ->orderBy('start_date')
        //     ->take(5)
        //     ->get();

        // Get related locations (siblings or nearby)
        $relatedLocations = [];
        if ($location->parent_id) {
            // Get sibling locations (same parent)
            $relatedLocations = Location::active()
                ->where('parent_id', $location->parent_id)
                ->where('id', '!=', $location->id)
                ->take(5)
                ->get();
        } else {
            // For countries, get other countries
            $relatedLocations = Location::active()
                ->ofType($location->type)
                ->where('id', '!=', $location->id)
                ->take(5)
                ->get();
        }

            // Get child locations for navigation
            $childLocations = Location::active()
                ->where('parent_id', $location->id)
                ->orderBy('name')
                ->get();

            // Get popular services (placeholder for now)
            $popularServices = collect([]);

            // Get upcoming tours (placeholder for now)
            $upcomingTours = collect([]);

            // Get related locations (same level or nearby)
            $relatedLocations = Location::active()
                ->where('parent_id', $location->parent_id)
                ->where('id', '!=', $location->id)
                ->orderBy('name')
                ->limit(6)
                ->get();

            // Generate SEO meta data and content
            $escortCount = $escorts->total();
            $seoMeta = $this->seoService->getLocationPageMeta($location);
            $seoContent = $this->seoService->generateLocationSeoContent($location, $escortCount);
            $faqData = $this->seoService->generateLocationFaqData($location);

            // Analytics data
            $analyticsPage = 'location';
            $analyticsEntity = $location;

            return view('locations.show', compact(
                'location',
                'escorts',
                'childLocations',
                'popularServices',
                'upcomingTours',
                'relatedLocations',
                'seoMeta',
                'seoContent',
                'faqData',
                'analyticsPage',
                'analyticsEntity'
            ));
        } catch (\Exception $e) {
            // Log the actual error for debugging
            \Log::error('Location show error: ' . $e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine());

            // If location doesn't exist or there's an error, redirect to locations index
            return redirect()->route('locations.index')
                ->with('error', 'The requested location could not be found.');
        }
    }

    /**
     * Get all child location IDs including the current location.
     */
    private function getAllChildLocationIds($location)
    {
        $ids = [$location->id];

        // If it's a country, include all cities and areas
        if ($location->type == 'country') {
            $cities = Location::active()
                ->ofType('city')
                ->where('parent_id', $location->id)
                ->get();

            foreach ($cities as $city) {
                $ids[] = $city->id;

                // Add areas within this city
                $areas = Location::active()
                    ->ofType('area')
                    ->where('parent_id', $city->id)
                    ->pluck('id')
                    ->toArray();

                $ids = array_merge($ids, $areas);
            }
        }
        // If it's a city, include all areas
        elseif ($location->type == 'city') {
            $areas = Location::active()
                ->ofType('area')
                ->where('parent_id', $location->id)
                ->pluck('id')
                ->toArray();

            $ids = array_merge($ids, $areas);
        }

        return $ids;
    }

    /**
     * Get locations for AJAX requests (used in forms).
     */
    public function getLocations(Request $request)
    {
        $type = $request->input('type', 'country');
        $parentId = $request->input('parent_id');

        $query = Location::active()->ofType($type);

        if ($parentId) {
            $query->where('parent_id', $parentId);
        } elseif ($type !== 'country') {
            // If no parent ID is provided and we're not looking for countries,
            // return an empty result
            return response()->json([]);
        }

        $locations = $query->orderBy('name')->get();

        return response()->json($locations);
    }

    /**
     * Enhanced search functionality for locations
     */
    private function searchLocations($search)
    {
        try {
            $searchTerm = strtolower(trim($search));

            \Log::info('Starting location search', ['search_term' => $searchTerm]);

            // Search in all location types with fuzzy matching
            $locations = Location::active()
                ->where(function($query) use ($searchTerm) {
                    $query->whereRaw('LOWER(name) LIKE ?', ["%{$searchTerm}%"])
                          ->orWhereRaw('LOWER(slug) LIKE ?', ["%{$searchTerm}%"]);
                })
                ->with(['parent', 'children'])
                ->orderByRaw("
                    CASE
                        WHEN LOWER(name) = ? THEN 1
                        WHEN LOWER(name) LIKE ? THEN 2
                        WHEN LOWER(slug) = ? THEN 3
                        WHEN LOWER(slug) LIKE ? THEN 4
                        ELSE 5
                    END
                ", [$searchTerm, "{$searchTerm}%", $searchTerm, "{$searchTerm}%"])
                ->orderBy('type')
                ->orderBy('name')
                ->get();

            \Log::info('Location search query completed', ['locations_found' => $locations->count()]);

        // Group results by country
        $countriesWithCities = [];
        $processedCities = [];

        foreach ($locations as $location) {
            $country = $this->getCountryForLocation($location);
            if (!$country) continue;

            $countryKey = $country->id;
            if (!isset($countriesWithCities[$countryKey])) {
                $countriesWithCities[$countryKey] = [
                    'country' => $country,
                    'cities' => []
                ];
            }

            if ($location->type === 'city') {
                $this->addCityToResults($countriesWithCities[$countryKey], $location, $processedCities);
            } elseif ($location->type === 'area') {
                $city = $location->parent;
                if ($city && $city->type === 'city') {
                    $this->addCityToResults($countriesWithCities[$countryKey], $city, $processedCities);
                }
            }
        }

        return array_values($countriesWithCities);

        } catch (\Exception $e) {
            \Log::error('Location search error', [
                'search' => $search,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return empty results on error
            return [];
        }
    }

    /**
     * Get the country for a given location
     */
    private function getCountryForLocation($location)
    {
        $current = $location;
        while ($current && $current->type !== 'country') {
            $current = $current->parent;
        }
        return $current;
    }

    /**
     * Add city to search results
     */
    private function addCityToResults(&$countryData, $city, &$processedCities)
    {
        if (in_array($city->id, $processedCities)) {
            return;
        }

        $processedCities[] = $city->id;

        $areas = Location::active()
            ->ofType('area')
            ->where('parent_id', $city->id)
            ->orderBy('name')
            ->limit(6)
            ->get();

        $areaCount = Location::active()
            ->ofType('area')
            ->where('parent_id', $city->id)
            ->count();

        // Get escort count for this city (including all child areas)
        $cityLocationIds = [$city->id];
        $areaIds = Location::active()
            ->ofType('area')
            ->where('parent_id', $city->id)
            ->pluck('id')
            ->toArray();
        $cityLocationIds = array_merge($cityLocationIds, $areaIds);

        $escortCount = Escort::whereHas('locations', function($query) use ($cityLocationIds) {
            $query->whereIn('location_id', $cityLocationIds);
        })
        ->verified()
        ->whereHas('user', function ($q) {
            $q->where('is_active', true);
        })
        ->count();

        $countryData['cities'][] = [
            'city' => $city,
            'areas' => $areas,
            'area_count' => $areaCount,
            'escort_count' => $escortCount
        ];
    }
}