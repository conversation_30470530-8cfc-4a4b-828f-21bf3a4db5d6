<?php

namespace App\Http\Controllers\Agency;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\AgencyStatusRequest;
use App\Models\StatusPricing;
use App\Services\NotificationService;

class StatusRequestController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Display a listing of the agency's status requests.
     */
    public function index()
    {
        $agency = Auth::user()->agency;
        $statusRequests = $agency->statusRequests()->latest()->get();

        return view('agency.status-requests.index', compact('agency', 'statusRequests'));
    }

    /**
     * Show the form for creating a new status request.
     */
    public function create()
    {
        $agency = Auth::user()->agency;

        // Check if there are any pending requests
        $pendingApproval = $agency->statusRequests()
            ->where('request_type', 'approval')
            ->where('status', 'pending')
            ->exists();

        $pendingFeatured = $agency->statusRequests()
            ->where('request_type', 'featured')
            ->where('status', 'pending')
            ->exists();

        // Get pricing information
        $approvalPricing = StatusPricing::agencyApproval()->active()->orderBy('duration')->get();
        $featuredPricing = StatusPricing::agencyFeatured()->active()->orderBy('duration')->get();

        // If no pricing exists, create default pricing
        if ($approvalPricing->isEmpty()) {
            $approvalPricing = collect([
                (object)[
                    'id' => 'default',
                    'duration' => 'month',
                    'price' => 50000,
                    'formatted_price' => 'UGX 50,000',
                    'formatted_duration' => '1 Month'
                ]
            ]);
        }

        if ($featuredPricing->isEmpty()) {
            $featuredPricing = collect([
                (object)[
                    'id' => 'default',
                    'duration' => 'week',
                    'price' => 20000,
                    'formatted_price' => 'UGX 20,000',
                    'formatted_duration' => '1 Week'
                ],
                (object)[
                    'id' => 'default',
                    'duration' => 'month',
                    'price' => 50000,
                    'formatted_price' => 'UGX 50,000',
                    'formatted_duration' => '1 Month'
                ]
            ]);
        }

        return view('agency.status-requests.create', compact(
            'agency',
            'pendingApproval',
            'pendingFeatured',
            'approvalPricing',
            'featuredPricing'
        ));
    }

    /**
     * Store a newly created status request in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'request_type' => 'required|in:approval,featured',
            'duration' => 'required|in:day,week,month,annual',
        ]);

        $agency = Auth::user()->agency;

        // Enhanced conflict prevention checks
        if ($request->request_type === 'approval') {
            // Check if already approved
            if ($agency->is_approved) {
                return redirect()->back()->withErrors([
                    'request_type' => 'Your agency is already approved. No need to request approval again.'
                ])->withInput();
            }

            // Check for pending approval request
            $existingRequest = $agency->statusRequests()
                ->where('request_type', 'approval')
                ->where('status', 'pending')
                ->first();

            if ($existingRequest) {
                return redirect()->back()->withErrors([
                    'request_type' => 'You already have a pending approval request submitted on ' . $existingRequest->created_at->format('M d, Y') . '.'
                ])->withInput();
            }
        }

        if ($request->request_type === 'featured') {
            // Check if already featured and not expired
            if ($agency->is_featured && ($agency->featured_expires_at === null || $agency->featured_expires_at > now())) {
                $expiryText = $agency->featured_expires_at ? ' until ' . $agency->featured_expires_at->format('M d, Y') : '';
                return redirect()->back()->withErrors([
                    'request_type' => 'Your agency is already featured' . $expiryText . '. Wait for it to expire before requesting again.'
                ])->withInput();
            }

            // Check for pending featured request
            $existingRequest = $agency->statusRequests()
                ->where('request_type', 'featured')
                ->where('status', 'pending')
                ->first();

            if ($existingRequest) {
                return redirect()->back()->withErrors([
                    'request_type' => 'You already have a pending featured request submitted on ' . $existingRequest->created_at->format('M d, Y') . '.'
                ])->withInput();
            }
        }

        // Get pricing based on request type and duration
        $pricingType = 'agency_' . $request->request_type;
        $pricing = StatusPricing::where('request_type', $pricingType)
            ->where('duration', $request->duration)
            ->active()
            ->first();

        // If no pricing exists, use default values
        $price = $pricing ? $pricing->price : ($request->request_type === 'approval' ? 50000 : 20000);

        // Create the status request
        $statusRequest = $agency->statusRequests()->create([
            'request_type' => $request->request_type,
            'duration' => $request->duration,
            'price' => $price,
            'status' => 'pending',
        ]);

        // Notify admins about the new request
        $this->notificationService->notifyUserTypes(
            ['admin'],
            'created',
            'agency_status_request',
            $agency->name,
            [
                'request_type' => $request->request_type,
                'duration' => $request->duration,
                'price' => $price
            ]
        );

        // Show confirmation page
        return redirect()->route('agency.status-requests.confirm', $statusRequest->id);
    }

    /**
     * Display the confirmation page for a status request.
     */
    public function confirm($id)
    {
        $agency = Auth::user()->agency;
        $statusRequest = AgencyStatusRequest::where('id', $id)
            ->where('agency_id', $agency->id)
            ->firstOrFail();

        // Get admin contact information from settings
        $adminPhone = \App\Models\Setting::where('key', 'admin_phone')->first()?->value ?? '+256 700 000000';

        return view('agency.status-requests.confirm', compact('agency', 'statusRequest', 'adminPhone'));
    }

    /**
     * Display the specified status request.
     */
    public function show($id)
    {
        $agency = Auth::user()->agency;
        $statusRequest = AgencyStatusRequest::where('id', $id)
            ->where('agency_id', $agency->id)
            ->firstOrFail();

        return view('agency.status-requests.show', compact('agency', 'statusRequest'));
    }

    /**
     * Cancel the specified status request.
     */
    public function cancel($id)
    {
        $agency = Auth::user()->agency;
        $statusRequest = AgencyStatusRequest::where('id', $id)
            ->where('agency_id', $agency->id)
            ->where('status', 'pending')
            ->firstOrFail();

        // Delete the request
        $statusRequest->delete();

        return redirect()->route('agency.status-requests.index')->with('success', 'Request cancelled successfully.');
    }
}
