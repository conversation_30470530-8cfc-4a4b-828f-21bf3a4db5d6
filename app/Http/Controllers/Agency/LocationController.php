<?php

namespace App\Http\Controllers\Agency;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Location;

class LocationController extends Controller
{
    /**
     * Display a listing of locations for the agency to select.
     */
    public function index()
    {
        $agency = Auth::user()->agency;
        $countries = Location::ofType('country')->orderBy('name')->get();
        $selectedLocations = ($agency && $agency->locations) ? $agency->locations->pluck('id')->toArray() : [];

        return view('agency.locations.index', compact('agency', 'countries', 'selectedLocations'));
    }

    /**
     * Update the agency's locations.
     */
    public function update(Request $request)
    {
        $request->validate([
            'locations' => 'array',
            'locations.*' => 'exists:locations,id',
        ]);

        $agency = Auth::user()->agency;

        if (!$agency) {
            return redirect()->route('profile.agency.edit')
                ->with('error', 'Please complete your agency profile first.');
        }

        // Sync locations
        $agency->locations()->sync($request->locations ?? []);

        return redirect()->route('agency.locations')->with('success', 'Locations updated successfully.');
    }
}
