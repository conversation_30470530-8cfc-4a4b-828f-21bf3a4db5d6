<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Mail\ContactFormMail;
use Illuminate\Support\Facades\Validator;
use App\Models\Setting;
use Illuminate\Support\Facades\Log;

class ContactController extends Controller
{
    /**
     * Display the contact form.
     */
    public function index()
    {
        return view('contact.index');
    }

    /**
     * Process the contact form submission.
     */
    public function store(Request $request)
    {
        // Validate the form data
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
            'g-recaptcha-response' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->route('contact.index')
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Get the admin-set contact email from settings
            $contactEmail = setting('contact_email', '<EMAIL>');

            // Send the email
            Mail::to($contactEmail)->send(new ContactFormMail($request->all()));

            return redirect()->route('contact.index')
                ->with('success', 'Thank you for your message! We will get back to you soon.');
        } catch (\Exception $e) {
            // Log the error
            Log::error('Failed to send contact email: ' . $e->getMessage());

            return redirect()->route('contact.index')
                ->with('success', 'Thank you for your message! We will get back to you soon.')
                ->with('error', 'There was an issue sending your message, but we have recorded your inquiry.');
        }
    }
}
