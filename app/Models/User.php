<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use Has<PERSON><PERSON>Tokens, HasFactory, Notifiable;

    /**
     * User types
     */
    const TYPE_ESCORT = 'escort';
    const TYPE_AGENCY = 'agency';
    const TYPE_ADMIN = 'admin';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'username',
        'email',
        'password',
        'user_type',
        'last_login',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'last_login' => 'datetime',
        'password' => 'hashed',
        'is_active' => 'boolean',
    ];
    /**
     * Get the user's profile
     */
    public function profile()
    {
        return $this->hasOne(UserProfile::class);
    }

    /**
     * Get the escort associated with the user
     */
    public function escort()
    {
        return $this->hasOne(Escort::class);
    }

    /**
     * Get the agency associated with the user
     */
    public function agency()
    {
        return $this->hasOne(Agency::class);
    }




    /**
     * Check if user is an admin
     */
    public function isAdmin()
    {
        return $this->user_type === self::TYPE_ADMIN;
    }

    /**
     * Check if user is an escort
     */
    public function isEscort()
    {
        return $this->user_type === self::TYPE_ESCORT;
    }

    /**
     * Check if user is an agency
     */
    public function isAgency()
    {
        return $this->user_type === self::TYPE_AGENCY;
    }

    /**
     * Get the user's notifications
     */
    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }

    /**
     * Get the user's unread notifications count
     */
    public function unreadNotificationsCount()
    {
        try {
            return $this->notifications()->unread()->count();
        } catch (\Exception $e) {
            // Return 0 if there's an issue with the notifications table
            return 0;
        }
    }


}
