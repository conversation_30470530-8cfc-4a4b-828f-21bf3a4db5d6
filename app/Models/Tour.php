<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Tour extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'escort_id',
        'location_id',
        'start_date',
        'end_date',
        'description',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
    ];

    /**
     * Get the escort that owns the tour.
     */
    public function escort()
    {
        return $this->belongsTo(Escort::class);
    }

    /**
     * Get the location for the tour.
     */
    public function location()
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * Scope a query to only include current tours.
     */
    public function scopeCurrent($query)
    {
        $today = now()->format('Y-m-d');
        return $query->where('start_date', '<=', $today)
            ->where('end_date', '>=', $today);
    }

    /**
     * Scope a query to only include upcoming tours.
     */
    public function scopeUpcoming($query)
    {
        $today = now()->format('Y-m-d');
        return $query->where('start_date', '>', $today);
    }

    /**
     * Scope a query to only include past tours.
     */
    public function scopePast($query)
    {
        $today = now()->format('Y-m-d');
        return $query->where('end_date', '<', $today);
    }
}
