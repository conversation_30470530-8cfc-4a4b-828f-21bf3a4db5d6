<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EscortRate extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'escort_id',
        'duration',
        'incall_price',
        'outcall_price',
        'min_incall_price',
        'max_incall_price',
        'min_outcall_price',
        'max_outcall_price',
        'currency',
        'description',
    ];

    /**
     * The attributes that should have default values.
     *
     * @var array
     */
    protected $attributes = [
        'currency' => 'UGX',
    ];

    /**
     * Get the escort that owns the rate.
     */
    public function escort()
    {
        return $this->belongsTo(Escort::class);
    }

    /**
     * Format the incall price with currency.
     */
    public function getFormattedIncallPriceAttribute()
    {
        // Check if we have range prices
        if ($this->min_incall_price !== null && $this->max_incall_price !== null) {
            // Format without decimal places for UGX
            $decimals = ($this->currency === 'UGX') ? 0 : 2;
            $minPrice = number_format($this->min_incall_price, $decimals);
            $maxPrice = number_format($this->max_incall_price, $decimals);
            return $this->currency . ' ' . $minPrice . ' - ' . $maxPrice;
        }
        // Fallback to old single price if range not set
        else if ($this->incall_price) {
            $decimals = ($this->currency === 'UGX') ? 0 : 2;
            return $this->currency . ' ' . number_format($this->incall_price, $decimals);
        }

        return 'Not offered';
    }

    /**
     * Format the outcall price with currency.
     */
    public function getFormattedOutcallPriceAttribute()
    {
        // Check if we have range prices
        if ($this->min_outcall_price !== null && $this->max_outcall_price !== null) {
            // Format without decimal places for UGX
            $decimals = ($this->currency === 'UGX') ? 0 : 2;
            $minPrice = number_format($this->min_outcall_price, $decimals);
            $maxPrice = number_format($this->max_outcall_price, $decimals);
            return $this->currency . ' ' . $minPrice . ' - ' . $maxPrice;
        }
        // Fallback to old single price if range not set
        else if ($this->outcall_price) {
            $decimals = ($this->currency === 'UGX') ? 0 : 2;
            return $this->currency . ' ' . number_format($this->outcall_price, $decimals);
        }

        return 'Not offered';
    }

    /**
     * Check if incall price is a range
     */
    public function getHasIncallRangeAttribute()
    {
        return $this->min_incall_price !== null && $this->max_incall_price !== null;
    }

    /**
     * Check if outcall price is a range
     */
    public function getHasOutcallRangeAttribute()
    {
        return $this->min_outcall_price !== null && $this->max_outcall_price !== null;
    }

    /**
     * Get a human-readable duration label.
     */
    public function getHumanDurationAttribute()
    {
        switch ($this->duration) {
            case '30min':
                return '30 minutes';
            case '1hour':
                return '1 hour';
            case '2hours':
                return '2 hours';
            case '3hours':
                return '3 hours';
            case '6hours':
                return '6 hours';
            case '12hours':
                return '12 hours';
            case '24hours':
                return '24 hours (overnight)';
            default:
                return $this->duration;
        }
    }
}
