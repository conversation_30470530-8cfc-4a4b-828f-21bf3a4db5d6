<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Language extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'code',
    ];

    /**
     * Get the escorts for the language.
     */
    public function escorts()
    {
        return $this->belongsToMany(Escort::class, 'escort_languages')
            ->withPivot('proficiency');
    }
}
