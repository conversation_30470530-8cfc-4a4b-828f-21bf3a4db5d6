<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Agency extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'name',
        'slug',
        'description',
        'logo_path',
        'website',
        'phone',
        'email',
        'address',
        'is_verified',
        'is_premium',
        'is_featured',
        'featured_expires_at',
        'is_approved',
        'is_active',
        'profile_views',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_verified' => 'boolean',
        'is_premium' => 'boolean',
        'is_featured' => 'boolean',
        'featured_expires_at' => 'datetime',
        'is_approved' => 'boolean',
        'is_active' => 'boolean',
        'profile_views' => 'integer',
    ];

    /**
     * Get the user that owns the agency.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the escorts for the agency.
     */
    public function escorts()
    {
        return $this->hasMany(Escort::class);
    }

    /**
     * Get the locations for the agency.
     */
    public function locations()
    {
        return $this->belongsToMany(Location::class, 'agency_locations')
            ->withPivot('is_primary');
    }

    /**
     * Get the primary location for the agency.
     */
    public function primaryLocation()
    {
        return $this->belongsToMany(Location::class, 'agency_locations')
            ->wherePivot('is_primary', true)
            ->first();
    }





    /**
     * Scope a query to only include verified agencies.
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope a query to only include premium agencies.
     */
    public function scopePremium($query)
    {
        return $query->where('is_premium', true);
    }

    /**
     * Scope a query to only include featured agencies.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true)
            ->where(function ($q) {
                $q->whereNull('featured_expires_at')
                  ->orWhere('featured_expires_at', '>', now());
            });
    }

    /**
     * Scope a query to only include approved agencies.
     */
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    /**
     * Scope a query to only include active agencies.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the status requests for the agency.
     */
    public function statusRequests()
    {
        return $this->hasMany(AgencyStatusRequest::class);
    }

    /**
     * Check if agency can request approval.
     */
    public function canRequestApproval()
    {
        // Already approved
        if ($this->is_approved) {
            return false;
        }

        // Has pending approval request
        return !$this->statusRequests()
            ->where('request_type', 'approval')
            ->where('status', 'pending')
            ->exists();
    }

    /**
     * Check if agency can request featured status.
     */
    public function canRequestFeatured()
    {
        // Already featured and not expired
        if ($this->is_featured && ($this->featured_expires_at === null || $this->featured_expires_at > now())) {
            return false;
        }

        // Has pending featured request
        return !$this->statusRequests()
            ->where('request_type', 'featured')
            ->where('status', 'pending')
            ->exists();
    }

    /**
     * Get pending approval request.
     */
    public function getPendingApprovalRequest()
    {
        return $this->statusRequests()
            ->where('request_type', 'approval')
            ->where('status', 'pending')
            ->first();
    }

    /**
     * Get pending featured request.
     */
    public function getPendingFeaturedRequest()
    {
        return $this->statusRequests()
            ->where('request_type', 'featured')
            ->where('status', 'pending')
            ->first();
    }

    /**
     * Check if featured status is active (not expired).
     */
    public function isFeaturedActive()
    {
        return $this->is_featured && ($this->featured_expires_at === null || $this->featured_expires_at > now());
    }
}
