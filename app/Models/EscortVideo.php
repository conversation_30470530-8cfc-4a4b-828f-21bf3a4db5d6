<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EscortVideo extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'escort_id',
        'video_path',
        'path',
        'thumbnail_path',
        'is_primary',
        'is_main',
        'sort_order',
        'file_size',
        'duration',
        'mime_type',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_primary' => 'boolean',
        'is_main' => 'boolean',
        'file_size' => 'integer',
        'duration' => 'integer',
    ];

    /**
     * Get the escort that owns the video.
     */
    public function escort()
    {
        return $this->belongsTo(Escort::class);
    }

    /**
     * Set this video as the primary video for the escort.
     */
    public function setPrimary()
    {
        // First, unset any existing primary video
        EscortVideo::where('escort_id', $this->escort_id)
            ->where('is_primary', true)
            ->update([
                'is_primary' => false,
                'is_main' => false
            ]);

        // Then set this one as primary
        $this->is_primary = true;
        $this->is_main = true;
        $this->save();
    }

    /**
     * Get the video URL.
     */
    public function getVideoUrlAttribute()
    {
        return asset('storage/' . ($this->path ?: $this->video_path));
    }

    /**
     * Get the thumbnail URL.
     */
    public function getThumbnailUrlAttribute()
    {
        if ($this->thumbnail_path) {
            return asset('storage/' . $this->thumbnail_path);
        }
        
        // Return a default video thumbnail if no thumbnail exists
        return asset('images/default-video-thumbnail.jpg');
    }

    /**
     * Get formatted file size.
     */
    public function getFormattedFileSizeAttribute()
    {
        if (!$this->file_size) {
            return 'Unknown size';
        }

        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get formatted duration.
     */
    public function getFormattedDurationAttribute()
    {
        if (!$this->duration) {
            return 'Unknown duration';
        }

        $minutes = floor($this->duration / 60);
        $seconds = $this->duration % 60;
        
        return sprintf('%d:%02d', $minutes, $seconds);
    }
}
