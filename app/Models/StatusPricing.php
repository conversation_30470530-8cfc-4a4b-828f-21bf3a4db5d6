<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StatusPricing extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'request_type',
        'duration',
        'price',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Scope a query to only include active pricing.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include verification pricing.
     */
    public function scopeVerification($query)
    {
        return $query->where('request_type', 'verification');
    }

    /**
     * Scope a query to only include featured pricing.
     */
    public function scopeFeatured($query)
    {
        return $query->where('request_type', 'featured');
    }

    /**
     * Scope a query to only include agency approval pricing.
     */
    public function scopeAgencyApproval($query)
    {
        return $query->where('request_type', 'agency_approval');
    }

    /**
     * Scope a query to only include agency featured pricing.
     */
    public function scopeAgencyFeatured($query)
    {
        return $query->where('request_type', 'agency_featured');
    }

    /**
     * Get the formatted price.
     */
    public function getFormattedPriceAttribute()
    {
        return 'UGX ' . number_format($this->price, 0);
    }

    /**
     * Get the formatted duration.
     */
    public function getFormattedDurationAttribute()
    {
        return match ($this->duration) {
            'day' => '1 Day',
            'week' => '1 Week',
            'month' => '1 Month',
            'annual' => '1 Year',
            default => $this->duration,
        };
    }
}
