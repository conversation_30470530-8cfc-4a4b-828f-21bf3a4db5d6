<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EscortImage extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'escort_id',
        'image_path',
        'path',
        'is_primary',
        'is_main',
        'sort_order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_primary' => 'boolean',
        'is_main' => 'boolean',
    ];

    /**
     * Get the escort that owns the image.
     */
    public function escort()
    {
        return $this->belongsTo(Escort::class);
    }

    /**
     * Set this image as the primary image for the escort.
     */
    public function setPrimary()
    {
        // First, unset any existing primary image
        EscortImage::where('escort_id', $this->escort_id)
            ->where('is_primary', true)
            ->update([
                'is_primary' => false,
                'is_main' => false
            ]);

        // Then set this one as primary
        $this->is_primary = true;
        $this->is_main = true;
        $this->save();
    }
}
