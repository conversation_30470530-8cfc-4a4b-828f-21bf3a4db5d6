<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Location extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'type',
        'parent_id',
        'slug',
        'is_active',
    ];

    /**
     * Get the parent location.
     */
    public function parent()
    {
        return $this->belongsTo(Location::class, 'parent_id');
    }

    /**
     * Get the child locations.
     */
    public function children()
    {
        return $this->hasMany(Location::class, 'parent_id');
    }

    /**
     * Get escorts associated with this location.
     */
    public function escorts()
    {
        return $this->belongsToMany(Escort::class, 'escort_locations');
    }

    /**
     * Get agencies associated with this location.
     */
    public function agencies()
    {
        return $this->belongsToMany(Agency::class, 'agency_locations');
    }

    /**
     * Scope a query to only include active locations.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include locations of a specific type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get the full location path (e.g., "Uganda > Kampala > Ntinda")
     */
    public function getFullPathAttribute()
    {
        $path = $this->name;
        $current = $this;

        while ($current->parent) {
            $current = $current->parent;
            $path = $current->name . ' > ' . $path;
        }

        return $path;
    }
}
