<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Service extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
        'category',
    ];

    /**
     * Get the escorts for the service.
     */
    public function escorts()
    {
        return $this->belongsToMany(Escort::class, 'escort_services');
    }

    /**
     * Scope a query to only include services of a specific category.
     */
    public function scopeCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Get all available categories.
     */
    public static function categories()
    {
        return self::select('category')->distinct()->pluck('category');
    }
}
