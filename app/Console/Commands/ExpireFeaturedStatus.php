<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Escort;
use App\Models\Agency;
use Carbon\Carbon;

class ExpireFeaturedStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'featured:expire';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Expire featured status for escorts and agencies that have passed their expiration date';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $now = Carbon::now();

        // Expire featured escorts
        $expiredEscorts = Escort::where('is_featured', true)
            ->where('featured_expires_at', '<=', $now)
            ->whereNotNull('featured_expires_at')
            ->get();

        foreach ($expiredEscorts as $escort) {
            $escort->update([
                'is_featured' => false,
                'featured_expires_at' => null
            ]);
        }

        // Expire featured agencies
        $expiredAgencies = Agency::where('is_featured', true)
            ->where('featured_expires_at', '<=', $now)
            ->whereNotNull('featured_expires_at')
            ->get();

        foreach ($expiredAgencies as $agency) {
            $agency->update([
                'is_featured' => false,
                'featured_expires_at' => null
            ]);
        }

        $totalExpired = $expiredEscorts->count() + $expiredAgencies->count();

        $this->info("Expired featured status for {$expiredEscorts->count()} escorts and {$expiredAgencies->count()} agencies.");
        $this->info("Total expired: {$totalExpired}");

        return Command::SUCCESS;
    }
}
