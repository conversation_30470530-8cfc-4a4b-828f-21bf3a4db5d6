<?php

namespace App\Console\Commands;

use App\Models\Agency;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class UpdateAgencyLogos extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'agencies:update-logos';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update agency logos to use existing files';

    /**
     * Default agency logo images
     */
    protected $defaultLogos = [
        'agencies/default-agency-logo-1.jpg',
        'agencies/default-agency-logo-2.jpg',
        'agencies/default-agency-logo-3.jpg',
    ];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $agencies = Agency::all();

        if ($agencies->isEmpty()) {
            $this->info('No agencies found.');
            return 0;
        }

        $bar = $this->output->createProgressBar($agencies->count());
        $bar->start();

        foreach ($agencies as $index => $agency) {
            // Check if the logo file exists
            if (!$agency->logo_path || !Storage::disk('public')->exists($agency->logo_path)) {
                // Assign a default logo
                $logoIndex = $index % count($this->defaultLogos);
                $agency->logo_path = $this->defaultLogos[$logoIndex];
                $agency->save();

                $this->line('\nUpdated logo for agency: ' . $agency->name);
            }

            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info('Agency logos updated successfully.');

        return 0;
    }
}
