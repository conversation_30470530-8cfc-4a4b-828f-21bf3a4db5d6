<?php

namespace App\Console\Commands;

use App\Models\Agency;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class GenerateAgencySlugs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'agencies:generate-slugs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate slugs for agencies that do not have them';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $agencies = Agency::whereNull('slug')->orWhere('slug', '')->get();

        if ($agencies->isEmpty()) {
            $this->info('All agencies already have slugs.');
            return 0;
        }

        $bar = $this->output->createProgressBar($agencies->count());
        $bar->start();

        foreach ($agencies as $agency) {
            $slug = Str::slug($agency->name);
            $originalSlug = $slug;
            $count = 1;

            // Make sure the slug is unique
            while (Agency::where('slug', $slug)->where('id', '!=', $agency->id)->exists()) {
                $slug = $originalSlug . '-' . $count++;
            }

            $agency->slug = $slug;
            $agency->save();

            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info('Slugs generated for ' . $agencies->count() . ' agencies.');

        return 0;
    }
}
