<?php

namespace App\Console\Commands;

use App\Models\Agency;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class MatchAgencyLogos extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'agencies:match-logos';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create and assign logos that match agency names';

    /**
     * Agency logo mappings
     */
    protected $logoMappings = [
        'vip' => ['color' => [220, 66, 150], 'element' => 'crown', 'bg' => 'luxury'],
        'elite' => ['color' => [41, 128, 185], 'element' => 'star', 'bg' => 'elegant'],
        'luxury' => ['color' => [142, 68, 173], 'element' => 'diamond', 'bg' => 'luxury'],
        'premium' => ['color' => [211, 84, 0], 'element' => 'badge', 'bg' => 'premium'],
        'diamond' => ['color' => [52, 152, 219], 'element' => 'gem', 'bg' => 'diamond'],
        'royal' => ['color' => [155, 89, 182], 'element' => 'crown', 'bg' => 'royal'],
        'high' => ['color' => [230, 126, 34], 'element' => 'star', 'bg' => 'premium'],
        'top' => ['color' => [46, 204, 113], 'element' => 'badge', 'bg' => 'elegant'],
        'exclusive' => ['color' => [52, 73, 94], 'element' => 'diamond', 'bg' => 'luxury'],
        'companions' => ['color' => [231, 76, 60], 'element' => 'heart', 'bg' => 'elegant'],
        'models' => ['color' => [26, 188, 156], 'element' => 'star', 'bg' => 'model'],
        'escort' => ['color' => [241, 196, 15], 'element' => 'badge', 'bg' => 'default'],
    ];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $agencies = Agency::all();

        if ($agencies->isEmpty()) {
            $this->info('No agencies found.');
            return 0;
        }

        // Create the agencies directory if it doesn't exist
        if (!Storage::disk('public')->exists('agencies')) {
            Storage::disk('public')->makeDirectory('agencies');
            $this->info('Created agencies directory in storage.');
        }

        $bar = $this->output->createProgressBar($agencies->count());
        $bar->start();

        foreach ($agencies as $agency) {
            // Generate a logo path based on the agency name
            $logoPath = $this->generateLogoForAgency($agency);

            // Update the agency record
            $agency->logo_path = $logoPath;
            $agency->save();

            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info('Agency logos matched and updated successfully.');

        return 0;
    }

    /**
     * Generate a logo for an agency based on its name
     */
    protected function generateLogoForAgency(Agency $agency)
    {
        // Create a slug from the agency name for the filename
        $filename = Str::slug($agency->name) . '.png';
        $logoPath = 'agencies/' . $filename;

        // Skip if the logo already exists
        if (Storage::disk('public')->exists($logoPath)) {
            return $logoPath;
        }

        // Determine the style based on the agency name
        $style = $this->determineLogoStyle($agency->name);

        // Create the logo image
        $width = 600;
        $height = 400;
        $image = imagecreatetruecolor($width, $height);

        // Enable alpha blending
        imagealphablending($image, true);
        imagesavealpha($image, true);

        // Create a transparent background
        $transparent = imagecolorallocatealpha($image, 0, 0, 0, 127);
        imagefill($image, 0, 0, $transparent);

        // Add background
        $this->addBackground($image, $width, $height, $style['bg']);

        // Add decorative element
        $this->addDecorativeElement($image, $width, $height, $style['element'], $style['color']);

        // Add agency name text
        $this->addAgencyText($image, $width, $height, $agency->name);

        // Save the image
        $tempFile = tempnam(sys_get_temp_dir(), 'agency_logo');
        imagepng($image, $tempFile, 9);
        imagedestroy($image);

        // Copy to storage
        Storage::disk('public')->put($logoPath, file_get_contents($tempFile));
        unlink($tempFile);

        $this->line('\nCreated logo for: ' . $agency->name);

        return $logoPath;
    }

    /**
     * Determine the logo style based on the agency name
     */
    protected function determineLogoStyle($name)
    {
        $name = strtolower($name);
        $style = [
            'color' => [220, 66, 150], // Default pink
            'element' => 'badge',      // Default element
            'bg' => 'default',         // Default background
        ];

        // Check for keywords in the agency name
        foreach ($this->logoMappings as $keyword => $mapping) {
            if (Str::contains($name, $keyword)) {
                $style = $mapping;
                break;
            }
        }

        return $style;
    }

    /**
     * Add a background to the logo
     */
    protected function addBackground($image, $width, $height, $style)
    {
        switch ($style) {
            case 'luxury':
                // Luxury gradient background (dark purple to pink)
                $startColor = imagecolorallocate($image, 45, 20, 60);
                $endColor = imagecolorallocate($image, 150, 50, 100);
                $this->createGradientBackground($image, $width, $height, $startColor, $endColor);
                break;

            case 'elegant':
                // Elegant gradient (dark blue to light blue)
                $startColor = imagecolorallocate($image, 20, 40, 80);
                $endColor = imagecolorallocate($image, 60, 100, 170);
                $this->createGradientBackground($image, $width, $height, $startColor, $endColor);
                break;

            case 'premium':
                // Premium gradient (dark gold to light gold)
                $startColor = imagecolorallocate($image, 100, 70, 20);
                $endColor = imagecolorallocate($image, 200, 170, 80);
                $this->createGradientBackground($image, $width, $height, $startColor, $endColor);
                break;

            case 'diamond':
                // Diamond gradient (dark teal to light teal)
                $startColor = imagecolorallocate($image, 20, 80, 100);
                $endColor = imagecolorallocate($image, 80, 200, 220);
                $this->createGradientBackground($image, $width, $height, $startColor, $endColor);
                break;

            case 'royal':
                // Royal gradient (dark purple to light purple)
                $startColor = imagecolorallocate($image, 50, 20, 80);
                $endColor = imagecolorallocate($image, 120, 80, 180);
                $this->createGradientBackground($image, $width, $height, $startColor, $endColor);
                break;

            case 'model':
                // Model gradient (dark gray to light gray)
                $startColor = imagecolorallocate($image, 40, 40, 40);
                $endColor = imagecolorallocate($image, 120, 120, 120);
                $this->createGradientBackground($image, $width, $height, $startColor, $endColor);
                break;

            default:
                // Default gradient (dark pink to light pink)
                $startColor = imagecolorallocate($image, 150, 30, 80);
                $endColor = imagecolorallocate($image, 220, 100, 150);
                $this->createGradientBackground($image, $width, $height, $startColor, $endColor);
                break;
        }
    }

    /**
     * Create a gradient background
     */
    protected function createGradientBackground($image, $width, $height, $startColor, $endColor)
    {
        // Create gradient effect
        for ($i = 0; $i < $height; $i++) {
            $ratio = $i / $height;
            $r = (int)(($startColor >> 16 & 0xFF) * (1 - $ratio) + ($endColor >> 16 & 0xFF) * $ratio);
            $g = (int)(($startColor >> 8 & 0xFF) * (1 - $ratio) + ($endColor >> 8 & 0xFF) * $ratio);
            $b = (int)(($startColor & 0xFF) * (1 - $ratio) + ($endColor & 0xFF) * $ratio);
            $color = imagecolorallocate($image, $r, $g, $b);
            imageline($image, 0, $i, $width, $i, $color);
        }
    }

    /**
     * Add decorative element to the logo
     */
    protected function addDecorativeElement($image, $width, $height, $element, $colorValues)
    {
        $color = imagecolorallocate($image, $colorValues[0], $colorValues[1], $colorValues[2]);
        $white = imagecolorallocate($image, 255, 255, 255);

        switch ($element) {
            case 'crown':
                // Crown shape
                $points = [
                    $width/2 - 60, $height/2 - 20,
                    $width/2 - 40, $height/2 - 50,
                    $width/2 - 20, $height/2 - 30,
                    $width/2, $height/2 - 60,
                    $width/2 + 20, $height/2 - 30,
                    $width/2 + 40, $height/2 - 50,
                    $width/2 + 60, $height/2 - 20,
                    $width/2 + 60, $height/2,
                    $width/2 - 60, $height/2,
                ];
                imagefilledpolygon($image, $points, 9, $color);
                // Add highlights
                imagesetthickness($image, 2);
                imageline($image, $width/2 - 40, $height/2 - 50, $width/2 - 20, $height/2 - 30, $white);
                imageline($image, $width/2, $height/2 - 60, $width/2 + 20, $height/2 - 30, $white);
                break;

            case 'star':
                // Star shape
                $cx = $width/2;
                $cy = $height/2 - 30;
                $r = 50;
                $points = [];
                for ($i = 0; $i < 10; $i++) {
                    $r2 = $i % 2 == 0 ? $r : $r/2;
                    $points[] = $cx + $r2 * cos(M_PI/2 + $i * 2 * M_PI / 10);
                    $points[] = $cy + $r2 * sin(M_PI/2 + $i * 2 * M_PI / 10);
                }
                imagefilledpolygon($image, $points, 10, $color);
                break;

            case 'diamond':
                // Diamond shape
                $points = [
                    $width/2, $height/2 - 60,
                    $width/2 + 50, $height/2,
                    $width/2, $height/2 + 60,
                    $width/2 - 50, $height/2,
                ];
                imagefilledpolygon($image, $points, 4, $color);
                // Add highlight
                imagesetthickness($image, 2);
                imageline($image, $width/2, $height/2 - 60, $width/2 + 50, $height/2, $white);
                break;

            case 'gem':
                // Gem shape
                $points = [
                    $width/2 - 10, $height/2 - 60,
                    $width/2 + 40, $height/2 - 60,
                    $width/2 + 60, $height/2 - 20,
                    $width/2 + 40, $height/2 + 40,
                    $width/2 - 10, $height/2 + 40,
                    $width/2 - 30, $height/2 - 20,
                ];
                imagefilledpolygon($image, $points, 6, $color);
                // Add facets
                imagesetthickness($image, 1);
                imageline($image, $width/2 - 10, $height/2 - 60, $width/2 + 15, $height/2 + 40, $white);
                imageline($image, $width/2 + 40, $height/2 - 60, $width/2 + 15, $height/2 + 40, $white);
                break;

            case 'badge':
                // Badge/shield shape
                $points = [
                    $width/2 - 50, $height/2 - 50,
                    $width/2 + 50, $height/2 - 50,
                    $width/2 + 50, $height/2 + 10,
                    $width/2, $height/2 + 50,
                    $width/2 - 50, $height/2 + 10,
                ];
                imagefilledpolygon($image, $points, 5, $color);
                break;

            case 'heart':
                // Heart shape (simplified)
                $cx = $width/2;
                $cy = $height/2;
                $size = 40;

                // Draw two circles for the top of the heart
                imagefilledellipse($image, $cx - $size/2, $cy - $size/4, $size, $size, $color);
                imagefilledellipse($image, $cx + $size/2, $cy - $size/4, $size, $size, $color);

                // Draw the bottom triangle
                $points = [
                    $cx - $size, $cy - $size/4,
                    $cx + $size, $cy - $size/4,
                    $cx, $cy + $size,
                ];
                imagefilledpolygon($image, $points, 3, $color);
                break;
        }
    }

    /**
     * Add agency name text to the logo
     */
    protected function addAgencyText($image, $width, $height, $agencyName)
    {
        // Add text
        $white = imagecolorallocate($image, 255, 255, 255);

        // Split agency name into words
        $words = explode(' ', $agencyName);
        $lineHeight = 30;
        $y = $height - (count($words) * $lineHeight) - 20;

        // Add each word on a new line
        foreach ($words as $word) {
            // Use built-in font (no TTF required)
            $font = 5; // Largest built-in font
            $textWidth = imagefontwidth($font) * strlen($word);
            $x = ($width - $textWidth) / 2;

            // Add text shadow for better readability
            $shadow = imagecolorallocate($image, 0, 0, 0);
            imagestring($image, $font, $x + 1, $y + 1, $word, $shadow);
            imagestring($image, $font, $x, $y, $word, $white);

            $y += $lineHeight;
        }
    }
}
