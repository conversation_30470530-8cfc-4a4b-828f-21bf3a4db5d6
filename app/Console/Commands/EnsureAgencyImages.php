<?php

namespace App\Console\Commands;

use App\Models\Agency;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class EnsureAgencyImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'agencies:ensure-images';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Ensure all agencies have logo images';

    /**
     * Default agency logo images
     */
    protected $defaultLogos = [
        'agencies/luxury-escorts-agency.png',
        'agencies/vip-companions.png',
        'agencies/elite-models-agency.png',
        'agencies/diamond-escorts.png',
        'agencies/premium-companions.png',
        'agencies/royal-escorts-agency.png',
    ];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Create the agencies directory if it doesn't exist
        if (!Storage::disk('public')->exists('agencies')) {
            Storage::disk('public')->makeDirectory('agencies');
            $this->info('Created agencies directory in storage.');
        }

        // Copy default logos to storage if they don't exist
        $this->ensureDefaultLogosExist();

        // Get agencies without logos
        $agencies = Agency::whereNull('logo_path')
            ->orWhere('logo_path', '')
            ->orWhere(function($query) {
                $query->whereNotNull('logo_path')
                    ->whereRaw("logo_path NOT LIKE 'agencies/%'");
            })
            ->get();

        if ($agencies->isEmpty()) {
            $this->info('All agencies already have logo images.');
            return 0;
        }

        $bar = $this->output->createProgressBar($agencies->count());
        $bar->start();

        foreach ($agencies as $agency) {
            // Assign a random default logo
            $randomLogo = $this->defaultLogos[array_rand($this->defaultLogos)];
            $agency->logo_path = $randomLogo;
            $agency->save();

            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info('Logo images assigned to ' . $agencies->count() . ' agencies.');

        return 0;
    }

    /**
     * Ensure default logo images exist in storage
     */
    protected function ensureDefaultLogosExist()
    {
        // Agency names corresponding to logo files
        $agencyNames = [
            'Luxury Escorts Agency',
            'VIP Companions',
            'Elite Models Agency',
            'Diamond Escorts',
            'Premium Companions',
            'Royal Escorts Agency',
        ];

        // Create sample default agency logos directly in storage
        foreach ($this->defaultLogos as $index => $logoPath) {
            if (!Storage::disk('public')->exists($logoPath)) {
                // Create a professional-looking logo
                $width = 600;
                $height = 400;
                $image = imagecreatetruecolor($width, $height);

                // Enable alpha blending
                imagealphablending($image, true);
                imagesavealpha($image, true);

                // Create a transparent background
                $transparent = imagecolorallocatealpha($image, 0, 0, 0, 127);
                imagefill($image, 0, 0, $transparent);

                // Create a gradient background
                $this->createGradientBackground($image, $width, $height, $index);

                // Add a decorative element
                $this->addDecorativeElement($image, $width, $height, $index);

                // Add agency name text
                $this->addAgencyText($image, $width, $height, $agencyNames[$index]);

                // Save the image to a temporary file
                $tempFile = tempnam(sys_get_temp_dir(), 'agency_logo');
                imagepng($image, $tempFile, 9); // Use PNG for better quality with transparency
                imagedestroy($image);

                // Copy to storage
                Storage::disk('public')->put($logoPath, file_get_contents($tempFile));
                unlink($tempFile);

                $this->info('Created professional logo: ' . basename($logoPath));
            }
        }
    }

    /**
     * Create a gradient background for the logo
     */
    private function createGradientBackground($image, $width, $height, $index)
    {
        // Different color schemes for variety
        $colorSchemes = [
            // Luxury pink/purple gradient
            [imagecolorallocate($image, 220, 66, 150), imagecolorallocate($image, 100, 43, 115)],
            // Blue/teal gradient
            [imagecolorallocate($image, 41, 128, 185), imagecolorallocate($image, 22, 160, 133)],
            // Dark elegant gradient
            [imagecolorallocate($image, 45, 52, 54), imagecolorallocate($image, 149, 165, 166)],
            // Gold/amber gradient
            [imagecolorallocate($image, 211, 84, 0), imagecolorallocate($image, 243, 156, 18)],
            // Red/pink gradient
            [imagecolorallocate($image, 192, 57, 43), imagecolorallocate($image, 231, 76, 60)],
            // Purple/violet gradient
            [imagecolorallocate($image, 142, 68, 173), imagecolorallocate($image, 155, 89, 182)],
        ];

        $colorIndex = $index % count($colorSchemes);
        $colors = $colorSchemes[$colorIndex];

        // Create gradient effect
        for ($i = 0; $i < $height; $i++) {
            $ratio = $i / $height;
            $r = (int)(($colors[0] >> 16 & 0xFF) * (1 - $ratio) + ($colors[1] >> 16 & 0xFF) * $ratio);
            $g = (int)(($colors[0] >> 8 & 0xFF) * (1 - $ratio) + ($colors[1] >> 8 & 0xFF) * $ratio);
            $b = (int)(($colors[0] & 0xFF) * (1 - $ratio) + ($colors[1] & 0xFF) * $ratio);
            $color = imagecolorallocate($image, $r, $g, $b);
            imageline($image, 0, $i, $width, $i, $color);
        }
    }

    /**
     * Add decorative element to the logo
     */
    private function addDecorativeElement($image, $width, $height, $index)
    {
        // Different decorative elements based on index
        switch ($index % 6) {
            case 0: // Luxury - diamond shape
                $white = imagecolorallocate($image, 255, 255, 255);
                $points = [
                    $width/2, $height/4,
                    $width/2 + 50, $height/2,
                    $width/2, $height*3/4,
                    $width/2 - 50, $height/2,
                ];
                imagefilledpolygon($image, $points, 4, $white);
                break;

            case 1: // VIP - crown
                $gold = imagecolorallocate($image, 255, 215, 0);
                $points = [
                    $width/2 - 60, $height/2,
                    $width/2 - 40, $height/2 - 30,
                    $width/2 - 20, $height/2 - 10,
                    $width/2, $height/2 - 40,
                    $width/2 + 20, $height/2 - 10,
                    $width/2 + 40, $height/2 - 30,
                    $width/2 + 60, $height/2,
                    $width/2 + 60, $height/2 + 10,
                    $width/2 - 60, $height/2 + 10,
                ];
                imagefilledpolygon($image, $points, 9, $gold);
                break;

            case 2: // Elite - star
                $white = imagecolorallocate($image, 255, 255, 255);
                $cx = $width/2;
                $cy = $height/2 - 20;
                $r = 40;
                $points = [];
                for ($i = 0; $i < 10; $i++) {
                    $r2 = $i % 2 == 0 ? $r : $r/2;
                    $points[] = $cx + $r2 * cos(M_PI/2 + $i * 2 * M_PI / 10);
                    $points[] = $cy + $r2 * sin(M_PI/2 + $i * 2 * M_PI / 10);
                }
                imagefilledpolygon($image, $points, 10, $white);
                break;

            case 3: // Diamond - gem shape
                $lightBlue = imagecolorallocate($image, 173, 216, 230);
                $points = [
                    $width/2, $height/2 - 50,
                    $width/2 + 40, $height/2,
                    $width/2, $height/2 + 50,
                    $width/2 - 40, $height/2,
                ];
                imagefilledpolygon($image, $points, 4, $lightBlue);
                break;

            case 4: // Premium - circle
                $gold = imagecolorallocate($image, 255, 215, 0);
                imagefilledellipse($image, $width/2, $height/2 - 20, 80, 80, $gold);
                break;

            case 5: // Royal - shield
                $white = imagecolorallocate($image, 255, 255, 255);
                $points = [
                    $width/2 - 50, $height/2 - 40,
                    $width/2 + 50, $height/2 - 40,
                    $width/2 + 50, $height/2 + 20,
                    $width/2, $height/2 + 60,
                    $width/2 - 50, $height/2 + 20,
                ];
                imagefilledpolygon($image, $points, 5, $white);
                break;
        }
    }

    /**
     * Add agency name text to the logo
     */
    private function addAgencyText($image, $width, $height, $agencyName)
    {
        // Add text
        $white = imagecolorallocate($image, 255, 255, 255);

        // Split agency name into words
        $words = explode(' ', $agencyName);
        $lineHeight = 30;
        $y = $height - (count($words) * $lineHeight) - 20;

        // Add each word on a new line
        foreach ($words as $word) {
            // Use built-in font (no TTF required)
            $font = 5; // Largest built-in font
            $textWidth = imagefontwidth($font) * strlen($word);
            $x = ($width - $textWidth) / 2;

            imagestring($image, $font, $x, $y, $word, $white);
            $y += $lineHeight;
        }
    }
}
