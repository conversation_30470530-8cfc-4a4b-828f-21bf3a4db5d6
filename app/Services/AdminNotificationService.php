<?php

namespace App\Services;

use App\Models\User;
use App\Models\Notification;

class AdminNotificationService
{
    /**
     * Create a notification for all users about an admin action
     *
     * @param string $action The action performed (e.g., 'updated', 'created', 'deleted')
     * @param string $entityType The type of entity affected (e.g., 'user', 'escort', 'agency')
     * @param string $entityName The name of the entity affected
     * @param array|null $additionalData Any additional data to store with the notification
     * @return void
     */
    public function notifyAllUsers(string $action, string $entityType, string $entityName, ?array $additionalData = null): void
    {
        $title = "Admin {$action} {$entityType}";
        $message = "An administrator has {$action} the {$entityType}: {$entityName}";

        $data = [
            'action' => $action,
            'entity_type' => $entityType,
            'entity_name' => $entityName,
        ];

        if ($additionalData) {
            $data = array_merge($data, $additionalData);
        }

        $userIds = User::pluck('id')->toArray();
        $this->createForMultipleUsers($userIds, 'admin_action', $title, $message, $data);
    }

    /**
     * Create a notification for specific user types about an admin action
     *
     * @param array $userTypes Array of user types to notify (e.g., ['escort', 'agency'])
     * @param string $action The action performed
     * @param string $entityType The type of entity affected
     * @param string $entityName The name of the entity affected
     * @param array|null $additionalData Any additional data to store with the notification
     * @return void
     */
    public function notifyUserTypes(array $userTypes, string $action, string $entityType, string $entityName, ?array $additionalData = null): void
    {
        $title = "Admin {$action} {$entityType}";
        $message = "An administrator has {$action} the {$entityType}: {$entityName}";

        $data = [
            'action' => $action,
            'entity_type' => $entityType,
            'entity_name' => $entityName,
        ];

        if ($additionalData) {
            $data = array_merge($data, $additionalData);
        }

        $userIds = User::whereIn('user_type', $userTypes)->pluck('id')->toArray();
        $this->createForMultipleUsers($userIds, 'admin_action', $title, $message, $data);
    }

    /**
     * Create a notification for a specific user about an admin action
     *
     * @param User $user The user to notify
     * @param string $action The action performed
     * @param string $entityType The type of entity affected
     * @param string $entityName The name of the entity affected
     * @param array|null $additionalData Any additional data to store with the notification
     * @return Notification
     */
    public function notifyUser(User $user, string $action, string $entityType, string $entityName, ?array $additionalData = null): Notification
    {
        $title = "Admin {$action} {$entityType}";
        $message = "An administrator has {$action} the {$entityType}: {$entityName}";

        $data = [
            'action' => $action,
            'entity_type' => $entityType,
            'entity_name' => $entityName,
        ];

        if ($additionalData) {
            $data = array_merge($data, $additionalData);
        }

        return $this->create($user, 'admin_action', $title, $message, $data);
    }

    /**
     * Create a system-wide announcement from admin
     *
     * @param string $title The announcement title
     * @param string $message The announcement message
     * @param array|null $additionalData Any additional data to store with the notification
     * @return void
     */
    public function createAnnouncement(string $title, string $message, ?array $additionalData = null): void
    {
        $userIds = User::pluck('id')->toArray();
        $this->createForMultipleUsers($userIds, 'announcement', $title, $message, $additionalData);
    }

    /**
     * Create a new notification for a user.
     *
     * @param User $user
     * @param string $type
     * @param string $title
     * @param string $message
     * @param array|null $data
     * @return Notification
     */
    private function create(User $user, string $type, string $title, string $message, ?array $data = null): Notification
    {
        return Notification::create([
            'user_id' => $user->id,
            'type' => $type,
            'title' => $title,
            'message' => $message,
            'data' => $data,
            'is_read' => false,
        ]);
    }

    /**
     * Create a new notification for multiple users.
     *
     * @param array $userIds
     * @param string $type
     * @param string $title
     * @param string $message
     * @param array|null $data
     * @return void
     */
    private function createForMultipleUsers(array $userIds, string $type, string $title, string $message, ?array $data = null): void
    {
        $notifications = [];

        foreach ($userIds as $userId) {
            $notifications[] = [
                'user_id' => $userId,
                'type' => $type,
                'title' => $title,
                'message' => $message,
                'data' => $data ? json_encode($data) : null,
                'is_read' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        if (!empty($notifications)) {
            Notification::insert($notifications);
        }
    }
}
