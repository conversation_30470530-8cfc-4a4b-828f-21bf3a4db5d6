<?php

namespace App\Services;

class AdvancedSchemaService
{
    /**
     * Generate review schema for escort profiles
     */
    public function generateReviewSchema($escort, $reviews = []): array
    {
        if (empty($reviews)) {
            return [];
        }

        $reviewSchemas = [];
        foreach ($reviews as $review) {
            $reviewSchemas[] = [
                '@type' => 'Review',
                'author' => [
                    '@type' => 'Person',
                    'name' => $review->author_name ?? 'Anonymous'
                ],
                'datePublished' => $review->created_at->toISOString(),
                'reviewBody' => $review->content,
                'reviewRating' => [
                    '@type' => 'Rating',
                    'ratingValue' => $review->rating,
                    'bestRating' => 5,
                    'worstRating' => 1
                ]
            ];
        }

        $averageRating = collect($reviews)->avg('rating');
        $reviewCount = count($reviews);

        return [
            '@context' => 'https://schema.org',
            '@type' => 'Service',
            'name' => $escort->name . ' - Professional Escort Services',
            'description' => 'Professional companion services in ' . ($escort->locations->first()?->name ?? 'Uganda'),
            'provider' => [
                '@type' => 'Person',
                'name' => $escort->name
            ],
            'areaServed' => [
                '@type' => 'City',
                'name' => $escort->locations->first()?->name ?? 'Uganda'
            ],
            'aggregateRating' => [
                '@type' => 'AggregateRating',
                'ratingValue' => round($averageRating, 1),
                'reviewCount' => $reviewCount,
                'bestRating' => 5,
                'worstRating' => 1
            ],
            'review' => $reviewSchemas
        ];
    }

    /**
     * Generate organization schema for the platform
     */
    public function generateOrganizationSchema(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => 'Get Hot Babes',
            'url' => url('/'),
            'logo' => [
                '@type' => 'ImageObject',
                'url' => asset('images/logo.png'),
                'width' => 200,
                'height' => 60
            ],
            'description' => 'Premier escort platform in Uganda connecting clients with verified professional companions.',
            'address' => [
                '@type' => 'PostalAddress',
                'addressCountry' => 'UG',
                'addressRegion' => 'Central Region',
                'addressLocality' => 'Kampala'
            ],
            'areaServed' => [
                [
                    '@type' => 'Country',
                    'name' => 'Uganda'
                ],
                [
                    '@type' => 'AdministrativeArea',
                    'name' => 'East Africa'
                ]
            ],
            'sameAs' => [
                'https://www.facebook.com/gethotbabes',
                'https://www.twitter.com/gethotbabes',
                'https://www.instagram.com/gethotbabes'
            ],
            'contactPoint' => [
                '@type' => 'ContactPoint',
                'contactType' => 'customer service',
                'areaServed' => 'UG',
                'availableLanguage' => ['English', 'Luganda']
            ]
        ];
    }

    /**
     * Generate website schema with search functionality
     */
    public function generateWebsiteSchema(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => 'Get Hot Babes - Professional Escorts Uganda',
            'url' => url('/'),
            'description' => 'Find verified professional escorts and companions in Uganda. Premium escort services in Kampala, Entebbe, Jinja and across East Africa.',
            'inLanguage' => 'en-UG',
            'potentialAction' => [
                [
                    '@type' => 'SearchAction',
                    'target' => [
                        '@type' => 'EntryPoint',
                        'urlTemplate' => url('/escorts?search={search_term_string}')
                    ],
                    'query-input' => 'required name=search_term_string'
                ]
            ],
            'publisher' => [
                '@type' => 'Organization',
                'name' => 'Get Hot Babes',
                'logo' => [
                    '@type' => 'ImageObject',
                    'url' => asset('images/logo.png')
                ]
            ]
        ];
    }

    /**
     * Generate service schema for escort services
     */
    public function generateServiceSchema($escort): array
    {
        $location = $escort->locations->first();
        $services = $escort->services->pluck('name')->toArray();

        return [
            '@context' => 'https://schema.org',
            '@type' => 'Service',
            'name' => 'Professional Escort Services by ' . $escort->name,
            'description' => 'Premium companion services including ' . implode(', ', array_slice($services, 0, 3)),
            'provider' => [
                '@type' => 'Person',
                'name' => $escort->name,
                'image' => $escort->primaryImage ? asset('storage/' . $escort->primaryImage->image_path) : null
            ],
            'areaServed' => $location ? [
                '@type' => 'City',
                'name' => $location->name,
                'containedInPlace' => [
                    '@type' => 'Country',
                    'name' => 'Uganda'
                ]
            ] : null,
            'serviceType' => 'Companion Services',
            'category' => 'Adult Entertainment',
            'offers' => $escort->rates->map(function($rate) {
                return [
                    '@type' => 'Offer',
                    'name' => $rate->duration . ' Service',
                    'description' => 'Professional companion service for ' . $rate->duration,
                    'priceCurrency' => 'UGX',
                    'price' => $rate->min_incall_price ?? $rate->incall_price,
                    'availability' => 'https://schema.org/InStock',
                    'validFrom' => now()->toISOString(),
                    'priceValidUntil' => now()->addMonths(3)->toISOString()
                ];
            })->toArray()
        ];
    }

    /**
     * Generate local business schema for agencies
     */
    public function generateAgencySchema($agency): array
    {
        $location = $agency->locations->first();

        return [
            '@context' => 'https://schema.org',
            '@type' => 'LocalBusiness',
            'name' => $agency->name,
            'description' => $agency->description ?? 'Professional escort agency providing verified companions',
            'url' => route('agencies.show', $agency->slug),
            'image' => $agency->logo ? asset('storage/' . $agency->logo) : null,
            'telephone' => $agency->phone_number,
            'email' => $agency->email,
            'address' => $location ? [
                '@type' => 'PostalAddress',
                'addressLocality' => $location->name,
                'addressRegion' => $location->parent?->name ?? 'Central Region',
                'addressCountry' => 'UG'
            ] : null,
            'geo' => $location ? [
                '@type' => 'GeoCoordinates',
                'latitude' => $location->latitude ?? 0.3476,
                'longitude' => $location->longitude ?? 32.5825
            ] : null,
            'areaServed' => [
                '@type' => 'City',
                'name' => $location?->name ?? 'Kampala'
            ],
            'serviceArea' => [
                '@type' => 'GeoCircle',
                'geoMidpoint' => [
                    '@type' => 'GeoCoordinates',
                    'latitude' => $location->latitude ?? 0.3476,
                    'longitude' => $location->longitude ?? 32.5825
                ],
                'geoRadius' => '50000' // 50km radius
            ],
            'openingHours' => 'Mo-Su 00:00-23:59', // 24/7 availability
            'priceRange' => '$$-$$$'
        ];
    }

    /**
     * Generate collection page schema for listings
     */
    public function generateCollectionPageSchema(string $pageType, $items, string $location = null): array
    {
        $baseUrl = url('/');
        $itemCount = is_countable($items) ? count($items) : $items;

        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'CollectionPage',
            'name' => $this->getCollectionPageTitle($pageType, $location),
            'description' => $this->getCollectionPageDescription($pageType, $location, $itemCount),
            'url' => request()->url(),
            'mainEntity' => [
                '@type' => 'ItemList',
                'numberOfItems' => $itemCount,
                'itemListOrder' => 'https://schema.org/ItemListOrderDescending'
            ],
            'breadcrumb' => [
                '@type' => 'BreadcrumbList',
                'itemListElement' => $this->generateBreadcrumbItems($pageType, $location)
            ]
        ];

        return $schema;
    }

    /**
     * Generate FAQ schema for location pages
     */
    public function generateLocationFaqSchema($location): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'FAQPage',
            'mainEntity' => [
                [
                    '@type' => 'Question',
                    'name' => "How many escorts are available in {$location->name}?",
                    'acceptedAnswer' => [
                        '@type' => 'Answer',
                        'text' => "We have multiple verified professional escorts available in {$location->name}. All profiles are verified for authenticity and safety through our comprehensive screening process."
                    ]
                ],
                [
                    '@type' => 'Question',
                    'name' => "Are the escorts in {$location->name} verified?",
                    'acceptedAnswer' => [
                        '@type' => 'Answer',
                        'text' => "Yes, all escorts listed in {$location->name} go through our strict verification process. We verify identity, authenticity, and professionalism to ensure the highest standards."
                    ]
                ],
                [
                    '@type' => 'Question',
                    'name' => "What services are available in {$location->name}?",
                    'acceptedAnswer' => [
                        '@type' => 'Answer',
                        'text' => "Professional escorts in {$location->name} offer various companion services including dinner dates, social events, business functions, and professional companionship."
                    ]
                ],
                [
                    '@type' => 'Question',
                    'name' => "How do I book an escort in {$location->name}?",
                    'acceptedAnswer' => [
                        '@type' => 'Answer',
                        'text' => "Simply browse our verified escort profiles in {$location->name}, select your preferred companion, and use the contact information provided. All communication is handled directly and discreetly."
                    ]
                ]
            ]
        ];
    }

    /**
     * Helper method to get collection page title
     */
    private function getCollectionPageTitle(string $pageType, string $location = null): string
    {
        switch ($pageType) {
            case 'escorts':
                return $location ? "Professional Escorts in {$location}" : "Professional Escorts in Uganda";
            case 'agencies':
                return $location ? "Escort Agencies in {$location}" : "Escort Agencies in Uganda";
            case 'locations':
                return "Escort Locations in Uganda";
            default:
                return "Get Hot Babes - Professional Escorts Uganda";
        }
    }

    /**
     * Helper method to get collection page description
     */
    private function getCollectionPageDescription(string $pageType, string $location = null, int $count = 0): string
    {
        switch ($pageType) {
            case 'escorts':
                $locationText = $location ? "in {$location}" : "across Uganda";
                return "Find {$count}+ verified professional escorts {$locationText}. Premium companion services with verified profiles and safe platform.";
            case 'agencies':
                $locationText = $location ? "in {$location}" : "across Uganda";
                return "Discover {$count}+ professional escort agencies {$locationText}. Verified agencies providing premium companion services.";
            case 'locations':
                return "Browse escort services by location across Uganda. Find professional companions in major cities and regions.";
            default:
                return "Premier escort platform in Uganda with verified professional companions.";
        }
    }

    /**
     * Helper method to generate breadcrumb items
     */
    private function generateBreadcrumbItems(string $pageType, string $location = null): array
    {
        $items = [
            [
                '@type' => 'ListItem',
                'position' => 1,
                'name' => 'Home',
                'item' => url('/')
            ]
        ];

        if ($pageType === 'escorts') {
            $items[] = [
                '@type' => 'ListItem',
                'position' => 2,
                'name' => 'Escorts',
                'item' => route('escorts.index')
            ];
        } elseif ($pageType === 'agencies') {
            $items[] = [
                '@type' => 'ListItem',
                'position' => 2,
                'name' => 'Agencies',
                'item' => route('agencies.index')
            ];
        }

        if ($location) {
            $items[] = [
                '@type' => 'ListItem',
                'position' => count($items) + 1,
                'name' => $location,
                'item' => request()->url()
            ];
        }

        return $items;
    }
}

// Continue with performance view...
