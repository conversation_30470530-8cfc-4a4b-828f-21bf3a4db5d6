<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AnalyticsService
{
    protected $gaTrackingId;
    protected $gscProperty;

    public function __construct()
    {
        $this->gaTrackingId = env('GOOGLE_ANALYTICS_ID');
        $this->gscProperty = env('GOOGLE_SEARCH_CONSOLE_PROPERTY');
    }

    /**
     * Generate Google Analytics 4 tracking code
     */
    public function getGA4TrackingCode(): string
    {
        if (!$this->gaTrackingId) {
            return '';
        }

        return "
<!-- Google tag (gtag.js) -->
<script async src=\"https://www.googletagmanager.com/gtag/js?id={$this->gaTrackingId}\"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', '{$this->gaTrackingId}', {
    page_title: document.title,
    page_location: window.location.href,
    content_group1: 'Escort Platform',
    content_group2: 'Uganda',
    custom_map: {
      'custom_parameter_1': 'user_type',
      'custom_parameter_2': 'page_category'
    }
  });
</script>";
    }

    /**
     * Track custom events
     */
    public function trackEvent(string $eventName, array $parameters = []): string
    {
        if (!$this->gaTrackingId) {
            return '';
        }

        $params = json_encode($parameters);
        
        return "
<script>
  gtag('event', '{$eventName}', {$params});
</script>";
    }

    /**
     * Track escort profile views
     */
    public function trackEscortView($escort): string
    {
        return $this->trackEvent('view_escort_profile', [
            'escort_id' => $escort->id,
            'escort_name' => $escort->name,
            'location' => $escort->locations->first()?->name ?? 'Unknown',
            'is_verified' => $escort->is_verified,
            'is_featured' => $escort->is_featured,
            'value' => 1
        ]);
    }

    /**
     * Track agency profile views
     */
    public function trackAgencyView($agency): string
    {
        return $this->trackEvent('view_agency_profile', [
            'agency_id' => $agency->id,
            'agency_name' => $agency->name,
            'location' => $agency->locations->first()?->name ?? 'Unknown',
            'escort_count' => $agency->escorts()->count(),
            'value' => 1
        ]);
    }

    /**
     * Track location page views
     */
    public function trackLocationView($location, $escortCount): string
    {
        return $this->trackEvent('view_location_page', [
            'location_id' => $location->id,
            'location_name' => $location->name,
            'location_type' => $location->type,
            'escort_count' => $escortCount,
            'value' => 1
        ]);
    }

    /**
     * Track contact attempts
     */
    public function trackContactAttempt(string $type, $entityId): string
    {
        return $this->trackEvent('contact_attempt', [
            'contact_type' => $type, // 'phone', 'whatsapp', 'email'
            'entity_type' => 'escort', // or 'agency'
            'entity_id' => $entityId,
            'value' => 5 // Higher value for conversion tracking
        ]);
    }

    /**
     * Track search queries
     */
    public function trackSearch(string $query, array $filters = []): string
    {
        return $this->trackEvent('search', [
            'search_term' => $query,
            'filters' => json_encode($filters),
            'value' => 1
        ]);
    }

    /**
     * Generate Google Search Console verification meta tag
     */
    public function getGSCVerificationTag(): string
    {
        $verificationCode = env('GOOGLE_SEARCH_CONSOLE_VERIFICATION');
        
        if (!$verificationCode) {
            return '';
        }

        return "<meta name=\"google-site-verification\" content=\"{$verificationCode}\" />";
    }

    /**
     * Generate Facebook Pixel code
     */
    public function getFacebookPixelCode(): string
    {
        $pixelId = env('FACEBOOK_PIXEL_ID');
        
        if (!$pixelId) {
            return '';
        }

        return "
<!-- Facebook Pixel Code -->
<script>
!function(f,b,e,v,n,t,s)
{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};
if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];
s.parentNode.insertBefore(t,s)}(window,document,'script',
'https://connect.facebook.net/en_US/fbevents.js');
fbq('init', '{$pixelId}');
fbq('track', 'PageView');
</script>
<noscript>
<img height=\"1\" width=\"1\" style=\"display:none\" 
src=\"https://www.facebook.com/tr?id={$pixelId}&ev=PageView&noscript=1\" />
</noscript>
<!-- End Facebook Pixel Code -->";
    }

    /**
     * Generate structured data for analytics
     */
    public function getAnalyticsStructuredData(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => 'Get Hot Babes',
            'url' => url('/'),
            'potentialAction' => [
                '@type' => 'SearchAction',
                'target' => [
                    '@type' => 'EntryPoint',
                    'urlTemplate' => url('/escorts?search={search_term_string}')
                ],
                'query-input' => 'required name=search_term_string'
            ],
            'publisher' => [
                '@type' => 'Organization',
                'name' => 'Get Hot Babes',
                'url' => url('/'),
                'logo' => [
                    '@type' => 'ImageObject',
                    'url' => asset('images/logo.png')
                ]
            ]
        ];
    }

    /**
     * Track conversion events
     */
    public function trackConversion(string $type, float $value = 0): string
    {
        $events = [];
        
        // Google Analytics conversion
        if ($this->gaTrackingId) {
            $events[] = "gtag('event', 'conversion', {
                'send_to': '{$this->gaTrackingId}',
                'event_category': 'engagement',
                'event_label': '{$type}',
                'value': {$value}
            });";
        }

        // Facebook Pixel conversion
        $pixelId = env('FACEBOOK_PIXEL_ID');
        if ($pixelId) {
            $events[] = "fbq('track', 'Lead', {
                'content_category': '{$type}',
                'value': {$value},
                'currency': 'UGX'
            });";
        }

        return empty($events) ? '' : '<script>' . implode("\n", $events) . '</script>';
    }

    /**
     * Get heat map tracking code (Hotjar)
     */
    public function getHeatMapCode(): string
    {
        $hotjarId = env('HOTJAR_ID');
        
        if (!$hotjarId) {
            return '';
        }

        return "
<!-- Hotjar Tracking Code -->
<script>
    (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:{$hotjarId},hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
</script>";
    }

    /**
     * Generate performance monitoring code
     */
    public function getPerformanceMonitoringCode(): string
    {
        return "
<script>
// Core Web Vitals monitoring
function sendToAnalytics(metric) {
    gtag('event', metric.name, {
        event_category: 'Web Vitals',
        event_label: metric.id,
        value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
        non_interaction: true,
    });
}

// Load web-vitals library
import('https://unpkg.com/web-vitals@3/dist/web-vitals.js').then(({getCLS, getFID, getFCP, getLCP, getTTFB}) => {
    getCLS(sendToAnalytics);
    getFID(sendToAnalytics);
    getFCP(sendToAnalytics);
    getLCP(sendToAnalytics);
    getTTFB(sendToAnalytics);
});

// Page load time tracking
window.addEventListener('load', function() {
    const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
    gtag('event', 'page_load_time', {
        event_category: 'Performance',
        value: loadTime,
        non_interaction: true
    });
});
</script>";
    }

    /**
     * Get all analytics codes combined
     */
    public function getAllAnalyticsCodes(): string
    {
        $codes = [
            $this->getGA4TrackingCode(),
            $this->getFacebookPixelCode(),
            $this->getHeatMapCode(),
            $this->getPerformanceMonitoringCode()
        ];

        return implode("\n", array_filter($codes));
    }
}
