<?php

namespace App\Services;

use App\Models\User;
use App\Models\Notification;

class NotificationService
{
    /**
     * Create a new notification for a user.
     *
     * @param User $user
     * @param string $type
     * @param string $title
     * @param string $message
     * @param array|null $data
     * @return Notification
     */
    public function create(User $user, string $type, string $title, string $message, ?array $data = null): Notification
    {
        return Notification::create([
            'user_id' => $user->id,
            'type' => $type,
            'title' => $title,
            'message' => $message,
            'data' => $data,
            'is_read' => false,
        ]);
    }

    /**
     * Create a new notification for multiple users.
     *
     * @param array $userIds
     * @param string $type
     * @param string $title
     * @param string $message
     * @param array|null $data
     * @return void
     */
    public function createForMultipleUsers(array $userIds, string $type, string $title, string $message, ?array $data = null): void
    {
        $notifications = [];

        foreach ($userIds as $userId) {
            $notifications[] = [
                'user_id' => $userId,
                'type' => $type,
                'title' => $title,
                'message' => $message,
                'data' => $data ? json_encode($data) : null,
                'is_read' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        if (!empty($notifications)) {
            Notification::insert($notifications);
        }
    }

    /**
     * Create a new notification for all users of a specific type.
     *
     * @param string $userType
     * @param string $type
     * @param string $title
     * @param string $message
     * @param array|null $data
     * @return void
     */
    public function createForUserType(string $userType, string $type, string $title, string $message, ?array $data = null): void
    {
        $userIds = User::where('user_type', $userType)->pluck('id')->toArray();
        $this->createForMultipleUsers($userIds, $type, $title, $message, $data);
    }

    /**
     * Create a new notification for all users.
     *
     * @param string $type
     * @param string $title
     * @param string $message
     * @param array|null $data
     * @return void
     */
    public function createForAllUsers(string $type, string $title, string $message, ?array $data = null): void
    {
        $userIds = User::pluck('id')->toArray();
        $this->createForMultipleUsers($userIds, $type, $title, $message, $data);
    }

    /**
     * Create a notification for specific user types about an action
     *
     * @param array $userTypes Array of user types to notify (e.g., ['admin', 'escort'])
     * @param string $action The action performed
     * @param string $entityType The type of entity affected
     * @param string $entityName The name of the entity affected
     * @param array|null $additionalData Any additional data to store with the notification
     * @return void
     */
    public function notifyUserTypes(array $userTypes, string $action, string $entityType, string $entityName, ?array $additionalData = null): void
    {
        // Create appropriate title and message based on entity type
        if (str_contains($entityType, 'status_request')) {
            $requestType = $additionalData['request_type'] ?? 'status';
            $title = "New " . ucfirst($requestType) . " Request";
            $message = $entityName . " has requested " . $requestType . " status.";
        } else {
            $title = ucfirst($action) . " " . str_replace('_', ' ', $entityType);
            $message = "A new " . str_replace('_', ' ', $entityType) . " has been " . $action . " by " . $entityName;
        }

        $data = [
            'action' => $action,
            'entity_type' => $entityType,
            'entity_name' => $entityName,
        ];

        if ($additionalData) {
            $data = array_merge($data, $additionalData);
        }

        $userIds = User::whereIn('user_type', $userTypes)->pluck('id')->toArray();
        $this->createForMultipleUsers($userIds, 'status_request', $title, $message, $data);
    }
}
