<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\Setting;

class ContactFormMail extends Mailable
{
    use Queueable, SerializesModels;

    public $data;
    public $settings;

    /**
     * Create a new message instance.
     */
    public function __construct(array $data)
    {
        $this->data = $data;

        // Get settings for the email template
        if (class_exists(Setting::class) && \Illuminate\Support\Facades\Schema::hasTable('settings')) {
            $this->settings = Setting::where('is_public', true)->pluck('value', 'key')->toArray();
        } else {
            $this->settings = [
                'site_name' => 'Get Hot Babes',
            ];
        }
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $siteName = $this->settings['site_name'] ?? 'Get Hot Babes';

        return new Envelope(
            subject: "[$siteName] Contact Form: " . $this->data['subject'],
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.contact-form',
            with: [
                'settings' => $this->settings,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
