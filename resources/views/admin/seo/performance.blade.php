<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Performance Monitoring') }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('admin.seo.dashboard') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Dashboard
                </a>
                <form action="{{ route('admin.seo.clear-caches') }}" method="POST" class="inline">
                    @csrf
                    <button type="submit" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                        Clear Caches
                    </button>
                </form>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Core Web Vitals -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Core Web Vitals</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- LCP -->
                        <div class="p-4 border border-gray-200 rounded-lg">
                            <div class="flex justify-between items-center mb-2">
                                <h4 class="font-medium text-gray-900">Largest Contentful Paint</h4>
                                <span class="px-2 py-1 text-xs font-medium rounded-full {{ $performanceData['core_web_vitals']['lcp']['status'] === 'good' ? 'bg-green-100 text-green-800' : ($performanceData['core_web_vitals']['lcp']['status'] === 'unknown' ? 'bg-gray-100 text-gray-800' : 'bg-red-100 text-red-800') }}">
                                    {{ ucfirst($performanceData['core_web_vitals']['lcp']['status']) }}
                                </span>
                            </div>
                            <div class="flex items-end space-x-2">
                                @if($performanceData['core_web_vitals']['lcp']['value'] !== null)
                                    <span class="text-2xl font-bold text-gray-900">{{ $performanceData['core_web_vitals']['lcp']['value'] }}s</span>
                                    <span class="text-sm text-gray-500">/ {{ $performanceData['core_web_vitals']['lcp']['threshold'] }}s</span>
                                @else
                                    <span class="text-lg text-gray-400">Not measured</span>
                                @endif
                            </div>
                            @if($performanceData['core_web_vitals']['lcp']['value'] !== null)
                                <div class="mt-2 w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-{{ $performanceData['core_web_vitals']['lcp']['status'] === 'good' ? 'green' : 'red' }}-500 h-2 rounded-full"
                                         style="width: {{ min(100, ($performanceData['core_web_vitals']['lcp']['value'] / $performanceData['core_web_vitals']['lcp']['threshold']) * 100) }}%"></div>
                                </div>
                            @endif
                        </div>

                        <!-- FID -->
                        <div class="p-4 border border-gray-200 rounded-lg">
                            <div class="flex justify-between items-center mb-2">
                                <h4 class="font-medium text-gray-900">First Input Delay</h4>
                                <span class="px-2 py-1 text-xs font-medium rounded-full {{ $performanceData['core_web_vitals']['fid']['status'] === 'good' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ ucfirst($performanceData['core_web_vitals']['fid']['status']) }}
                                </span>
                            </div>
                            <div class="flex items-end space-x-2">
                                <span class="text-2xl font-bold text-gray-900">{{ $performanceData['core_web_vitals']['fid']['value'] }}ms</span>
                                <span class="text-sm text-gray-500">/ {{ $performanceData['core_web_vitals']['fid']['threshold'] }}ms</span>
                            </div>
                            <div class="mt-2 w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-{{ $performanceData['core_web_vitals']['fid']['status'] === 'good' ? 'green' : 'red' }}-500 h-2 rounded-full"
                                     style="width: {{ min(100, ($performanceData['core_web_vitals']['fid']['value'] / $performanceData['core_web_vitals']['fid']['threshold']) * 100) }}%"></div>
                            </div>
                        </div>

                        <!-- CLS -->
                        <div class="p-4 border border-gray-200 rounded-lg">
                            <div class="flex justify-between items-center mb-2">
                                <h4 class="font-medium text-gray-900">Cumulative Layout Shift</h4>
                                <span class="px-2 py-1 text-xs font-medium rounded-full {{ $performanceData['core_web_vitals']['cls']['status'] === 'good' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ ucfirst($performanceData['core_web_vitals']['cls']['status']) }}
                                </span>
                            </div>
                            <div class="flex items-end space-x-2">
                                <span class="text-2xl font-bold text-gray-900">{{ $performanceData['core_web_vitals']['cls']['value'] }}</span>
                                <span class="text-sm text-gray-500">/ {{ $performanceData['core_web_vitals']['cls']['threshold'] }}</span>
                            </div>
                            <div class="mt-2 w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-{{ $performanceData['core_web_vitals']['cls']['status'] === 'good' ? 'green' : 'red' }}-500 h-2 rounded-full"
                                     style="width: {{ min(100, ($performanceData['core_web_vitals']['cls']['value'] / $performanceData['core_web_vitals']['cls']['threshold']) * 100) }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page Speed Scores -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">PageSpeed Insights</h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                    </svg>
                                    <span class="text-sm text-gray-700">Mobile</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-{{ $performanceData['page_speed']['mobile']['status'] === 'excellent' ? 'green' : ($performanceData['page_speed']['mobile']['status'] === 'good' ? 'yellow' : 'red') }}-500 h-2 rounded-full"
                                             style="width: {{ $performanceData['page_speed']['mobile']['score'] }}%"></div>
                                    </div>
                                    <span class="text-sm font-medium">{{ $performanceData['page_speed']['mobile']['score'] }}</span>
                                </div>
                            </div>

                            <div class="flex justify-between items-center">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                    <span class="text-sm text-gray-700">Desktop</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-{{ $performanceData['page_speed']['desktop']['status'] === 'excellent' ? 'green' : ($performanceData['page_speed']['desktop']['status'] === 'good' ? 'yellow' : 'red') }}-500 h-2 rounded-full"
                                             style="width: {{ $performanceData['page_speed']['desktop']['score'] }}%"></div>
                                    </div>
                                    <span class="text-sm font-medium">{{ $performanceData['page_speed']['desktop']['score'] }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <a href="https://pagespeed.web.dev/report?url={{ urlencode(url('/')) }}" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                Test with PageSpeed Insights →
                            </a>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Optimization Status</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-700">Images Optimized</span>
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: {{ $performanceData['optimization_status']['images_optimized'] }}%"></div>
                                    </div>
                                    <span class="text-sm font-medium">{{ $performanceData['optimization_status']['images_optimized'] }}%</span>
                                </div>
                            </div>

                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-700">CSS Minified</span>
                                <span class="w-3 h-3 bg-{{ $performanceData['optimization_status']['css_minified'] ? 'green' : 'red' }}-500 rounded-full"></span>
                            </div>

                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-700">JS Minified</span>
                                <span class="w-3 h-3 bg-{{ $performanceData['optimization_status']['js_minified'] ? 'green' : 'red' }}-500 rounded-full"></span>
                            </div>

                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-700">Gzip Enabled</span>
                                <span class="w-3 h-3 bg-{{ $performanceData['optimization_status']['gzip_enabled'] ? 'green' : 'red' }}-500 rounded-full"></span>
                            </div>

                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-700">CDN Enabled</span>
                                <span class="w-3 h-3 bg-{{ $performanceData['optimization_status']['cdn_enabled'] ? 'green' : 'red' }}-500 rounded-full"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Recommendations -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Performance Recommendations</h3>
                    <div class="space-y-4">
                        <div class="flex items-start p-4 bg-green-50 border border-green-200 rounded-lg">
                            <svg class="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <div>
                                <h4 class="font-medium text-green-900">Excellent Core Web Vitals</h4>
                                <p class="text-sm text-green-700 mt-1">Your site meets all Core Web Vitals thresholds. Keep monitoring to maintain performance.</p>
                            </div>
                        </div>

                        <div class="flex items-start p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <svg class="w-5 h-5 text-yellow-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                            <div>
                                <h4 class="font-medium text-yellow-900">Enable CDN</h4>
                                <p class="text-sm text-yellow-700 mt-1">Consider enabling a CDN to improve global performance and reduce server load.</p>
                            </div>
                        </div>

                        <div class="flex items-start p-4 bg-blue-50 border border-blue-200 rounded-lg">
                            <svg class="w-5 h-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                            </svg>
                            <div>
                                <h4 class="font-medium text-blue-900">Optimize Remaining Images</h4>
                                <p class="text-sm text-blue-700 mt-1">{{ 100 - $performanceData['optimization_status']['images_optimized'] }}% of images can still be optimized for better performance.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Tools -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Performance Testing Tools</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <a href="https://pagespeed.web.dev/report?url={{ urlencode(url('/')) }}" target="_blank" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                            <div class="text-center">
                                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                    </svg>
                                </div>
                                <h4 class="font-medium text-gray-900">PageSpeed Insights</h4>
                                <p class="text-sm text-gray-500 mt-1">Google's performance tool</p>
                            </div>
                        </a>

                        <a href="https://gtmetrix.com/?url={{ urlencode(url('/')) }}" target="_blank" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                            <div class="text-center">
                                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M13 3a9 9 0 0 0-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42A8.954 8.954 0 0 0 13 21a9 9 0 0 0 0-18zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z"/>
                                    </svg>
                                </div>
                                <h4 class="font-medium text-gray-900">GTmetrix</h4>
                                <p class="text-sm text-gray-500 mt-1">Detailed performance analysis</p>
                            </div>
                        </a>

                        <a href="https://www.webpagetest.org/?url={{ urlencode(url('/')) }}" target="_blank" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                            <div class="text-center">
                                <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                </div>
                                <h4 class="font-medium text-gray-900">WebPageTest</h4>
                                <p class="text-sm text-gray-500 mt-1">Advanced testing options</p>
                            </div>
                        </a>

                        <a href="https://web.dev/measure/" target="_blank" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                            <div class="text-center">
                                <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M9 11H7v9a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3V5a2 2 0 0 0-2-2H9a2 2 0 0 0-2 2v6zm0-6h3v2H9V5z"/>
                                    </svg>
                                </div>
                                <h4 class="font-medium text-gray-900">Web.dev Measure</h4>
                                <p class="text-sm text-gray-500 mt-1">Lighthouse audit tool</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
