<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Analytics & Tracking') }}
            </h2>
            <div class="flex space-x-3">
                <a href="{{ route('admin.analytics.config') }}" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Configure Analytics
                </a>
                <a href="{{ route('admin.seo.dashboard') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Dashboard
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Analytics Configuration -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Analytics Configuration</h3>
                    @php
                        $dbGoogleAnalytics = \App\Models\Setting::getValue('google_analytics_id');
                        $dbGoogleSearchConsole = \App\Models\Setting::getValue('google_search_console_verification');
                        $dbFacebookPixel = \App\Models\Setting::getValue('facebook_pixel_id');
                        $dbHotjar = \App\Models\Setting::getValue('hotjar_id');
                        $analyticsEnabled = \App\Models\Setting::getValue('analytics_enabled', false);
                    @endphp

                    <div class="mb-4 p-4 {{ $analyticsEnabled ? 'bg-green-50 border-green-200' : 'bg-yellow-50 border-yellow-200' }} border rounded-lg">
                        <div class="flex items-center">
                            <div class="w-4 h-4 {{ $analyticsEnabled ? 'bg-green-500' : 'bg-yellow-500' }} rounded-full mr-3"></div>
                            <span class="font-medium">Analytics Tracking: {{ $analyticsEnabled ? 'Enabled' : 'Disabled' }}</span>
                            @if(!$analyticsEnabled)
                                <a href="{{ route('admin.analytics.config') }}" class="ml-4 text-sm text-blue-600 underline">Enable Now</a>
                            @endif
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="p-4 border border-gray-200 rounded-lg">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-gray-900">Google Analytics</h4>
                                <span class="w-3 h-3 bg-{{ $dbGoogleAnalytics ? 'green' : 'red' }}-500 rounded-full"></span>
                            </div>
                            <p class="text-sm text-gray-600">
                                {{ $dbGoogleAnalytics ? 'ID: ' . $dbGoogleAnalytics : 'Not configured' }}
                            </p>
                        </div>

                        <div class="p-4 border border-gray-200 rounded-lg">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-gray-900">Search Console</h4>
                                <span class="w-3 h-3 bg-{{ $dbGoogleSearchConsole ? 'green' : 'red' }}-500 rounded-full"></span>
                            </div>
                            <p class="text-sm text-gray-600">
                                {{ $dbGoogleSearchConsole ? 'Verified' : 'Not configured' }}
                            </p>
                        </div>

                        <div class="p-4 border border-gray-200 rounded-lg">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-gray-900">Facebook Pixel</h4>
                                <span class="w-3 h-3 bg-{{ $dbFacebookPixel ? 'green' : 'red' }}-500 rounded-full"></span>
                            </div>
                            <p class="text-sm text-gray-600">
                                {{ $dbFacebookPixel ? 'ID: ' . $dbFacebookPixel : 'Not configured' }}
                            </p>
                        </div>

                        <div class="p-4 border border-gray-200 rounded-lg">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-gray-900">Hotjar</h4>
                                <span class="w-3 h-3 bg-{{ $dbHotjar ? 'green' : 'red' }}-500 rounded-full"></span>
                            </div>
                            <p class="text-sm text-gray-600">
                                {{ $dbHotjar ? 'ID: ' . $dbHotjar : 'Not configured' }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Traffic Overview -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Traffic Sources (Last 30 Days)</h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-green-500 rounded mr-3"></div>
                                    <span class="text-sm text-gray-700">Organic Search</span>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium">{{ number_format($mockAnalyticsData['sessions']['organic']) }}</p>
                                    <p class="text-xs text-gray-500">{{ $mockAnalyticsData['sessions']['total'] > 0 ? round(($mockAnalyticsData['sessions']['organic'] / $mockAnalyticsData['sessions']['total']) * 100, 1) : 0 }}%</p>
                                </div>
                            </div>

                            <div class="flex justify-between items-center">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-blue-500 rounded mr-3"></div>
                                    <span class="text-sm text-gray-700">Direct</span>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium">{{ number_format($mockAnalyticsData['sessions']['direct']) }}</p>
                                    <p class="text-xs text-gray-500">{{ $mockAnalyticsData['sessions']['total'] > 0 ? round(($mockAnalyticsData['sessions']['direct'] / $mockAnalyticsData['sessions']['total']) * 100, 1) : 0 }}%</p>
                                </div>
                            </div>

                            <div class="flex justify-between items-center">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-purple-500 rounded mr-3"></div>
                                    <span class="text-sm text-gray-700">Referral</span>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium">{{ number_format($mockAnalyticsData['sessions']['referral']) }}</p>
                                    <p class="text-xs text-gray-500">{{ $mockAnalyticsData['sessions']['total'] > 0 ? round(($mockAnalyticsData['sessions']['referral'] / $mockAnalyticsData['sessions']['total']) * 100, 1) : 0 }}%</p>
                                </div>
                            </div>

                            <div class="flex justify-between items-center">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-pink-500 rounded mr-3"></div>
                                    <span class="text-sm text-gray-700">Social</span>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium">{{ number_format($mockAnalyticsData['sessions']['social']) }}</p>
                                    <p class="text-xs text-gray-500">{{ $mockAnalyticsData['sessions']['total'] > 0 ? round(($mockAnalyticsData['sessions']['social'] / $mockAnalyticsData['sessions']['total']) * 100, 1) : 0 }}%</p>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-900">Total Sessions</span>
                                <span class="text-lg font-bold text-gray-900">{{ number_format($mockAnalyticsData['sessions']['total']) }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Conversion Tracking</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center p-4 bg-green-50 rounded-lg">
                                <p class="text-2xl font-bold text-green-600">{{ $mockAnalyticsData['conversions']['phone_calls'] }}</p>
                                <p class="text-sm text-gray-600">Phone Calls</p>
                            </div>
                            <div class="text-center p-4 bg-blue-50 rounded-lg">
                                <p class="text-2xl font-bold text-blue-600">{{ $mockAnalyticsData['conversions']['whatsapp_clicks'] }}</p>
                                <p class="text-sm text-gray-600">WhatsApp Clicks</p>
                            </div>
                            <div class="text-center p-4 bg-purple-50 rounded-lg">
                                <p class="text-2xl font-bold text-purple-600">{{ $mockAnalyticsData['conversions']['email_contacts'] }}</p>
                                <p class="text-sm text-gray-600">Email Contacts</p>
                            </div>
                            <div class="text-center p-4 bg-yellow-50 rounded-lg">
                                <p class="text-2xl font-bold text-yellow-600">{{ number_format($mockAnalyticsData['conversions']['profile_views']) }}</p>
                                <p class="text-sm text-gray-600">Profile Views</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Pages -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Top Performing Pages</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Page</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Page Views</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bounce Rate</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($mockAnalyticsData['top_pages'] as $page)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {{ $page['page'] }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ number_format($page['views']) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="px-2 py-1 text-xs font-medium rounded-full {{ $page['bounce_rate'] < 40 ? 'bg-green-100 text-green-800' : ($page['bounce_rate'] < 60 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                                            {{ $page['bounce_rate'] }}%
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <div class="bg-blue-500 h-2 rounded-full" style="width: {{ min(100, ($page['views'] / 6000) * 100) }}%"></div>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Top Keywords -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Top Search Keywords</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Keyword</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clicks</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Impressions</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CTR</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($mockAnalyticsData['top_keywords'] as $keyword)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {{ $keyword['keyword'] }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ number_format($keyword['clicks']) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ number_format($keyword['impressions']) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        @php
                                            $ctr = $keyword['impressions'] > 0 ? round(($keyword['clicks'] / $keyword['impressions']) * 100, 1) : 0;
                                        @endphp
                                        <span class="px-2 py-1 text-xs font-medium rounded-full {{ $ctr > 5 ? 'bg-green-100 text-green-800' : ($ctr > 2 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                                            {{ $ctr }}%
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Setup Instructions -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="text-lg font-medium text-blue-900 mb-4">Analytics Setup</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-medium text-blue-800 mb-2">Configuration</h4>
                        <div class="text-sm text-blue-700 space-y-2">
                            <p>Configure all analytics services through the dashboard:</p>
                            <a href="{{ route('admin.analytics.config') }}" class="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                                Configure Analytics
                            </a>
                            <p class="text-xs">No need to edit environment files manually!</p>
                        </div>
                    </div>
                    <div>
                        <h4 class="font-medium text-blue-800 mb-2">Service Links</h4>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• <a href="https://analytics.google.com" target="_blank" class="underline">Google Analytics</a></li>
                            <li>• <a href="https://search.google.com/search-console" target="_blank" class="underline">Google Search Console</a></li>
                            <li>• <a href="https://business.facebook.com/events_manager" target="_blank" class="underline">Facebook Events Manager</a></li>
                            <li>• <a href="https://www.hotjar.com" target="_blank" class="underline">Hotjar Dashboard</a></li>
                            <li>• <a href="https://www.bing.com/webmasters" target="_blank" class="underline">Bing Webmaster Tools</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
