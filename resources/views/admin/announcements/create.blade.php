<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Create New Announcement') }}
            </h2>
            <a href="{{ route('dashboard') }}" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                {{ __('Back to Dashboard') }}
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <div class="p-4 sm:p-8 bg-white shadow sm:rounded-lg">
                <div class="max-w-xl">
                    <section>
                        <header>
                            <h2 class="text-lg font-medium text-gray-900">
                                {{ __('Announcement Information') }}
                            </h2>

                            <p class="mt-1 text-sm text-gray-600">
                                {{ __('Create a new announcement to notify users.') }}
                            </p>
                        </header>

                        <form method="post" action="{{ route('admin.announcements.store') }}" class="mt-6 space-y-6">
                            @csrf

                            <div>
                                <x-input-label for="title" :value="__('Announcement Title')" />
                                <x-text-input id="title" name="title" type="text" class="mt-1 block w-full" :value="old('title')" required autofocus />
                                <x-input-error class="mt-2" :messages="$errors->get('title')" />
                            </div>

                            <div>
                                <x-input-label for="message" :value="__('Announcement Message')" />
                                <textarea id="message" name="message" rows="4" class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm" required>{{ old('message') }}</textarea>
                                <x-input-error class="mt-2" :messages="$errors->get('message')" />
                            </div>

                            <div>
                                <x-input-label :value="__('Send To')" />
                                <div class="mt-2 space-y-2">
                                    <div class="flex items-center">
                                        <input id="all_users" name="send_to" type="radio" value="all" class="h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500" checked>
                                        <label for="all_users" class="ml-2 block text-sm text-gray-900">All Users</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input id="specific_users" name="send_to" type="radio" value="specific" class="h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500">
                                        <label for="specific_users" class="ml-2 block text-sm text-gray-900">Specific User Types</label>
                                    </div>
                                </div>
                            </div>

                            <div id="user_types_container" class="hidden">
                                <x-input-label :value="__('User Types')" />
                                <div class="mt-2 space-y-2">
                                    <div class="flex items-center">
                                        <input id="admin_type" name="user_types[]" type="checkbox" value="admin" class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                                        <label for="admin_type" class="ml-2 block text-sm text-gray-900">Administrators</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input id="escort_type" name="user_types[]" type="checkbox" value="escort" class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                                        <label for="escort_type" class="ml-2 block text-sm text-gray-900">Escorts</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input id="agency_type" name="user_types[]" type="checkbox" value="agency" class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                                        <label for="agency_type" class="ml-2 block text-sm text-gray-900">Agencies</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input id="regular_type" name="user_types[]" type="checkbox" value="regular" class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                                        <label for="regular_type" class="ml-2 block text-sm text-gray-900">Regular Users</label>
                                    </div>
                                </div>
                            </div>

                            <div class="flex items-center gap-4">
                                <x-primary-button>{{ __('Send Announcement') }}</x-primary-button>
                            </div>
                        </form>
                    </section>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const allUsersRadio = document.getElementById('all_users');
            const specificUsersRadio = document.getElementById('specific_users');
            const userTypesContainer = document.getElementById('user_types_container');

            function toggleUserTypes() {
                if (specificUsersRadio.checked) {
                    userTypesContainer.classList.remove('hidden');
                } else {
                    userTypesContainer.classList.add('hidden');
                }
            }

            allUsersRadio.addEventListener('change', toggleUserTypes);
            specificUsersRadio.addEventListener('change', toggleUserTypes);
        });
    </script>
</x-app-layout>
