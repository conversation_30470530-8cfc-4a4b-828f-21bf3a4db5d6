<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Manage Escorts') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <div class="p-4 sm:p-8 bg-white shadow sm:rounded-lg">
                <div>
                    @if(session('success'))
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                            <span class="block sm:inline">{{ session('success') }}</span>
                        </div>
                    @endif

                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-lg font-medium text-gray-900">
                            {{ __('All Escorts') }}
                        </h2>
                        <a href="{{ route('admin.escorts.create') }}" class="inline-flex items-center px-4 py-2 bg-pink-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-pink-700 focus:bg-pink-700 active:bg-pink-900 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            {{ __('Add New Escort') }}
                        </a>
                    </div>

                    <div class="mb-6">
                        <form action="{{ route('admin.escorts') }}" method="GET" class="flex flex-wrap gap-4">
                            <div>
                                <x-input-label for="gender" :value="__('Gender')" />
                                <select id="gender" name="gender" class="mt-1 block w-full border-gray-300 focus:border-pink-500 focus:ring-pink-500 rounded-md shadow-sm">
                                    <option value="">All Genders</option>
                                    <option value="female" {{ request('gender') === 'female' ? 'selected' : '' }}>Female</option>
                                    <option value="male" {{ request('gender') === 'male' ? 'selected' : '' }}>Male</option>
                                    <option value="couple" {{ request('gender') === 'couple' ? 'selected' : '' }}>Couple</option>
                                    <option value="gay" {{ request('gender') === 'gay' ? 'selected' : '' }}>Gay</option>
                                    <option value="transsexual" {{ request('gender') === 'transsexual' ? 'selected' : '' }}>Transsexual</option>
                                </select>
                            </div>

                            <div>
                                <x-input-label for="is_verified" :value="__('Verification')" />
                                <select id="is_verified" name="is_verified" class="mt-1 block w-full border-gray-300 focus:border-pink-500 focus:ring-pink-500 rounded-md shadow-sm">
                                    <option value="">All</option>
                                    <option value="1" {{ request('is_verified') === '1' ? 'selected' : '' }}>Verified</option>
                                    <option value="0" {{ request('is_verified') === '0' ? 'selected' : '' }}>Not Verified</option>
                                </select>
                            </div>

                            <div>
                                <x-input-label for="is_featured" :value="__('Featured')" />
                                <select id="is_featured" name="is_featured" class="mt-1 block w-full border-gray-300 focus:border-pink-500 focus:ring-pink-500 rounded-md shadow-sm">
                                    <option value="">All</option>
                                    <option value="1" {{ request('is_featured') === '1' ? 'selected' : '' }}>Featured</option>
                                    <option value="0" {{ request('is_featured') === '0' ? 'selected' : '' }}>Not Featured</option>
                                </select>
                            </div>

                            <div>
                                <x-input-label for="agency_id" :value="__('Agency')" />
                                <select id="agency_id" name="agency_id" class="mt-1 block w-full border-gray-300 focus:border-pink-500 focus:ring-pink-500 rounded-md shadow-sm">
                                    <option value="">All Agencies</option>
                                    <option value="independent" {{ request('agency_id') === 'independent' ? 'selected' : '' }}>Independent Only</option>
                                    @foreach($agencies as $agency)
                                        <option value="{{ $agency->id }}" {{ request('agency_id') == $agency->id ? 'selected' : '' }}>{{ $agency->name }}</option>
                                    @endforeach
                                </select>
                            </div>

                            <div>
                                <x-input-label for="search" :value="__('Search')" />
                                <x-text-input id="search" name="search" type="text" class="mt-1 block w-full" :value="request('search')" placeholder="Name or username" />
                            </div>

                            <div class="flex items-end">
                                <x-primary-button>{{ __('Filter') }}</x-primary-button>
                            </div>
                        </form>
                    </div>

                    <div class="overflow-x-auto responsive-table">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Escort</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Agency</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Views</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($escorts as $escort)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    @if($escort->images->count() > 0)
                                                        <img class="h-10 w-10 rounded-full object-cover" src="{{ asset('storage/' . $escort->images->first()->path) }}" alt="{{ $escort->name }}">
                                                    @else
                                                        <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                                            <span class="text-gray-500 text-xs">No img</span>
                                                        </div>
                                                    @endif
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">{{ $escort->name }}</div>
                                                    <div class="text-sm text-gray-500">{{ ucfirst($escort->gender) }} • {{ $escort->age }} years</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            @if($escort->user)
                                                {{ $escort->user->username }}<br>
                                                <span class="text-xs">{{ $escort->user->email }}</span>
                                            @else
                                                <span class="text-red-500">No user</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            @if($escort->agency)
                                                {{ $escort->agency->name }}
                                            @else
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Independent</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex flex-wrap gap-1">
                                                @if($escort->is_verified)
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Verified</span>
                                                @endif
                                                @if($escort->is_featured)
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">Featured</span>
                                                @endif
                                                @if($escort->is_premium)
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Premium</span>
                                                @endif
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $escort->profile_views }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <a href="{{ route('escorts.show', $escort->slug) }}" class="text-blue-600 hover:text-blue-900 mr-3">View</a>
                                            <a href="{{ route('admin.escorts.edit', $escort->id) }}" class="text-indigo-600 hover:text-indigo-900 mr-3">Edit</a>
                                            <form action="{{ route('admin.escorts.destroy', $escort->id) }}" method="POST" class="inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="text-red-600 hover:text-red-900" data-confirm="Are you sure you want to delete this escort?">Delete</button>
                                            </form>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-6">
                        {{ $escorts->links() }}
                    </div>
                </div>
            </div>

            <div class="p-4 sm:p-8 bg-white shadow sm:rounded-lg">
                <div class="max-w-xl">
                    <div class="flex justify-between items-center">
                        <h2 class="text-lg font-medium text-gray-900">
                            {{ __('Back to Dashboard') }}
                        </h2>
                        <a href="{{ route('dashboard') }}" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            {{ __('Dashboard') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
