<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Create New Escort') }}
            </h2>
            <a href="{{ route('admin.escorts') }}" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                {{ __('Back to Escorts') }}
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <div class="p-4 sm:p-8 bg-white shadow sm:rounded-lg">
                <div class="max-w-xl">
                    <section>
                        <header>
                            <h2 class="text-lg font-medium text-gray-900">
                                {{ __('Escort Information') }}
                            </h2>

                            <p class="mt-1 text-sm text-gray-600">
                                {{ __('Create a new escort profile.') }}
                            </p>
                        </header>

                        <form method="post" action="{{ route('admin.escorts.store') }}" class="mt-6 space-y-6">
                            @csrf

                            <div>
                                <x-input-label for="name" :value="__('Name')" />
                                <x-text-input id="name" name="name" type="text" class="mt-1 block w-full" :value="old('name')" required autofocus />
                                <x-input-error class="mt-2" :messages="$errors->get('name')" />
                            </div>

                            <div>
                                <x-input-label for="gender" :value="__('Gender')" />
                                <select id="gender" name="gender" class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm">
                                    <option value="female" {{ old('gender') === 'female' ? 'selected' : '' }}>Female</option>
                                    <option value="male" {{ old('gender') === 'male' ? 'selected' : '' }}>Male</option>
                                    <option value="transgender" {{ old('gender') === 'transgender' ? 'selected' : '' }}>Transgender</option>
                                </select>
                                <x-input-error class="mt-2" :messages="$errors->get('gender')" />
                            </div>

                            <div>
                                <x-input-label for="date_of_birth" :value="__('Date of Birth')" />
                                <x-text-input id="date_of_birth" name="date_of_birth" type="date" class="mt-1 block w-full" :value="old('date_of_birth')" required />
                                <x-input-error class="mt-2" :messages="$errors->get('date_of_birth')" />
                            </div>

                            <div>
                                <x-input-label for="agency_id" :value="__('Agency')" />
                                <select id="agency_id" name="agency_id" class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm">
                                    <option value="">Independent Escort</option>
                                    @foreach($agencies as $agency)
                                        <option value="{{ $agency->id }}" {{ old('agency_id') == $agency->id ? 'selected' : '' }}>
                                            {{ $agency->name }}
                                        </option>
                                    @endforeach
                                </select>
                                <x-input-error class="mt-2" :messages="$errors->get('agency_id')" />
                            </div>

                            <div>
                                <x-input-label for="ethnicity" :value="__('Ethnicity')" />
                                <select id="ethnicity" name="ethnicity" class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm">
                                    <option value="Asian" {{ old('ethnicity') === 'Asian' ? 'selected' : '' }}>Asian</option>
                                    <option value="Black" {{ old('ethnicity') === 'Black' ? 'selected' : '' }}>Black</option>
                                    <option value="Caucasian" {{ old('ethnicity') === 'Caucasian' ? 'selected' : '' }}>Caucasian</option>
                                    <option value="Hispanic" {{ old('ethnicity') === 'Hispanic' ? 'selected' : '' }}>Hispanic</option>
                                    <option value="Middle Eastern" {{ old('ethnicity') === 'Middle Eastern' ? 'selected' : '' }}>Middle Eastern</option>
                                    <option value="Mixed" {{ old('ethnicity') === 'Mixed' ? 'selected' : '' }}>Mixed</option>
                                    <option value="Other" {{ old('ethnicity') === 'Other' ? 'selected' : '' }}>Other</option>
                                </select>
                                <x-input-error class="mt-2" :messages="$errors->get('ethnicity')" />
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <x-input-label for="height_cm" :value="__('Height (cm)')" />
                                    <x-text-input id="height_cm" name="height_cm" type="number" class="mt-1 block w-full" :value="old('height_cm')" required />
                                    <x-input-error class="mt-2" :messages="$errors->get('height_cm')" />
                                </div>

                                <div>
                                    <x-input-label for="weight_kg" :value="__('Weight (kg)')" />
                                    <x-text-input id="weight_kg" name="weight_kg" type="number" class="mt-1 block w-full" :value="old('weight_kg')" required />
                                    <x-input-error class="mt-2" :messages="$errors->get('weight_kg')" />
                                </div>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <x-input-label for="hair_color" :value="__('Hair Color')" />
                                    <select id="hair_color" name="hair_color" class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm">
                                        <option value="Black" {{ old('hair_color') === 'Black' ? 'selected' : '' }}>Black</option>
                                        <option value="Blonde" {{ old('hair_color') === 'Blonde' ? 'selected' : '' }}>Blonde</option>
                                        <option value="Brown" {{ old('hair_color') === 'Brown' ? 'selected' : '' }}>Brown</option>
                                        <option value="Red" {{ old('hair_color') === 'Red' ? 'selected' : '' }}>Red</option>
                                        <option value="Other" {{ old('hair_color') === 'Other' ? 'selected' : '' }}>Other</option>
                                    </select>
                                    <x-input-error class="mt-2" :messages="$errors->get('hair_color')" />
                                </div>

                                <div>
                                    <x-input-label for="hair_length" :value="__('Hair Length')" />
                                    <select id="hair_length" name="hair_length" class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm">
                                        <option value="Short" {{ old('hair_length') === 'Short' ? 'selected' : '' }}>Short</option>
                                        <option value="Medium" {{ old('hair_length') === 'Medium' ? 'selected' : '' }}>Medium</option>
                                        <option value="Long" {{ old('hair_length') === 'Long' ? 'selected' : '' }}>Long</option>
                                    </select>
                                    <x-input-error class="mt-2" :messages="$errors->get('hair_length')" />
                                </div>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <x-input-label for="build" :value="__('Build')" />
                                    <select id="build" name="build" class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm">
                                        <option value="Slim" {{ old('build') === 'Slim' ? 'selected' : '' }}>Slim</option>
                                        <option value="Athletic" {{ old('build') === 'Athletic' ? 'selected' : '' }}>Athletic</option>
                                        <option value="Regular" {{ old('build') === 'Regular' ? 'selected' : '' }}>Regular</option>
                                        <option value="Curvy" {{ old('build') === 'Curvy' ? 'selected' : '' }}>Curvy</option>
                                        <option value="Full" {{ old('build') === 'Full' ? 'selected' : '' }}>Full</option>
                                    </select>
                                    <x-input-error class="mt-2" :messages="$errors->get('build')" />
                                </div>

                                <div>
                                    <x-input-label for="looks" :value="__('Looks')" />
                                    <select id="looks" name="looks" class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm">
                                        <option value="Average" {{ old('looks') === 'Average' ? 'selected' : '' }}>Average</option>
                                        <option value="Attractive" {{ old('looks') === 'Attractive' ? 'selected' : '' }}>Attractive</option>
                                        <option value="Very Attractive" {{ old('looks') === 'Very Attractive' ? 'selected' : '' }}>Very Attractive</option>
                                        <option value="Stunning" {{ old('looks') === 'Stunning' ? 'selected' : '' }}>Stunning</option>
                                    </select>
                                    <x-input-error class="mt-2" :messages="$errors->get('looks')" />
                                </div>
                            </div>

                            <div>
                                <x-input-label for="bio" :value="__('Bio')" />
                                <textarea id="bio" name="bio" rows="4" class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm">{{ old('bio') }}</textarea>
                                <x-input-error class="mt-2" :messages="$errors->get('bio')" />
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <x-input-label for="hourly_rate" :value="__('Hourly Rate ($)')" />
                                    <x-text-input id="hourly_rate" name="hourly_rate" type="number" class="mt-1 block w-full" :value="old('hourly_rate')" />
                                    <x-input-error class="mt-2" :messages="$errors->get('hourly_rate')" />
                                </div>

                                <div>
                                    <x-input-label for="phone" :value="__('Phone')" />
                                    <x-text-input id="phone" name="phone" type="text" class="mt-1 block w-full" :value="old('phone')" />
                                    <x-input-error class="mt-2" :messages="$errors->get('phone')" />
                                </div>
                            </div>

                            <div class="flex flex-wrap gap-4">
                                <div>
                                    <label for="is_verified" class="inline-flex items-center">
                                        <input id="is_verified" type="checkbox" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500" name="is_verified" value="1" {{ old('is_verified') ? 'checked' : '' }}>
                                        <span class="ml-2 text-sm text-gray-600">{{ __('Verified') }}</span>
                                    </label>
                                </div>

                                <div>
                                    <label for="is_featured" class="inline-flex items-center">
                                        <input id="is_featured" type="checkbox" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500" name="is_featured" value="1" {{ old('is_featured') ? 'checked' : '' }}>
                                        <span class="ml-2 text-sm text-gray-600">{{ __('Featured') }}</span>
                                    </label>
                                </div>

                                <div>
                                    <label for="is_premium" class="inline-flex items-center">
                                        <input id="is_premium" type="checkbox" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500" name="is_premium" value="1" {{ old('is_premium') ? 'checked' : '' }}>
                                        <span class="ml-2 text-sm text-gray-600">{{ __('Premium') }}</span>
                                    </label>
                                </div>

                                <div>
                                    <label for="is_new" class="inline-flex items-center">
                                        <input id="is_new" type="checkbox" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500" name="is_new" value="1" {{ old('is_new') ? 'checked' : '' }}>
                                        <span class="ml-2 text-sm text-gray-600">{{ __('New') }}</span>
                                    </label>
                                </div>
                            </div>

                            <div class="flex items-center gap-4">
                                <x-primary-button>{{ __('Create Escort') }}</x-primary-button>
                            </div>
                        </form>
                    </section>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
