<x-app-layout>
    <x-slot name="header">
        <script>
            function copyToClipboard(elementId) {
                const element = document.getElementById(elementId);
                const textToCopy = element.value || element.textContent;

                navigator.clipboard.writeText(textToCopy).then(() => {
                    // Show a temporary success message
                    const button = event.currentTarget;
                    const originalHTML = button.innerHTML;

                    button.innerHTML = `
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                    `;

                    setTimeout(() => {
                        button.innerHTML = originalHTML;
                    }, 2000);
                }).catch(err => {
                    console.error('Failed to copy text: ', err);
                });
            }
        </script>
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Edit User') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <div class="p-4 sm:p-8 bg-white shadow sm:rounded-lg">
                <div class="max-w-xl">
                    <section>
                        <header>
                            <h2 class="text-lg font-medium text-gray-900">
                                {{ __('Edit User Information') }}
                            </h2>

                            <p class="mt-1 text-sm text-gray-600">
                                {{ __('Update the user\'s account information.') }}
                            </p>
                        </header>

                        <form method="post" action="{{ route('admin.users.update', $user->id) }}" class="mt-6 space-y-6">
                            @csrf
                            @method('patch')

                            <div>
                                <x-input-label for="username" :value="__('Username')" />
                                <x-text-input id="username" name="username" type="text" class="mt-1 block w-full" :value="old('username', $user->username)" required autofocus />
                                <x-input-error class="mt-2" :messages="$errors->get('username')" />
                            </div>

                            <div>
                                <x-input-label for="email" :value="__('Email')" />
                                <x-text-input id="email" name="email" type="email" class="mt-1 block w-full" :value="old('email', $user->email)" required />
                                <x-input-error class="mt-2" :messages="$errors->get('email')" />
                            </div>

                            <div>
                                <x-input-label for="current_password_display" :value="__('Current Password')" />
                                <div class="relative">
                                    <x-text-input id="current_password_display" type="text" class="mt-1 block w-full bg-gray-100" value="{{ $user->getAuthPassword() }}" readonly />
                                    <button type="button" onclick="copyToClipboard('current_password_display')" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600 hover:text-gray-900">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                                        </svg>
                                    </button>
                                </div>
                                <p class="mt-1 text-xs text-gray-500">This is the hashed password. For security reasons, you cannot see the actual password.</p>
                            </div>

                            <div>
                                <x-input-label for="password" :value="__('New Password (leave blank to keep current)')" />
                                <x-text-input id="password" name="password" type="text" class="mt-1 block w-full" />
                                <x-input-error class="mt-2" :messages="$errors->get('password')" />
                            </div>

                            <div>
                                <x-input-label for="password_confirmation" :value="__('Confirm Password')" />
                                <x-text-input id="password_confirmation" name="password_confirmation" type="text" class="mt-1 block w-full" />
                                <x-input-error class="mt-2" :messages="$errors->get('password_confirmation')" />
                            </div>

                            <div>
                                <label for="is_active" class="inline-flex items-center">
                                    <input id="is_active" type="checkbox" class="rounded border-gray-300 text-pink-600 shadow-sm focus:ring-pink-500" name="is_active" value="1" {{ old('is_active', $user->is_active) ? 'checked' : '' }}>
                                    <span class="ml-2 text-sm text-gray-600">{{ __('Active Account') }}</span>
                                </label>
                                <x-input-error class="mt-2" :messages="$errors->get('is_active')" />
                            </div>

                            <div class="flex items-center gap-4">
                                <x-primary-button>{{ __('Save') }}</x-primary-button>
                                <a href="{{ route('admin.users') }}" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                    {{ __('Cancel') }}
                                </a>
                            </div>
                        </form>
                    </section>
                </div>
            </div>

            <div class="p-4 sm:p-8 bg-white shadow sm:rounded-lg">
                <div class="max-w-xl">
                    <div class="flex justify-between items-center">
                        <h2 class="text-lg font-medium text-gray-900">
                            {{ __('User Details') }}
                        </h2>
                    </div>

                    <div class="mt-4 space-y-4">
                        <div>
                            <h3 class="text-sm font-medium text-gray-500">User Type</h3>
                            <p class="mt-1">
                                @if($user->user_type === 'admin')
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">Admin</span>
                                @elseif($user->user_type === 'escort')
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-pink-100 text-pink-800">Escort</span>
                                @elseif($user->user_type === 'agency')
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Agency</span>
                                @else
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Regular</span>
                                @endif
                            </p>
                        </div>

                        <div>
                            <h3 class="text-sm font-medium text-gray-500">Registered On</h3>
                            <p class="mt-1">{{ $user->created_at->format('F j, Y, g:i a') }}</p>
                        </div>

                        <div>
                            <h3 class="text-sm font-medium text-gray-500">Last Updated</h3>
                            <p class="mt-1">{{ $user->updated_at->format('F j, Y, g:i a') }}</p>
                        </div>

                        @if($user->user_type === 'escort' && $user->escort)
                            <div>
                                <h3 class="text-sm font-medium text-gray-500">Escort Profile</h3>
                                <p class="mt-1">
                                    <a href="{{ route('escorts.show', ['slug' => $user->escort->slug ?? $user->escort->id]) }}" class="text-pink-600 hover:text-pink-900">View Public Profile</a>
                                </p>
                            </div>
                        @endif

                        @if($user->user_type === 'agency' && $user->agency)
                            <div>
                                <h3 class="text-sm font-medium text-gray-500">Agency Profile</h3>
                                <p class="mt-1">
                                    <a href="{{ route('agencies.show', ['slug' => $user->agency->slug ?? $user->agency->id]) }}" class="text-pink-600 hover:text-pink-900">View Public Profile</a>
                                </p>
                            </div>
                        @endif

                        <!-- Contact Information -->
                        <div class="mt-6">
                            <h3 class="text-sm font-medium text-gray-900 bg-gray-100 p-2 rounded">Contact Information</h3>

                            @if($user->profile && $user->profile->phone)
                                <div class="mt-3">
                                    <h4 class="text-sm font-medium text-gray-500">Phone Number</h4>
                                    <div class="flex items-center mt-1">
                                        <a href="tel:{{ $user->profile->phone }}" class="text-blue-600 hover:text-blue-900">{{ $user->profile->phone }}</a>
                                        <button type="button" onclick="copyToClipboard('phone-{{ $user->id }}')" class="ml-2 text-gray-600 hover:text-gray-900">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                                            </svg>
                                        </button>
                                        <input type="hidden" id="phone-{{ $user->id }}" value="{{ $user->profile->phone }}">
                                    </div>
                                </div>
                            @endif

                            @if($user->user_type === 'escort' && $user->escort)
                                @if($user->escort->phone_number)
                                    <div class="mt-3">
                                        <h4 class="text-sm font-medium text-gray-500">Escort Phone Number</h4>
                                        <div class="flex items-center mt-1">
                                            <a href="tel:{{ $user->escort->phone_number }}" class="text-blue-600 hover:text-blue-900">{{ $user->escort->phone_number }}</a>
                                            <button type="button" onclick="copyToClipboard('escort-phone-{{ $user->id }}')" class="ml-2 text-gray-600 hover:text-gray-900">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                                                </svg>
                                            </button>
                                            <input type="hidden" id="escort-phone-{{ $user->id }}" value="{{ $user->escort->phone_number }}">
                                        </div>
                                    </div>
                                @endif

                                @if($user->escort->whatsapp_number)
                                    <div class="mt-3">
                                        <h4 class="text-sm font-medium text-gray-500">WhatsApp Number</h4>
                                        <div class="flex items-center mt-1">
                                            <a href="https://wa.me/{{ preg_replace('/[^0-9]/', '', $user->escort->whatsapp_number) }}" target="_blank" class="text-green-600 hover:text-green-900">{{ $user->escort->whatsapp_number }}</a>
                                            <button type="button" onclick="copyToClipboard('whatsapp-{{ $user->id }}')" class="ml-2 text-gray-600 hover:text-gray-900">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                                                </svg>
                                            </button>
                                            <input type="hidden" id="whatsapp-{{ $user->id }}" value="{{ $user->escort->whatsapp_number }}">
                                        </div>
                                    </div>
                                @endif
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
