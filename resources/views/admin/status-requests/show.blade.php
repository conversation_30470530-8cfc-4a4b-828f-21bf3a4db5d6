<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Status Request Details') }}
            </h2>
            <a href="{{ route('admin.status-requests.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                {{ __('Back to Requests') }}
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    @if(session('error'))
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                            <span class="block sm:inline">{{ session('error') }}</span>
                        </div>
                    @endif

                    <div class="mb-8">
                        <div class="flex items-center mb-4">
                            @if($statusRequest->request_type === 'verification')
                                <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-green-100 text-green-800 mr-3">
                                    Verification Request
                                </span>
                            @else
                                <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-purple-100 text-purple-800 mr-3">
                                    Featured Request
                                </span>
                            @endif

                            @if($statusRequest->status === 'pending')
                                <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    Pending
                                </span>
                            @elseif($statusRequest->status === 'approved')
                                <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    Approved
                                </span>
                            @else
                                <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                    Rejected
                                </span>
                            @endif
                        </div>

                        <h3 class="text-lg font-medium text-gray-900 mb-2">Request #{{ $statusRequest->id }}</h3>
                        <p class="text-gray-600">Submitted on {{ $statusRequest->created_at->format('F j, Y \a\t g:i a') }}</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <div class="col-span-2">
                            <div class="bg-gray-50 rounded-lg p-6 mb-6 border border-gray-200">
                                <h4 class="text-lg font-medium text-gray-900 mb-4">Request Information</h4>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <p class="text-sm text-gray-500 mb-1">Request Type</p>
                                        <p class="font-medium text-gray-900">{{ ucfirst($statusRequest->request_type) }}</p>
                                    </div>

                                    <div>
                                        <p class="text-sm text-gray-500 mb-1">Status</p>
                                        <p class="font-medium text-gray-900">{{ ucfirst($statusRequest->status) }}</p>
                                    </div>

                                    <div>
                                        <p class="text-sm text-gray-500 mb-1">Duration</p>
                                        <p class="font-medium text-gray-900">{{ $statusRequest->formatted_duration }}</p>
                                    </div>

                                    <div>
                                        <p class="text-sm text-gray-500 mb-1">Price</p>
                                        <p class="font-medium text-gray-900">{{ $statusRequest->formatted_price }}</p>
                                    </div>

                                    @if($statusRequest->approved_at)
                                        <div>
                                            <p class="text-sm text-gray-500 mb-1">Approved On</p>
                                            <p class="font-medium text-gray-900">{{ $statusRequest->approved_at->format('F j, Y') }}</p>
                                        </div>
                                    @endif

                                    @if($statusRequest->expires_at)
                                        <div>
                                            <p class="text-sm text-gray-500 mb-1">Expires On</p>
                                            <p class="font-medium text-gray-900">{{ $statusRequest->expires_at->format('F j, Y') }}</p>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            @if($statusRequest->admin_message)
                                <div class="bg-blue-50 rounded-lg p-6 mb-6 border border-blue-200">
                                    <h4 class="text-lg font-medium text-gray-900 mb-2">Admin Message</h4>
                                    <p class="text-gray-700">{{ $statusRequest->admin_message }}</p>
                                </div>
                            @endif
                        </div>

                        <div>
                            <div class="bg-white rounded-lg p-6 border border-gray-200">
                                <h4 class="text-lg font-medium text-gray-900 mb-4">Escort Information</h4>

                                <div class="mb-4">
                                    <p class="text-sm text-gray-500 mb-1">Name</p>
                                    <p class="font-medium text-gray-900">{{ $statusRequest->escort->name }}</p>
                                </div>

                                <div class="mb-4">
                                    <p class="text-sm text-gray-500 mb-1">Email</p>
                                    <p class="font-medium text-gray-900">{{ $statusRequest->escort->user->email }}</p>
                                </div>

                                <div class="mb-4">
                                    <p class="text-sm text-gray-500 mb-1">Status</p>
                                    <div class="flex flex-wrap gap-2 mt-1">
                                        @if($statusRequest->escort->is_verified)
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                Verified
                                            </span>
                                        @endif

                                        @if($statusRequest->escort->is_featured)
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">
                                                Featured
                                            </span>
                                        @endif

                                        @if($statusRequest->escort->is_premium)
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                Premium
                                            </span>
                                        @endif
                                    </div>
                                </div>

                                <div class="mt-6">
                                    <a href="{{ route('escorts.show', $statusRequest->escort->slug) }}" target="_blank" class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                        View Escort Profile
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($statusRequest->status === 'pending')
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Approve Form -->
                            <div class="bg-green-50 rounded-lg p-6 border border-green-200">
                                <h4 class="text-lg font-medium text-gray-900 mb-4">Approve Request</h4>

                                <form action="{{ route('admin.status-requests.approve', $statusRequest->id) }}" method="POST">
                                    @csrf

                                    <div class="mb-4">
                                        <x-input-label for="admin_message_approve" :value="__('Message (Optional)')" />
                                        <textarea id="admin_message_approve" name="admin_message" rows="3" class="mt-1 block w-full border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-md shadow-sm" placeholder="Add a message to the escort..."></textarea>
                                    </div>

                                    <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                        Approve Request
                                    </button>
                                </form>
                            </div>

                            <!-- Reject Form -->
                            <div class="bg-red-50 rounded-lg p-6 border border-red-200">
                                <h4 class="text-lg font-medium text-gray-900 mb-4">Reject Request</h4>

                                <form action="{{ route('admin.status-requests.reject', $statusRequest->id) }}" method="POST">
                                    @csrf

                                    <div class="mb-4">
                                        <x-input-label for="admin_message_reject" :value="__('Reason for Rejection')" />
                                        <textarea id="admin_message_reject" name="admin_message" rows="3" class="mt-1 block w-full border-gray-300 focus:border-red-500 focus:ring-red-500 rounded-md shadow-sm" placeholder="Explain why the request is being rejected..."></textarea>
                                    </div>

                                    <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 focus:bg-red-700 active:bg-red-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                        Reject Request
                                    </button>
                                </form>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
