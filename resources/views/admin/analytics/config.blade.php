<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Analytics Configuration') }}
            </h2>
            <div class="flex items-center space-x-4">
                <div class="text-sm text-gray-600">
                    Setup Progress: {{ $setupStatus['overall_progress'] }}%
                </div>
                <div class="w-32 bg-gray-200 rounded-full h-2">
                    <div class="bg-blue-500 h-2 rounded-full" style="width: {{ $setupStatus['overall_progress'] }}%"></div>
                </div>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            
            <!-- Success/Error Messages -->
            @if(session('success'))
                <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    {{ session('error') }}
                </div>
            @endif

            <!-- Setup Status Overview -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Setup Status</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        
                        <!-- Google Analytics -->
                        <div class="border rounded-lg p-4 {{ $setupStatus['google_analytics']['configured'] ? 'border-green-200 bg-green-50' : 'border-gray-200' }}">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-gray-900">Google Analytics</h4>
                                @if($setupStatus['google_analytics']['configured'])
                                    <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                @else
                                    <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                    </svg>
                                @endif
                            </div>
                            <p class="text-sm text-gray-600">{{ $setupStatus['google_analytics']['message'] }}</p>
                            @if($setupStatus['google_analytics']['configured'])
                                <button onclick="testService('google_analytics')" class="mt-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                    Test Connection
                                </button>
                            @endif
                        </div>

                        <!-- Google Search Console -->
                        <div class="border rounded-lg p-4 {{ $setupStatus['google_search_console']['configured'] ? 'border-green-200 bg-green-50' : 'border-gray-200' }}">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-gray-900">Search Console</h4>
                                @if($setupStatus['google_search_console']['configured'])
                                    <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                @else
                                    <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                    </svg>
                                @endif
                            </div>
                            <p class="text-sm text-gray-600">{{ $setupStatus['google_search_console']['message'] }}</p>
                            @if($setupStatus['google_search_console']['configured'])
                                <button onclick="testService('google_search_console')" class="mt-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                    Test Connection
                                </button>
                            @endif
                        </div>

                        <!-- Facebook Pixel -->
                        <div class="border rounded-lg p-4 {{ $setupStatus['facebook_pixel']['configured'] ? 'border-green-200 bg-green-50' : 'border-gray-200' }}">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-gray-900">Facebook Pixel</h4>
                                @if($setupStatus['facebook_pixel']['configured'])
                                    <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                @else
                                    <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                    </svg>
                                @endif
                            </div>
                            <p class="text-sm text-gray-600">{{ $setupStatus['facebook_pixel']['message'] }}</p>
                            @if($setupStatus['facebook_pixel']['configured'])
                                <button onclick="testService('facebook_pixel')" class="mt-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                    Test Connection
                                </button>
                            @endif
                        </div>

                        <!-- Hotjar -->
                        <div class="border rounded-lg p-4 {{ $setupStatus['hotjar']['configured'] ? 'border-green-200 bg-green-50' : 'border-gray-200' }}">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-gray-900">Hotjar</h4>
                                @if($setupStatus['hotjar']['configured'])
                                    <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                @else
                                    <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                    </svg>
                                @endif
                            </div>
                            <p class="text-sm text-gray-600">{{ $setupStatus['hotjar']['message'] }}</p>
                            @if($setupStatus['hotjar']['configured'])
                                <button onclick="testService('hotjar')" class="mt-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                    Test Connection
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Configuration Form -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">Analytics Configuration</h3>
                    
                    <form method="POST" action="{{ route('admin.analytics.config.update') }}">
                        @csrf
                        
                        <!-- Master Toggle -->
                        <div class="mb-8 p-4 bg-blue-50 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-lg font-medium text-gray-900">Enable Analytics Tracking</h4>
                                    <p class="text-sm text-gray-600">Master switch to enable/disable all analytics tracking</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" name="analytics_enabled" value="1" class="sr-only peer" {{ $analyticsSettings['analytics_enabled'] ? 'checked' : '' }}>
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>
                        </div>

                        <!-- Google Analytics -->
                        <div class="mb-8">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">Google Analytics</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="google_analytics_id" class="block text-sm font-medium text-gray-700 mb-2">
                                        Google Analytics ID
                                    </label>
                                    <input type="text" 
                                           name="google_analytics_id" 
                                           id="google_analytics_id"
                                           value="{{ old('google_analytics_id', $analyticsSettings['google_analytics_id']) }}"
                                           placeholder="G-XXXXXXXXXX or UA-XXXXXXXX-X"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    @error('google_analytics_id')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                    <p class="mt-1 text-xs text-gray-500">Find this in your Google Analytics property settings</p>
                                </div>
                                <div>
                                    <label for="google_analytics_measurement_id" class="block text-sm font-medium text-gray-700 mb-2">
                                        Measurement ID (GA4)
                                    </label>
                                    <input type="text" 
                                           name="google_analytics_measurement_id" 
                                           id="google_analytics_measurement_id"
                                           value="{{ old('google_analytics_measurement_id', $analyticsSettings['google_analytics_measurement_id']) }}"
                                           placeholder="G-XXXXXXXXXX"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    @error('google_analytics_measurement_id')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                    <p class="mt-1 text-xs text-gray-500">For GA4 properties only</p>
                                </div>
                            </div>
                        </div>

                        <!-- Google Search Console -->
                        <div class="mb-8">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">Google Search Console</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="google_search_console_verification" class="block text-sm font-medium text-gray-700 mb-2">
                                        Verification Code
                                    </label>
                                    <input type="text" 
                                           name="google_search_console_verification" 
                                           id="google_search_console_verification"
                                           value="{{ old('google_search_console_verification', $analyticsSettings['google_search_console_verification']) }}"
                                           placeholder="abcdef123456..."
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    @error('google_search_console_verification')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                    <p class="mt-1 text-xs text-gray-500">HTML tag verification code from Search Console</p>
                                </div>
                                <div>
                                    <label for="google_search_console_property" class="block text-sm font-medium text-gray-700 mb-2">
                                        Property URL
                                    </label>
                                    <input type="url" 
                                           name="google_search_console_property" 
                                           id="google_search_console_property"
                                           value="{{ old('google_search_console_property', $analyticsSettings['google_search_console_property']) }}"
                                           placeholder="https://yoursite.com"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    @error('google_search_console_property')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                    <p class="mt-1 text-xs text-gray-500">Your verified property URL</p>
                                </div>
                            </div>
                        </div>

                        <!-- Facebook Pixel -->
                        <div class="mb-8">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">Facebook Pixel</h4>
                            <div>
                                <label for="facebook_pixel_id" class="block text-sm font-medium text-gray-700 mb-2">
                                    Pixel ID
                                </label>
                                <input type="text" 
                                       name="facebook_pixel_id" 
                                       id="facebook_pixel_id"
                                       value="{{ old('facebook_pixel_id', $analyticsSettings['facebook_pixel_id']) }}"
                                       placeholder="123456789012345"
                                       class="w-full md:w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                @error('facebook_pixel_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-xs text-gray-500">Find this in your Facebook Events Manager</p>
                            </div>
                        </div>

                        <!-- Hotjar -->
                        <div class="mb-8">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">Hotjar</h4>
                            <div>
                                <label for="hotjar_id" class="block text-sm font-medium text-gray-700 mb-2">
                                    Site ID
                                </label>
                                <input type="text" 
                                       name="hotjar_id" 
                                       id="hotjar_id"
                                       value="{{ old('hotjar_id', $analyticsSettings['hotjar_id']) }}"
                                       placeholder="1234567"
                                       class="w-full md:w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                @error('hotjar_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-xs text-gray-500">Find this in your Hotjar dashboard</p>
                            </div>
                        </div>

                        <!-- Bing Webmaster -->
                        <div class="mb-8">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">Bing Webmaster Tools</h4>
                            <div>
                                <label for="bing_webmaster_verification" class="block text-sm font-medium text-gray-700 mb-2">
                                    Verification Code
                                </label>
                                <input type="text" 
                                       name="bing_webmaster_verification" 
                                       id="bing_webmaster_verification"
                                       value="{{ old('bing_webmaster_verification', $analyticsSettings['bing_webmaster_verification']) }}"
                                       placeholder="abcdef123456..."
                                       class="w-full md:w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                @error('bing_webmaster_verification')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-xs text-gray-500">HTML tag verification code from Bing Webmaster Tools</p>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end">
                            <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                Save Configuration
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Setup Instructions -->
            <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="text-lg font-medium text-blue-900 mb-4">Setup Instructions</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-medium text-blue-800 mb-2">Quick Links</h4>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• <a href="https://analytics.google.com" target="_blank" class="underline">Google Analytics</a></li>
                            <li>• <a href="https://search.google.com/search-console" target="_blank" class="underline">Google Search Console</a></li>
                            <li>• <a href="https://business.facebook.com/events_manager" target="_blank" class="underline">Facebook Events Manager</a></li>
                            <li>• <a href="https://www.hotjar.com" target="_blank" class="underline">Hotjar Dashboard</a></li>
                            <li>• <a href="https://www.bing.com/webmasters" target="_blank" class="underline">Bing Webmaster Tools</a></li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-medium text-blue-800 mb-2">Next Steps</h4>
                        <ol class="text-sm text-blue-700 space-y-1 list-decimal list-inside">
                            <li>Configure each service above</li>
                            <li>Test connections using the test buttons</li>
                            <li>Enable analytics tracking</li>
                            <li>Monitor data in the analytics dashboard</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Results Modal -->
    <div id="testModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Test Results</h3>
                <button onclick="closeTestModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div id="testResults" class="text-sm text-gray-600">
                <!-- Test results will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        function testService(service) {
            fetch(`{{ route('admin.analytics.config.test') }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({ service: service })
            })
            .then(response => response.json())
            .then(data => {
                showTestResults(data);
            })
            .catch(error => {
                showTestResults({ status: 'error', message: 'Test failed: ' + error.message });
            });
        }

        function showTestResults(results) {
            const modal = document.getElementById('testModal');
            const resultsDiv = document.getElementById('testResults');
            
            let html = `<div class="p-4 rounded-lg ${results.status === 'success' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}">`;
            html += `<div class="flex items-center mb-2">`;
            html += `<div class="w-4 h-4 ${results.status === 'success' ? 'text-green-500' : 'text-red-500'} mr-2">`;
            html += results.status === 'success' ? '✓' : '✗';
            html += `</div>`;
            html += `<span class="font-medium">${results.status === 'success' ? 'Success' : 'Error'}</span>`;
            html += `</div>`;
            html += `<p class="text-sm">${results.message}</p>`;
            
            if (results.details) {
                html += `<div class="mt-3 text-xs">`;
                html += `<strong>Details:</strong><br>`;
                for (const [key, value] of Object.entries(results.details)) {
                    html += `${key}: ${Array.isArray(value) ? value.join(', ') : value}<br>`;
                }
                html += `</div>`;
            }
            html += `</div>`;
            
            resultsDiv.innerHTML = html;
            modal.classList.remove('hidden');
            modal.classList.add('flex');
        }

        function closeTestModal() {
            const modal = document.getElementById('testModal');
            modal.classList.add('hidden');
            modal.classList.remove('flex');
        }
    </script>
</x-app-layout>
