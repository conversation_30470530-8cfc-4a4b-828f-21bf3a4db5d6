<x-home-layout>
    <!-- SEO Meta Tags -->
    @if(isset($seoMeta))
        <x-seo-meta :meta="$seoMeta" />
    @endif

    <!-- Breadcrumb Navigation -->
    <nav class="bg-white border-b border-gray-200 py-3">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <ol class="flex items-center space-x-2 text-sm">
                <li><a href="{{ route('home') }}" class="text-gray-500 hover:text-indigo-600">Home</a></li>
                <li><svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/></svg></li>
                <li><span class="text-gray-700 font-medium">Services</span></li>
            </ol>
        </div>
    </nav>

    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-purple-600 to-pink-600 py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-6">Professional Escort Services</h1>
            <p class="text-xl text-purple-100 max-w-3xl mx-auto">
                Discover our range of professional companion services in Uganda and East Africa.
            </p>
        </div>
    </div>

    <!-- Services Grid -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($services as $service)
                <div class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow duration-300">
                    <div class="bg-pink-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-pink-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ $service->name }}</h3>
                    <p class="text-gray-600 mb-4">{{ $service->description ?? 'Professional companion service available.' }}</p>
                    @if(isset($service->escorts_count) && $service->escorts_count > 0)
                        <p class="text-sm text-pink-600 font-medium">{{ $service->escorts_count }} escorts available</p>
                    @endif
                </div>
            @endforeach
        </div>

        <!-- Call to Action -->
        <div class="text-center mt-16">
            <h2 class="text-3xl font-bold text-gray-900 mb-6">Ready to Book?</h2>
            <p class="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
                Browse our verified escorts and find the perfect companion for your needs.
            </p>
            <a href="{{ route('home') }}" class="inline-block bg-pink-600 hover:bg-pink-700 text-white font-medium px-8 py-3 rounded-lg transition-colors duration-300">
                Browse Escorts
            </a>
        </div>
    </div>
</x-home-layout>
