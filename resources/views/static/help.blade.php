<x-home-layout>
    <!-- SEO Meta Tags -->
    @if(isset($seoMeta))
        <x-seo-meta :meta="$seoMeta" />
    @endif

    <!-- Breadcrumb Navigation -->
    <nav class="bg-white border-b border-gray-200 py-3">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <ol class="flex items-center space-x-2 text-sm">
                <li><a href="{{ route('home') }}" class="text-gray-500 hover:text-indigo-600">Home</a></li>
                <li><svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/></svg></li>
                <li><span class="text-gray-700 font-medium">Help Center</span></li>
            </ol>
        </div>
    </nav>

    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-indigo-600 to-purple-600 py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="flex justify-center mb-6">
                <div class="bg-white bg-opacity-20 p-4 rounded-full">
                    <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                    </svg>
                </div>
            </div>
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-6">Help Center</h1>
            <p class="text-xl text-indigo-100 max-w-3xl mx-auto">
                Find answers to frequently asked questions and get the support you need.
            </p>
        </div>
    </div>

    <!-- Search Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="max-w-2xl mx-auto">
            <div class="relative">
                <input type="text" placeholder="Search for help topics..." class="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                <svg class="w-6 h-6 text-gray-400 absolute left-4 top-3.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                </svg>
            </div>
        </div>
    </div>

    <!-- FAQ Categories -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <div class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow duration-300">
                <div class="bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">For Clients</h3>
                <p class="text-gray-600 mb-4">Questions about booking, payments, and using our platform as a client.</p>
                <a href="#client-faq" class="text-blue-600 hover:text-blue-700 font-medium">View FAQs →</a>
            </div>

            <div class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow duration-300">
                <div class="bg-pink-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-pink-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">For Escorts</h3>
                <p class="text-gray-600 mb-4">Information about registration, verification, and managing your profile.</p>
                <a href="#escort-faq" class="text-pink-600 hover:text-pink-700 font-medium">View FAQs →</a>
            </div>

            <div class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow duration-300">
                <div class="bg-purple-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-6a1 1 0 00-1-1H9a1 1 0 00-1 1v6a1 1 0 01-1 1H4a1 1 0 110-2V4z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">For Agencies</h3>
                <p class="text-gray-600 mb-4">Agency registration, management, and working with multiple escorts.</p>
                <a href="#agency-faq" class="text-purple-600 hover:text-purple-700 font-medium">View FAQs →</a>
            </div>
        </div>

        <!-- Client FAQ Section -->
        <div id="client-faq" class="mb-16">
            <h2 class="text-3xl font-bold text-gray-900 mb-8">Frequently Asked Questions - Clients</h2>
            <div class="space-y-6">
                <div class="bg-white border border-gray-200 rounded-lg">
                    <button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50" onclick="toggleFaq(this)">
                        <span class="font-semibold text-gray-900">How do I book an escort?</span>
                        <svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                    </button>
                    <div class="hidden px-6 pb-4">
                        <p class="text-gray-600">Browse our verified escorts, view their profiles, and contact them directly through their provided contact information. Always verify rates and services before meeting.</p>
                    </div>
                </div>

                <div class="bg-white border border-gray-200 rounded-lg">
                    <button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50" onclick="toggleFaq(this)">
                        <span class="font-semibold text-gray-900">How do I know if an escort is verified?</span>
                        <svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                    </button>
                    <div class="hidden px-6 pb-4">
                        <p class="text-gray-600">Verified escorts display a green verification badge on their profile. This means they have completed our identity verification process and provided required documentation.</p>
                    </div>
                </div>

                <div class="bg-white border border-gray-200 rounded-lg">
                    <button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50" onclick="toggleFaq(this)">
                        <span class="font-semibold text-gray-900">What payment methods are accepted?</span>
                        <svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                    </button>
                    <div class="hidden px-6 pb-4">
                        <p class="text-gray-600">Payment arrangements are made directly between clients and escorts. Common methods include cash, mobile money (MTN, Airtel), and bank transfers. Always confirm payment terms before booking.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Support -->
        <div class="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg p-8 text-center">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Still Need Help?</h2>
            <p class="text-gray-600 mb-6">Can't find the answer you're looking for? Our support team is here to help.</p>
            <a href="{{ route('contact.index') }}" class="inline-block bg-indigo-600 hover:bg-indigo-700 text-white font-medium px-8 py-3 rounded-lg transition-colors duration-300">
                Contact Support
            </a>
        </div>
    </div>

    <script>
        function toggleFaq(button) {
            const content = button.nextElementSibling;
            const icon = button.querySelector('svg');
            
            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        }
    </script>
</x-home-layout>
