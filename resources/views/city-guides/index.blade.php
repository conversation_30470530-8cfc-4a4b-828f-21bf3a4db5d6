@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-purple-600 to-pink-600 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-4">
                    Uganda City Guides
                </h1>
                <p class="text-xl md:text-2xl mb-8 opacity-90">
                    Comprehensive guides to professional escort services across Uganda
                </p>
                <p class="text-lg opacity-80 max-w-3xl mx-auto">
                    Discover verified escort services, safety guidelines, and local insights for major cities across Uganda.
                    Our detailed guides help you find professional companions safely and discreetly.
                </p>
            </div>
        </div>
    </div>

    <!-- City Guides Grid -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">

        <!-- Tier 1 Cities -->
        <div class="mb-16">
            <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Major Cities</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                @foreach($priorityCities['tier_1'] as $cityName => $cityData)
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                        <div class="h-48 bg-gradient-to-br from-purple-500 to-pink-500 relative">
                            <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                            <div class="absolute bottom-4 left-4 text-white">
                                <h3 class="text-2xl font-bold">{{ $cityName }}</h3>
                                <p class="text-sm opacity-90">{{ $cityData['population'] }} people</p>
                            </div>
                            <div class="absolute top-4 right-4">
                                <span class="bg-yellow-400 text-yellow-900 px-2 py-1 rounded-full text-xs font-semibold">
                                    {{ ucfirst($cityData['priority']) }} Priority
                                </span>
                            </div>
                        </div>
                        <div class="p-6">
                            <p class="text-gray-600 mb-4">
                                Professional escort services in {{ $cityName }} with verified companions and premium agencies.
                            </p>
                            <div class="flex justify-between items-center">
                                <a href="{{ route('locations.show', \Str::slug($cityName)) }}"
                                   class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                                    View Guide
                                </a>
                                <a href="{{ route('escorts.index', ['location' => $cityName]) }}"
                                   class="text-purple-600 hover:text-purple-800 font-medium">
                                    Browse Escorts
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Tier 2 Cities -->
        <div class="mb-16">
            <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Regional Centers</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                @foreach($priorityCities['tier_2'] as $cityName => $cityData)
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                        <div class="h-40 bg-gradient-to-br from-blue-500 to-purple-500 relative">
                            <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                            <div class="absolute bottom-4 left-4 text-white">
                                <h3 class="text-xl font-bold">{{ $cityName }}</h3>
                                <p class="text-sm opacity-90">{{ $cityData['population'] }} people</p>
                            </div>
                            <div class="absolute top-4 right-4">
                                <span class="bg-blue-400 text-blue-900 px-2 py-1 rounded-full text-xs font-semibold">
                                    {{ ucfirst($cityData['priority']) }} Priority
                                </span>
                            </div>
                        </div>
                        <div class="p-6">
                            <p class="text-gray-600 mb-4">
                                Escort services in {{ $cityName }} with professional companions and verified profiles.
                            </p>
                            <div class="flex justify-between items-center">
                                <a href="{{ route('locations.show', \Str::slug($cityName)) }}"
                                   class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                                    View Guide
                                </a>
                                <a href="{{ route('escorts.index', ['location' => $cityName]) }}"
                                   class="text-blue-600 hover:text-blue-800 font-medium">
                                    Browse Escorts
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Tier 3 & 4 Cities -->
        <div class="mb-16">
            <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Other Cities</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
                @foreach(array_merge($priorityCities['tier_3'], $priorityCities['tier_4']) as $cityName => $cityData)
                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                        <div class="h-32 bg-gradient-to-br from-green-500 to-blue-500 relative">
                            <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                            <div class="absolute bottom-2 left-2 text-white">
                                <h3 class="text-lg font-bold">{{ $cityName }}</h3>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="flex justify-between items-center">
                                <a href="{{ route('locations.show', \Str::slug($cityName)) }}"
                                   class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors">
                                    Guide
                                </a>
                                <a href="{{ route('escorts.index', ['location' => $cityName]) }}"
                                   class="text-green-600 hover:text-green-800 text-sm font-medium">
                                    Escorts
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Features Section -->
        <div class="bg-white rounded-lg shadow-lg p-8 mb-16">
            <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">What Our City Guides Include</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Verified Escorts</h3>
                    <p class="text-gray-600">Complete listings of verified professional escorts in each city with detailed profiles.</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Safety Guidelines</h3>
                    <p class="text-gray-600">Comprehensive safety information and guidelines specific to each city.</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Local Insights</h3>
                    <p class="text-gray-600">Local area information, popular districts, and city-specific recommendations.</p>
                </div>
            </div>
        </div>

        <!-- CTA Section -->
        <div class="bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg text-white p-8 text-center">
            <h2 class="text-3xl font-bold mb-4">Ready to Find Professional Companions?</h2>
            <p class="text-xl mb-6 opacity-90">
                Browse our verified escort directory or explore specific city guides for detailed local information.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('escorts.index') }}"
                   class="bg-white text-purple-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                    Browse All Escorts
                </a>
                <a href="{{ route('agencies.index') }}"
                   class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-purple-600 transition-colors">
                    View Agencies
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
