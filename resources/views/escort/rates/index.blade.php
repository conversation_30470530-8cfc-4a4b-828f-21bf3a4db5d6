<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Manage Rates') }}
            </h2>
            <a href="{{ route('profile.escort.edit') }}" class="inline-flex items-center px-4 py-2 bg-pink-500 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-pink-600 focus:bg-pink-600 active:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <i class="fas fa-user mr-1"></i> {{ __('Back to Profile') }}
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg shadow-sm mb-6" role="alert">
                    <div class="flex">
                        <div class="py-1"><i class="fas fa-check-circle fa-lg text-green-500 mr-3"></i></div>
                        <div>
                            <p class="font-medium">{{ session('success') }}</p>
                        </div>
                    </div>
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-0">
                    <!-- Tabs -->
                    <div class="border-b border-gray-200" x-data="{ activeTab: 'view' }">
                        <ul class="flex -mb-px">
                            <li class="mr-1">
                                <button @click="activeTab = 'view'" :class="{'border-pink-500 text-pink-600': activeTab === 'view', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'view'}" class="inline-block py-4 px-6 font-medium text-sm border-b-2 focus:outline-none">
                                    <i class="fas fa-list mr-2"></i> {{ __('Your Rates') }}
                                </button>
                            </li>
                            <li class="mr-1">
                                <button @click="activeTab = 'add'" :class="{'border-pink-500 text-pink-600': activeTab === 'add', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'add'}" class="inline-block py-4 px-6 font-medium text-sm border-b-2 focus:outline-none">
                                    <i class="fas fa-plus-square mr-2"></i> {{ __('Add New Rate') }}
                                </button>
                            </li>
                        </ul>
                    </div>

                    <!-- Add Rate Tab -->
                    <div x-show="activeTab === 'add'" class="p-6">
                        <div class="max-w-3xl mx-auto">
                            <header class="mb-6">
                                <div class="flex flex-col md:flex-row md:justify-between md:items-center">
                                    <div>
                                        <h2 class="text-xl font-medium text-gray-900">
                                            <i class="fas fa-plus-square mr-2 text-pink-500"></i> {{ __('Add New Rate') }}
                                        </h2>
                                        <p class="mt-1 text-sm text-gray-600">
                                            {{ __('Add your rates for different durations. Clients will see these rates on your profile.') }}
                                        </p>
                                    </div>
                                    <div class="mt-4 md:mt-0">
                                        <button @click="activeTab = 'view'" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                            <i class="fas fa-arrow-left mr-2"></i> {{ __('Back to Rates') }}
                                        </button>
                                    </div>
                                </div>
                            </header>

                            <form method="post" action="{{ route('escort.rates.store') }}" class="mt-6">
                                @csrf

                                @if($errors->has('price_error'))
                                    <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-lg mb-6" role="alert">
                                        <div class="flex">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-exclamation-triangle text-red-500 mt-0.5"></i>
                                            </div>
                                            <div class="ml-3">
                                                <p class="font-medium">{{ $errors->first('price_error') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                @endif

                                <div class="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm mb-6">
                                    <div class="p-5 border-b border-gray-200 bg-gradient-to-r from-pink-50 to-white">
                                        <h3 class="text-lg font-medium text-gray-900 flex items-center">
                                            <span class="flex-shrink-0 h-8 w-8 rounded-full bg-pink-100 flex items-center justify-center mr-3">
                                                <i class="fas fa-info-circle text-pink-500"></i>
                                            </span>
                                            {{ __('Rate Details') }}
                                        </h3>
                                    </div>
                                    <div class="p-5">
                                        <div class="space-y-6">
                                            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                                <h4 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
                                                    <i class="far fa-clock text-pink-500 mr-2"></i>
                                                    {{ __('Duration') }}
                                                </h4>
                                                <div x-data="{ duration: '1hour' }">
                                                    <div class="relative">
                                                        <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                                                            <i class="far fa-clock text-gray-400 text-lg"></i>
                                                        </div>
                                                        <select id="duration" name="duration" class="block w-full pl-11 pr-4 py-2.5 border border-gray-300 bg-white rounded-lg shadow-sm focus:border-pink-500 focus:ring-pink-500"
                                                            x-model="duration" required>
                                                            <option value="30min">30 minutes</option>
                                                            <option value="1hour">1 hour</option>
                                                            <option value="2hours">2 hours</option>
                                                            <option value="3hours">3 hours</option>
                                                            <option value="6hours">6 hours</option>
                                                            <option value="12hours">12 hours</option>
                                                            <option value="24hours">24 hours (overnight)</option>
                                                        </select>
                                                    </div>

                                                    <div class="mt-3 flex flex-wrap gap-2">
                                                        <!-- 30min button -->
                                                        <button type="button"
                                                            x-bind:class="duration === '30min' ?
                                                                'bg-blue-100 text-blue-800 border-blue-300' :
                                                                'bg-gray-100 text-gray-600 border-gray-300 hover:bg-gray-200'"
                                                            class="px-3 py-1 rounded-full text-xs font-medium border"
                                                            x-on:click="duration = '30min'">
                                                            30m
                                                        </button>

                                                        <!-- 1hour button -->
                                                        <button type="button"
                                                            x-bind:class="duration === '1hour' ?
                                                                'bg-green-100 text-green-800 border-green-300' :
                                                                'bg-gray-100 text-gray-600 border-gray-300 hover:bg-gray-200'"
                                                            class="px-3 py-1 rounded-full text-xs font-medium border"
                                                            x-on:click="duration = '1hour'">
                                                            1h
                                                        </button>

                                                        <!-- 2hours button -->
                                                        <button type="button"
                                                            x-bind:class="duration === '2hours' ?
                                                                'bg-purple-100 text-purple-800 border-purple-300' :
                                                                'bg-gray-100 text-gray-600 border-gray-300 hover:bg-gray-200'"
                                                            class="px-3 py-1 rounded-full text-xs font-medium border"
                                                            x-on:click="duration = '2hours'">
                                                            2h
                                                        </button>

                                                        <!-- 3hours button -->
                                                        <button type="button"
                                                            x-bind:class="duration === '3hours' ?
                                                                'bg-yellow-100 text-yellow-800 border-yellow-300' :
                                                                'bg-gray-100 text-gray-600 border-gray-300 hover:bg-gray-200'"
                                                            class="px-3 py-1 rounded-full text-xs font-medium border"
                                                            x-on:click="duration = '3hours'">
                                                            3h
                                                        </button>

                                                        <!-- 6hours button -->
                                                        <button type="button"
                                                            x-bind:class="duration === '6hours' ?
                                                                'bg-pink-100 text-pink-800 border-pink-300' :
                                                                'bg-gray-100 text-gray-600 border-gray-300 hover:bg-gray-200'"
                                                            class="px-3 py-1 rounded-full text-xs font-medium border"
                                                            x-on:click="duration = '6hours'">
                                                            6h
                                                        </button>

                                                        <!-- 12hours button -->
                                                        <button type="button"
                                                            x-bind:class="duration === '12hours' ?
                                                                'bg-indigo-100 text-indigo-800 border-indigo-300' :
                                                                'bg-gray-100 text-gray-600 border-gray-300 hover:bg-gray-200'"
                                                            class="px-3 py-1 rounded-full text-xs font-medium border"
                                                            x-on:click="duration = '12hours'">
                                                            12h
                                                        </button>

                                                        <!-- 24hours button -->
                                                        <button type="button"
                                                            x-bind:class="duration === '24hours' ?
                                                                'bg-red-100 text-red-800 border-red-300' :
                                                                'bg-gray-100 text-gray-600 border-gray-300 hover:bg-gray-200'"
                                                            class="px-3 py-1 rounded-full text-xs font-medium border"
                                                            x-on:click="duration = '24hours'">
                                                            24h
                                                        </button>
                                                    </div>
                                                    <x-input-error class="mt-2" :messages="$errors->get('duration')" />
                                                </div>
                                            </div>

                                            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                                <h4 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
                                                    <i class="fas fa-info-circle text-pink-500 mr-2"></i>
                                                    {{ __('Description (Optional)') }}
                                                </h4>
                                                <div x-data="{ charCount: 0, maxChars: 255 }">
                                                    <div class="relative">
                                                        <div class="absolute top-3 left-0 flex items-start pl-4 pointer-events-none">
                                                            <i class="fas fa-info-circle text-gray-400 text-lg"></i>
                                                        </div>
                                                        <textarea id="description" name="description"
                                                            x-on:input="charCount = $event.target.value.length"
                                                            class="block w-full pl-11 pr-4 py-2.5 border border-gray-300 bg-white rounded-lg shadow-sm focus:border-pink-500 focus:ring-pink-500"
                                                            placeholder="Special services included or any additional information"
                                                            rows="3"></textarea>
                                                        <div class="mt-1 text-xs text-gray-500 flex justify-end">
                                                            <span x-text="charCount"></span>/<span x-text="maxChars"></span> characters
                                                        </div>
                                                    </div>
                                                    <x-input-error class="mt-2" :messages="$errors->get('description')" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm mb-6">
                                    <div class="p-5 border-b border-gray-200 bg-gradient-to-r from-pink-50 to-white">
                                        <h3 class="text-lg font-medium text-gray-900 flex items-center">
                                            <span class="flex-shrink-0 h-8 w-8 rounded-full bg-pink-100 flex items-center justify-center mr-3">
                                                <i class="fas fa-money-bill-wave text-pink-500"></i>
                                            </span>
                                            {{ __('Pricing') }}
                                        </h3>
                                    </div>
                                    <div class="p-5">
                                        <div class="space-y-6">
                                            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                                <h4 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
                                                    <i class="fas fa-home text-pink-500 mr-2"></i>
                                                    {{ __('Incall Price Range') }}
                                                </h4>
                                                <div>
                                                   <div class="grid grid-cols-1 gap-2 md:grid-cols-2">

                                                        <div class="relative">
                                                            <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                                                                <i class="fas fa-arrow-down text-gray-400 text-lg"></i>
                                                            </div>
                                                            <div class="flex rounded-lg shadow-sm">
                                                                <x-text-input id="min_incall_price" name="min_incall_price" type="number" class="block w-full pl-11 pr-4 py-2.5 border border-gray-300 border-r-0 bg-white rounded-l-lg focus:border-pink-500 focus:ring-pink-500" min="0" placeholder="Min Amount" />
                                                                <span class="inline-flex items-center px-4 py-2.5 rounded-r-lg border border-l-0 border-gray-300 bg-gray-100 text-gray-700 font-medium">
                                                                    UGX
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <div class="relative">
                                                            <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                                                                <i class="fas fa-arrow-up text-gray-400 text-lg"></i>
                                                            </div>
                                                            <div class="flex rounded-lg shadow-sm">
                                                                <x-text-input id="max_incall_price" name="max_incall_price" type="number" class="block w-full pl-11 pr-4 py-2.5 border border-gray-300 bg-white rounded-l-lg focus:border-pink-500 focus:ring-pink-500" min="0" placeholder="Max Amount" />
                                                                <span class="inline-flex items-center px-4 py-2.5 rounded-r-lg border border-l-0 border-gray-300 bg-gray-100 text-gray-700 font-medium">
                                                                    UGX
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <p class="text-sm text-gray-500 mt-2">
                                                        <i class="fas fa-info-circle mr-1"></i> Enter price range for incall services (leave both empty if not offering)
                                                    </p>
                                                    <x-input-error class="mt-2" :messages="$errors->get('min_incall_price')" />
                                                    <x-input-error class="mt-2" :messages="$errors->get('max_incall_price')" />
                                                </div>
                                            </div>

                                            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                                <h4 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
                                                    <i class="fas fa-car text-pink-500 mr-2"></i>
                                                    {{ __('Outcall Price Range') }}
                                                </h4>
                                                <div>
                                                    <div class="grid grid-cols-1 gap-2 md:grid-cols-2">
                                                        <div class="relative">
                                                            <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                                                                <i class="fas fa-arrow-down text-gray-400 text-lg"></i>
                                                            </div>
                                                            <div class="flex rounded-lg shadow-sm">
                                                                <x-text-input id="min_outcall_price" name="min_outcall_price" type="number" class="block w-full pl-11 pr-4 py-2.5 border border-gray-300 bg-white rounded-l-lg focus:border-pink-500 focus:ring-pink-500" min="0" placeholder="Min Amount" />
                                                                <span class="inline-flex items-center px-4 py-2.5 rounded-r-lg border border-l-0 border-gray-300 bg-gray-100 text-gray-700 font-medium">
                                                                    UGX
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <div class="relative">
                                                            <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                                                                <i class="fas fa-arrow-up text-gray-400 text-lg"></i>
                                                            </div>
                                                            <div class="flex rounded-lg shadow-sm">
                                                                <x-text-input id="max_outcall_price" name="max_outcall_price" type="number" class="block w-full pl-11 pr-4 py-2.5 border border-gray-300 bg-white rounded-l-lg focus:border-pink-500 focus:ring-pink-500" min="0" placeholder="Max Amount" />
                                                                <span class="inline-flex items-center px-4 py-2.5 rounded-r-lg border border-l-0 border-gray-300 bg-gray-100 text-gray-700 font-medium">
                                                                    UGX
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <p class="text-sm text-gray-500 mt-2">
                                                        <i class="fas fa-info-circle mr-1"></i> Enter price range for outcall services (leave both empty if not offering)
                                                    </p>
                                                    <x-input-error class="mt-2" :messages="$errors->get('min_outcall_price')" />
                                                    <x-input-error class="mt-2" :messages="$errors->get('max_outcall_price')" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <input type="hidden" name="currency" value="UGX">

                                <div class="flex items-center justify-between mt-8">
                                    <button type="button" @click="activeTab = 'view'" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                        <i class="fas fa-times mr-2"></i> {{ __('Cancel') }}
                                    </button>
                                    <x-primary-button class="px-6 py-3 bg-pink-500 hover:bg-pink-600">
                                        <i class="fas fa-plus-circle mr-2"></i> {{ __('Add Rate') }}
                                    </x-primary-button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- View Rates Tab -->
                    <div x-show="activeTab === 'view'" class="p-6">
                        <div class="max-w-4xl mx-auto">
                            <header class="mb-6">
                                <div class="flex flex-col md:flex-row md:justify-between md:items-center">
                                    <div>
                                        <h2 class="text-xl font-medium text-gray-900">
                                            <i class="fas fa-list mr-2 text-pink-500"></i> {{ __('Your Rates') }}
                                        </h2>
                                        <p class="mt-1 text-sm text-gray-600">
                                            {{ __('Manage your service rates. These rates will be displayed on your profile.') }}
                                        </p>
                                    </div>
                                    @if($rates->count() > 0)
                                    <div class="mt-4 md:mt-0">
                                        <button @click="activeTab = 'add'" class="inline-flex items-center px-4 py-2 bg-pink-500 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-pink-600 focus:bg-pink-600 active:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                            <i class="fas fa-plus-circle mr-2"></i> {{ __('Add New Rate') }}
                                        </button>
                                    </div>
                                    @endif
                                </div>
                            </header>

                            @if($rates->count() > 0)
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    @foreach($rates as $rate)
                                        <div class="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 relative group">
                                            <!-- Duration Badge -->
                                            <div class="absolute top-0 right-0 mt-4 mr-4">
                                                @switch($rate->duration)
                                                    @case('30min')
                                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                            <i class="far fa-clock mr-1"></i> 30 minutes
                                                        </span>
                                                        @break
                                                    @case('1hour')
                                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                            <i class="far fa-clock mr-1"></i> 1 hour
                                                        </span>
                                                        @break
                                                    @case('2hours')
                                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                                            <i class="far fa-clock mr-1"></i> 2 hours
                                                        </span>
                                                        @break
                                                    @case('3hours')
                                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                            <i class="far fa-clock mr-1"></i> 3 hours
                                                        </span>
                                                        @break
                                                    @case('6hours')
                                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-pink-100 text-pink-800">
                                                            <i class="far fa-clock mr-1"></i> 6 hours
                                                        </span>
                                                        @break
                                                    @case('12hours')
                                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                                            <i class="far fa-clock mr-1"></i> 12 hours
                                                        </span>
                                                        @break
                                                    @case('24hours')
                                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                            <i class="far fa-clock mr-1"></i> 24 hours
                                                        </span>
                                                        @break
                                                    @default
                                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                            <i class="far fa-clock mr-1"></i> {{ $rate->human_duration }}
                                                        </span>
                                                @endswitch
                                            </div>

                                            <!-- Card Header -->
                                            <div class="p-5 border-b border-gray-100 bg-gradient-to-r from-pink-50 to-white">
                                                <h3 class="text-lg font-semibold text-gray-900">{{ $rate->human_duration }}</h3>
                                            </div>

                                            <!-- Card Body -->
                                            <div class="p-5">
                                                <div class="space-y-4">
                                                    <!-- Incall Price -->
                                                    <div class="flex items-start">
                                                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-pink-100 flex items-center justify-center">
                                                            <i class="fas fa-home text-pink-500"></i>
                                                        </div>
                                                        <div class="ml-4">
                                                            <h4 class="text-sm font-medium text-gray-500">Incall Price Range</h4>
                                                            @if($rate->has_incall_range || $rate->incall_price)
                                                                <p class="text-lg font-bold text-gray-900">{{ $rate->formatted_incall_price }}</p>
                                                                <p class="text-xs text-gray-500 mt-1">
                                                                    <i class="fas fa-info-circle mr-1"></i> Price varies based on services
                                                                </p>
                                                            @else
                                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-500">Not offered</span>
                                                            @endif
                                                        </div>
                                                    </div>

                                                    <!-- Outcall Price -->
                                                    <div class="flex items-start">
                                                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                                                            <i class="fas fa-car text-indigo-500"></i>
                                                        </div>
                                                        <div class="ml-4">
                                                            <h4 class="text-sm font-medium text-gray-500">Outcall Price Range</h4>
                                                            @if($rate->has_outcall_range || $rate->outcall_price)
                                                                <p class="text-lg font-bold text-gray-900">{{ $rate->formatted_outcall_price }}</p>
                                                                <p class="text-xs text-gray-500 mt-1">
                                                                    <i class="fas fa-info-circle mr-1"></i> Price varies based on services
                                                                </p>
                                                            @else
                                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-500">Not offered</span>
                                                            @endif
                                                        </div>
                                                    </div>

                                                    <!-- Description (if available) -->
                                                    @if($rate->description)
                                                    <div class="mt-4 pt-4 border-t border-gray-100">
                                                        <h4 class="text-sm font-medium text-gray-500 mb-1">Description</h4>
                                                        <p class="text-sm text-gray-700">{{ $rate->description }}</p>
                                                    </div>
                                                    @endif
                                                </div>
                                            </div>

                                            <!-- Card Footer -->
                                            <div class="px-5 py-4 bg-gray-50 border-t border-gray-100 flex justify-between items-center">
                                                <span class="text-xs text-gray-500">
                                                    <i class="fas fa-info-circle mr-1"></i> {{ __('Currency') }}: {{ $rate->currency }}
                                                </span>
                                                <div class="flex space-x-2">
                                                    <a href="{{ route('escort.rates.edit', $rate->id) }}" class="inline-flex items-center px-3 py-1.5 bg-indigo-50 text-indigo-700 rounded-md hover:bg-indigo-100 transition-colors">
                                                        <i class="fas fa-edit mr-1.5"></i> {{ __('Edit') }}
                                                    </a>
                                                    <form action="{{ route('escort.rates.destroy', $rate->id) }}" method="POST" class="inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="inline-flex items-center px-3 py-1.5 bg-red-50 text-red-700 rounded-md hover:bg-red-100 transition-colors" data-confirm="Are you sure you want to delete this rate?">
                                                            <i class="fas fa-trash-alt mr-1.5"></i> {{ __('Delete') }}
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>

                                <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-100 text-sm text-blue-700">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-info-circle text-blue-500 mt-0.5"></i>
                                        </div>
                                        <div class="ml-3">
                                            <p>{{ __('These rates will be displayed on your profile. Click on "Edit" to modify a rate or "Delete" to remove it.') }}</p>
                                        </div>
                                    </div>
                                </div>
                            @else
                                <div class="bg-white p-8 rounded-lg text-center border border-gray-200 shadow-sm">
                                    <div class="inline-flex items-center justify-center h-16 w-16 rounded-full bg-pink-100 text-pink-500 mb-6">
                                        <i class="fas fa-tags text-2xl"></i>
                                    </div>
                                    <h3 class="text-xl font-medium text-gray-900 mb-2">{{ __('No Rates Added Yet') }}</h3>
                                    <p class="text-gray-600 mb-8 max-w-md mx-auto">{{ __('You haven\'t added any rates for your services. Adding clear rates helps clients know what to expect.') }}</p>
                                    <button @click="activeTab = 'add'" class="inline-flex items-center px-5 py-3 bg-pink-500 border border-transparent rounded-md font-semibold text-sm text-white uppercase tracking-widest hover:bg-pink-600 focus:bg-pink-600 active:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 transition ease-in-out duration-150 shadow-sm">
                                        <i class="fas fa-plus-circle mr-2"></i> {{ __('Add Your First Rate') }}
                                    </button>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</x-app-layout>
