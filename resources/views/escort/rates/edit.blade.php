<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Edit Rate') }}
            </h2>
            <div class="flex space-x-4">
                <a href="{{ route('escort.rates') }}" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <i class="fas fa-arrow-left mr-1"></i> {{ __('Back to Rates') }}
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="max-w-2xl mx-auto">
                        <header class="mb-6">
                            <div class="flex flex-col md:flex-row md:justify-between md:items-center">
                                <div>
                                    <h2 class="text-xl font-medium text-gray-900">
                                        <i class="fas fa-edit mr-2 text-pink-500"></i> {{ __('Edit Rate') }}
                                    </h2>
                                    <p class="mt-1 text-sm text-gray-600">
                                        {{ __('Update your rate information for clients to see on your profile.') }}
                                    </p>
                                </div>
                            </div>
                        </header>

                        @if($errors->has('price_error'))
                            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-lg mb-6" role="alert">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-exclamation-triangle text-red-500 mt-0.5"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="font-medium">{{ $errors->first('price_error') }}</p>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <form method="post" action="{{ route('escort.rates.update', $rate->id) }}" class="mt-6">
                            @csrf
                            @method('patch')

                            <div class="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm mb-6">
                                <div class="p-5 border-b border-gray-200 bg-gradient-to-r from-pink-50 to-white">
                                    <h3 class="text-lg font-medium text-gray-900 flex items-center">
                                        <span class="flex-shrink-0 h-8 w-8 rounded-full bg-pink-100 flex items-center justify-center mr-3">
                                            <i class="fas fa-info-circle text-pink-500"></i>
                                        </span>
                                        {{ __('Rate Details') }}
                                    </h3>
                                </div>
                                <div class="p-5">
                                    <div class="space-y-6">
                                        <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                            <h4 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
                                                <i class="far fa-clock text-pink-500 mr-2"></i>
                                                {{ __('Duration') }}
                                            </h4>
                                            <div x-data="{ duration: '{{ $rate->duration }}' }">
                                                <div class="relative">
                                                    <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                                                        <i class="far fa-clock text-gray-400 text-lg"></i>
                                                    </div>
                                                    <select id="duration" name="duration" class="block w-full pl-11 pr-4 py-2.5 border border-gray-300 bg-white rounded-lg shadow-sm focus:border-pink-500 focus:ring-pink-500"
                                                        x-model="duration" required>
                                                        <option value="30min" {{ $rate->duration === '30min' ? 'selected' : '' }}>30 minutes</option>
                                                        <option value="1hour" {{ $rate->duration === '1hour' ? 'selected' : '' }}>1 hour</option>
                                                        <option value="2hours" {{ $rate->duration === '2hours' ? 'selected' : '' }}>2 hours</option>
                                                        <option value="3hours" {{ $rate->duration === '3hours' ? 'selected' : '' }}>3 hours</option>
                                                        <option value="6hours" {{ $rate->duration === '6hours' ? 'selected' : '' }}>6 hours</option>
                                                        <option value="12hours" {{ $rate->duration === '12hours' ? 'selected' : '' }}>12 hours</option>
                                                        <option value="24hours" {{ $rate->duration === '24hours' ? 'selected' : '' }}>24 hours (overnight)</option>
                                                    </select>
                                                </div>

                                                <div class="mt-3 flex flex-wrap gap-2">
                                                    <!-- 30min button -->
                                                    <button type="button"
                                                        x-bind:class="duration === '30min' ?
                                                            'bg-blue-100 text-blue-800 border-blue-300' :
                                                            'bg-gray-100 text-gray-600 border-gray-300 hover:bg-gray-200'"
                                                        class="px-3 py-1 rounded-full text-xs font-medium border"
                                                        x-on:click="duration = '30min'">
                                                        30m
                                                    </button>

                                                    <!-- 1hour button -->
                                                    <button type="button"
                                                        x-bind:class="duration === '1hour' ?
                                                            'bg-green-100 text-green-800 border-green-300' :
                                                            'bg-gray-100 text-gray-600 border-gray-300 hover:bg-gray-200'"
                                                        class="px-3 py-1 rounded-full text-xs font-medium border"
                                                        x-on:click="duration = '1hour'">
                                                        1h
                                                    </button>

                                                    <!-- 2hours button -->
                                                    <button type="button"
                                                        x-bind:class="duration === '2hours' ?
                                                            'bg-purple-100 text-purple-800 border-purple-300' :
                                                            'bg-gray-100 text-gray-600 border-gray-300 hover:bg-gray-200'"
                                                        class="px-3 py-1 rounded-full text-xs font-medium border"
                                                        x-on:click="duration = '2hours'">
                                                        2h
                                                    </button>

                                                    <!-- 3hours button -->
                                                    <button type="button"
                                                        x-bind:class="duration === '3hours' ?
                                                            'bg-yellow-100 text-yellow-800 border-yellow-300' :
                                                            'bg-gray-100 text-gray-600 border-gray-300 hover:bg-gray-200'"
                                                        class="px-3 py-1 rounded-full text-xs font-medium border"
                                                        x-on:click="duration = '3hours'">
                                                        3h
                                                    </button>

                                                    <!-- 6hours button -->
                                                    <button type="button"
                                                        x-bind:class="duration === '6hours' ?
                                                            'bg-pink-100 text-pink-800 border-pink-300' :
                                                            'bg-gray-100 text-gray-600 border-gray-300 hover:bg-gray-200'"
                                                        class="px-3 py-1 rounded-full text-xs font-medium border"
                                                        x-on:click="duration = '6hours'">
                                                        6h
                                                    </button>

                                                    <!-- 12hours button -->
                                                    <button type="button"
                                                        x-bind:class="duration === '12hours' ?
                                                            'bg-indigo-100 text-indigo-800 border-indigo-300' :
                                                            'bg-gray-100 text-gray-600 border-gray-300 hover:bg-gray-200'"
                                                        class="px-3 py-1 rounded-full text-xs font-medium border"
                                                        x-on:click="duration = '12hours'">
                                                        12h
                                                    </button>

                                                    <!-- 24hours button -->
                                                    <button type="button"
                                                        x-bind:class="duration === '24hours' ?
                                                            'bg-red-100 text-red-800 border-red-300' :
                                                            'bg-gray-100 text-gray-600 border-gray-300 hover:bg-gray-200'"
                                                        class="px-3 py-1 rounded-full text-xs font-medium border"
                                                        x-on:click="duration = '24hours'">
                                                        24h
                                                    </button>
                                                </div>
                                                <x-input-error class="mt-2" :messages="$errors->get('duration')" />
                                            </div>
                                        </div>

                                        <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                            <h4 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
                                                <i class="fas fa-info-circle text-pink-500 mr-2"></i>
                                                {{ __('Description (Optional)') }}
                                            </h4>
                                            <div x-data="{ charCount: {{ strlen($rate->description) }}, maxChars: 255 }">
                                                <div class="relative">
                                                    <div class="absolute top-3 left-0 flex items-start pl-4 pointer-events-none">
                                                        <i class="fas fa-info-circle text-gray-400 text-lg"></i>
                                                    </div>
                                                    <textarea id="description" name="description"
                                                        x-on:input="charCount = $event.target.value.length"
                                                        class="block w-full pl-11 pr-4 py-2.5 border border-gray-300 bg-white rounded-lg shadow-sm focus:border-pink-500 focus:ring-pink-500"
                                                        placeholder="Special services included or any additional information"
                                                        rows="3">{{ $rate->description }}</textarea>
                                                    <div class="mt-1 text-xs text-gray-500 flex justify-end">
                                                        <span x-text="charCount"></span>/<span x-text="maxChars"></span> characters
                                                    </div>
                                                </div>
                                                <x-input-error class="mt-2" :messages="$errors->get('description')" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm mb-6">
                                <div class="p-5 border-b border-gray-200 bg-gradient-to-r from-pink-50 to-white">
                                    <h3 class="text-lg font-medium text-gray-900 flex items-center">
                                        <span class="flex-shrink-0 h-8 w-8 rounded-full bg-pink-100 flex items-center justify-center mr-3">
                                            <i class="fas fa-money-bill-wave text-pink-500"></i>
                                        </span>
                                        {{ __('Pricing') }}
                                    </h3>
                                </div>
                                <div class="p-5">
                                    <div class="space-y-6">
                                        <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                            <h4 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
                                                <i class="fas fa-home text-pink-500 mr-2"></i>
                                                {{ __('Incall Price Range') }}
                                            </h4>
                                            <div>
                                                <div class="grid grid-cols-2 gap-2">
                                                    <div class="relative">
                                                        <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                                                            <i class="fas fa-arrow-down text-gray-400 text-lg"></i>
                                                        </div>
                                                        <div class="flex rounded-lg shadow-sm">
                                                            <x-text-input id="min_incall_price" name="min_incall_price" type="number" class="block w-full pl-11 pr-4 py-2.5 border border-gray-300 bg-white rounded-l-lg focus:border-pink-500 focus:ring-pink-500" min="0" placeholder="Min Amount" value="{{ $rate->min_incall_price ?? $rate->incall_price }}" />
                                                            <span class="inline-flex items-center px-4 py-2.5 rounded-r-lg border border-l-0 border-gray-300 bg-gray-100 text-gray-700 font-medium">
                                                                UGX
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="relative">
                                                        <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                                                            <i class="fas fa-arrow-up text-gray-400 text-lg"></i>
                                                        </div>
                                                        <div class="flex rounded-lg shadow-sm">
                                                            <x-text-input id="max_incall_price" name="max_incall_price" type="number" class="block w-full pl-11 pr-4 py-2.5 border border-gray-300 bg-white rounded-l-lg focus:border-pink-500 focus:ring-pink-500" min="0" placeholder="Max Amount" value="{{ $rate->max_incall_price ?? $rate->incall_price }}" />
                                                            <span class="inline-flex items-center px-4 py-2.5 rounded-r-lg border border-l-0 border-gray-300 bg-gray-100 text-gray-700 font-medium">
                                                                UGX
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <p class="text-sm text-gray-500 mt-2">
                                                    <i class="fas fa-info-circle mr-1"></i> Enter price range for incall services (leave both empty if not offering)
                                                </p>
                                                <x-input-error class="mt-2" :messages="$errors->get('min_incall_price')" />
                                                <x-input-error class="mt-2" :messages="$errors->get('max_incall_price')" />
                                            </div>
                                        </div>

                                        <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                            <h4 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
                                                <i class="fas fa-car text-pink-500 mr-2"></i>
                                                {{ __('Outcall Price Range') }}
                                            </h4>
                                            <div>
                                                <div class="grid grid-cols-2 gap-2">
                                                    <div class="relative">
                                                        <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                                                            <i class="fas fa-arrow-down text-gray-400 text-lg"></i>
                                                        </div>
                                                        <div class="flex rounded-lg shadow-sm">
                                                            <x-text-input id="min_outcall_price" name="min_outcall_price" type="number" class="block w-full pl-11 pr-4 py-2.5 border border-gray-300 bg-white rounded-l-lg focus:border-pink-500 focus:ring-pink-500" min="0" placeholder="Min Amount" value="{{ $rate->min_outcall_price ?? $rate->outcall_price }}" />
                                                            <span class="inline-flex items-center px-4 py-2.5 rounded-r-lg border border-l-0 border-gray-300 bg-gray-100 text-gray-700 font-medium">
                                                                UGX
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="relative">
                                                        <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                                                            <i class="fas fa-arrow-up text-gray-400 text-lg"></i>
                                                        </div>
                                                        <div class="flex rounded-lg shadow-sm">
                                                            <x-text-input id="max_outcall_price" name="max_outcall_price" type="number" class="block w-full pl-11 pr-4 py-2.5 border border-gray-300 bg-white rounded-l-lg focus:border-pink-500 focus:ring-pink-500" min="0" placeholder="Max Amount" value="{{ $rate->max_outcall_price ?? $rate->outcall_price }}" />
                                                            <span class="inline-flex items-center px-4 py-2.5 rounded-r-lg border border-l-0 border-gray-300 bg-gray-100 text-gray-700 font-medium">
                                                                UGX
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <p class="text-sm text-gray-500 mt-2">
                                                    <i class="fas fa-info-circle mr-1"></i> Enter price range for outcall services (leave both empty if not offering)
                                                </p>
                                                <x-input-error class="mt-2" :messages="$errors->get('min_outcall_price')" />
                                                <x-input-error class="mt-2" :messages="$errors->get('max_outcall_price')" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <input type="hidden" name="currency" value="UGX">

                            <div class="flex items-center justify-between mt-8">
                                <a href="{{ route('escort.rates') }}" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                    <i class="fas fa-arrow-left mr-2"></i> {{ __('Back to Rates') }}
                                </a>
                                <div class="flex gap-3">
                                    <a href="{{ route('escort.rates') }}" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                        <i class="fas fa-times mr-2"></i> {{ __('Cancel') }}
                                    </a>
                                    <x-primary-button class="px-6 py-3 bg-pink-500 hover:bg-pink-600">
                                        <i class="fas fa-save mr-2"></i> {{ __('Update Rate') }}
                                    </x-primary-button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
