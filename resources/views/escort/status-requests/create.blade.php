<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Request Verification or Featured Status') }}
            </h2>
            <a href="{{ route('escort.status-requests.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                {{ __('Back to Requests') }}
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="mb-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Choose Request Type</h3>
                        <p class="text-gray-600 mb-4">Select the type of request you want to make. You can request to be verified or featured on our platform.</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Verification Request Card -->
                        <div class="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
                            <div class="p-5 border-b border-gray-200 bg-green-50">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-green-600 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <h4 class="text-lg font-medium text-gray-900">Verification</h4>
                                </div>
                            </div>
                            <div class="p-5">
                                <p class="text-gray-600 mb-4">Get verified to show clients that you are a genuine escort. Verified escorts appear higher in search results and get more visibility.</p>

                                <ul class="mb-6 space-y-2">
                                    <li class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                        <span class="text-gray-700">Green verification badge</span>
                                    </li>
                                    <li class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                        <span class="text-gray-700">Higher ranking in search results</span>
                                    </li>
                                    <li class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                        <span class="text-gray-700">More client trust and bookings</span>
                                    </li>
                                </ul>

                                @if($isVerified)
                                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                                        <strong class="font-bold">Already Verified!</strong>
                                        <span class="block sm:inline">You are already verified. No need to request verification again.</span>
                                    </div>
                                @elseif($pendingVerification)
                                    <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-4" role="alert">
                                        <strong class="font-bold">Pending Request!</strong>
                                        <span class="block sm:inline">You have a pending verification request submitted on {{ $pendingVerification->created_at->format('M d, Y') }}.</span>
                                        <div class="mt-2">
                                            <a href="{{ route('escort.status-requests.show', $pendingVerification->id) }}" class="text-yellow-800 underline hover:text-yellow-900">View Request Details</a>
                                        </div>
                                    </div>
                                @else
                                    <form method="post" action="{{ route('escort.status-requests.store') }}" id="verification-form">
                                        @csrf
                                        <input type="hidden" name="request_type" value="verification">

                                        <div class="mb-4">
                                            <label for="verification_duration" class="block text-sm font-medium text-gray-700 mb-1">Duration & Pricing</label>
                                            <div class="mt-2 space-y-2">
                                                @if($verificationPricing->isEmpty())
                                                    <p class="text-sm text-red-600">No pricing options available. Please contact admin.</p>
                                                @else
                                                    @foreach($verificationPricing as $pricing)
                                                        <div class="flex items-center">
                                                            <input id="verification_duration_{{ $pricing->duration }}" name="duration" type="radio" value="{{ $pricing->duration }}" class="h-4 w-4 border-gray-300 text-green-600 focus:ring-green-500" {{ $loop->first ? 'checked' : '' }}>
                                                            <label for="verification_duration_{{ $pricing->duration }}" class="ml-3 block text-sm font-medium text-gray-700">
                                                                {{ $pricing->formatted_duration }} - {{ $pricing->formatted_price }}
                                                            </label>
                                                        </div>
                                                    @endforeach
                                                @endif
                                            </div>
                                            @error('duration')
                                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                            Request Verification
                                        </button>
                                    </form>
                                @endif
                            </div>
                        </div>

                        <!-- Featured Request Card -->
                        <div class="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
                            <div class="p-5 border-b border-gray-200 bg-purple-50">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-purple-600 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                                    </svg>
                                    <h4 class="text-lg font-medium text-gray-900">Featured</h4>
                                </div>
                            </div>
                            <div class="p-5">
                                <p class="text-gray-600 mb-4">Get featured to appear in the featured section on the homepage. Featured escorts get significantly more visibility and clients.</p>

                                <ul class="mb-6 space-y-2">
                                    <li class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                        <span class="text-gray-700">Appear in the featured section</span>
                                    </li>
                                    <li class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                        <span class="text-gray-700">Get up to 5x more profile views</span>
                                    </li>
                                    <li class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                        <span class="text-gray-700">Stand out from other escorts</span>
                                    </li>
                                </ul>

                                @if($isFeatured)
                                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                                        <strong class="font-bold">Already Featured!</strong>
                                        <span class="block sm:inline">You are currently featured{{ $featuredExpiresAt ? ' until ' . $featuredExpiresAt->format('M d, Y') : '' }}.</span>
                                    </div>
                                @elseif($pendingFeatured)
                                    <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-4" role="alert">
                                        <strong class="font-bold">Pending Request!</strong>
                                        <span class="block sm:inline">You have a pending featured request submitted on {{ $pendingFeatured->created_at->format('M d, Y') }}.</span>
                                        <div class="mt-2">
                                            <a href="{{ route('escort.status-requests.show', $pendingFeatured->id) }}" class="text-yellow-800 underline hover:text-yellow-900">View Request Details</a>
                                        </div>
                                    </div>
                                @else
                                    <form method="post" action="{{ route('escort.status-requests.store') }}" id="featured-form">
                                        @csrf
                                        <input type="hidden" name="request_type" value="featured">

                                        <div class="mb-4">
                                            <label for="duration" class="block text-sm font-medium text-gray-700 mb-1">Duration & Pricing</label>
                                            <div class="mt-2 space-y-2">
                                                @if($featuredPricing->isEmpty())
                                                    <p class="text-sm text-red-600">No pricing options available. Please contact admin.</p>
                                                @else
                                                    @foreach($featuredPricing as $pricing)
                                                        <div class="flex items-center">
                                                            <input id="featured_duration_{{ $pricing->duration }}" name="duration" type="radio" value="{{ $pricing->duration }}" class="h-4 w-4 border-gray-300 text-purple-600 focus:ring-purple-500" {{ $loop->first ? 'checked' : '' }}>
                                                            <label for="featured_duration_{{ $pricing->duration }}" class="ml-3 block text-sm font-medium text-gray-700">
                                                                {{ $pricing->formatted_duration }} - {{ $pricing->formatted_price }}
                                                            </label>
                                                        </div>
                                                    @endforeach
                                                @endif
                                            </div>
                                            @error('duration')
                                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 bg-purple-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-purple-700 focus:bg-purple-700 active:bg-purple-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                            Request Featured Status
                                        </button>
                                    </form>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
