<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Manage Services') }}
            </h2>
            <a href="{{ route('profile.escort.edit') }}" class="inline-flex items-center px-4 py-2 bg-pink-500 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-pink-600 focus:bg-pink-600 active:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <i class="fas fa-user mr-1"></i> {{ __('Back to Profile') }}
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <div class="p-4 sm:p-8 bg-white shadow sm:rounded-lg">
                <div>
                    @if(session('success'))
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg shadow-sm mb-6" role="alert">
                            <div class="flex">
                                <div class="py-1"><i class="fas fa-check-circle fa-lg text-green-500 mr-3"></i></div>
                                <div>
                                    <p class="font-medium">{{ session('success') }}</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <section>
                        <header class="mb-6">
                            <h2 class="text-xl font-medium text-gray-900">
                                <i class="fas fa-concierge-bell mr-2 text-pink-500"></i> {{ __('Select Your Services') }}
                            </h2>

                            <p class="mt-1 text-sm text-gray-600">
                                {{ __('Choose the services you offer to clients. These will be displayed on your profile.') }}
                            </p>
                        </header>

                        <form method="post" action="{{ route('escort.services.update') }}" class="mt-6">
                            @csrf

                            <div class="bg-white rounded-lg border border-gray-200 overflow-hidden mb-6">
                                <div class="p-5 border-b border-gray-200 bg-gray-50">
                                    <h3 class="text-lg font-medium text-gray-900">
                                        <i class="fas fa-list-check mr-2 text-pink-500"></i> {{ __('Available Services') }}
                                    </h3>
                                </div>
                                <div class="p-5">
                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        @foreach($allServices as $service)
                                            <div class="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors duration-150">
                                                <div class="flex items-center h-5">
                                                    <input id="service_{{ $service->id }}" name="services[]" type="checkbox" value="{{ $service->id }}" class="w-5 h-5 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-pink-300 text-pink-500" {{ in_array($service->id, $selectedServices) ? 'checked' : '' }}>
                                                </div>
                                                <div class="ml-3 text-sm">
                                                    <label for="service_{{ $service->id }}" class="font-medium text-gray-900 cursor-pointer">{{ $service->name }}</label>
                                                    @if($service->description)
                                                        <p class="text-xs text-gray-500 mt-1">{{ $service->description }}</p>
                                                    @endif
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>

                            <div class="flex items-center justify-end mt-8">
                                <x-primary-button class="px-6 py-3 bg-pink-500 hover:bg-pink-600">
                                    <i class="fas fa-save mr-2"></i> {{ __('Save Services') }}
                                </x-primary-button>
                            </div>
                        </form>
                    </section>
                </div>
            </div>


        </div>
    </div>
</x-app-layout>
