<x-app-layout>
    <x-slot name="header">
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0">
            <h2 class="font-bold text-xl text-gray-900 leading-tight flex items-center">
                <span class="bg-gradient-to-r from-purple-600 to-pink-600 w-1 h-6 rounded mr-2 inline-block"></span>
                {{ __('Manage Photos') }}
            </h2>
            <a href="{{ route('dashboard') }}" class="inline-flex items-center px-5 py-2.5 bg-gradient-to-r from-purple-600 to-pink-600 border border-transparent rounded-lg font-semibold text-sm text-white tracking-wide hover:from-purple-700 hover:to-pink-700 focus:from-purple-700 focus:to-pink-700 active:from-purple-800 active:to-pink-800 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-300 transform hover:scale-105 shadow-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                {{ __('Dashboard') }}
            </a>
        </div>
    </x-slot>

    <div class="py-6 sm:py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6 sm:space-y-8">
            <!-- Photos Banner -->
            <div class="bg-gradient-to-r from-pink-500 to-pink-700 rounded-xl shadow-xl overflow-hidden relative">
                <div class="absolute inset-0 opacity-10">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" class="w-full h-full text-white">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                </div>
                <div class="px-6 py-8 md:px-8 md:flex md:items-center md:justify-between relative z-10">
                    <div>
                        <h2 class="text-3xl font-bold text-white">{{ __('Your Photo Gallery') }}</h2>
                        <p class="mt-2 text-white text-opacity-90">{{ __('High-quality images will attract more clients') }}</p>
                    </div>
                    <div class="mt-4 md:mt-0">
                        <div class="bg-white bg-opacity-20 backdrop-blur-sm px-6 py-3 rounded-lg text-white shadow-inner border border-white border-opacity-20">
                            <span class="font-bold text-3xl">{{ $images->count() }}</span>
                            <span class="ml-2 text-lg">{{ __('/ 4 Photos Max') }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-6 sm:p-8 bg-white shadow-lg sm:rounded-xl">
                <div class="max-w-3xl mx-auto">
                    @if(session('success'))
                        <div class="bg-gradient-to-r from-green-50 to-emerald-50 border-l-4 border-green-500 text-green-700 p-4 rounded-md shadow-sm mb-6 flex items-center" role="alert">
                            <svg class="h-5 w-5 text-green-600 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span class="font-medium text-green-800">{{ session('success') }}</span>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="bg-gradient-to-r from-red-50 to-red-50 border-l-4 border-red-500 text-red-700 p-4 rounded-md shadow-sm mb-6 flex items-center" role="alert">
                            <svg class="h-5 w-5 text-red-600 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span class="font-medium text-red-800">{{ session('error') }}</span>
                        </div>
                    @endif

                    <section>
                        <header class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
                            <div>
                                <h2 class="text-xl font-bold text-gray-900 flex items-center">
                                    <span class="bg-gradient-to-r from-pink-500 to-pink-600 w-1 h-6 rounded mr-2 inline-block"></span>
                                    {{ __('Upload New Photo') }}
                                </h2>
                                <p class="mt-2 text-sm text-gray-600 ml-3">
                                    {{ __('Add photos to your profile. High-quality images will attract more clients.') }}
                                </p>
                            </div>
                            <div class="flex-shrink-0 bg-gradient-to-r from-pink-100 to-pink-200 rounded-full p-3 shadow-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-pink-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                            </div>
                        </header>

                        @if($images->count() >= 4)
                            <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-6 text-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-yellow-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                                <h3 class="text-lg font-medium text-yellow-800 mb-2">Photo Limit Reached</h3>
                                <p class="text-yellow-700 mb-4">You can only upload 4 photos maximum. Please delete some photos to upload new ones.</p>
                            </div>
                        @else
                            <form method="post" action="{{ route('escort.photos.store') }}" class="space-y-6" enctype="multipart/form-data" id="photoUploadForm">
                                @csrf

                                <div class="mt-2">
                                    <!-- Drag & Drop Upload Area -->
                                    <div class="relative">
                                        <input id="image" name="image" type="file" accept="image/jpeg,image/png,image/jpg" class="absolute inset-0 w-full h-full opacity-0 z-50 cursor-pointer" required />
                                        <div id="dropArea" class="border-2 border-dashed border-pink-300 rounded-xl p-8 text-center transition-all duration-200 hover:border-pink-500 bg-pink-50 hover:bg-pink-100">
                                        <div class="flex flex-col items-center justify-center space-y-4">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-pink-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                                            </svg>
                                            <div>
                                                <p class="text-lg font-medium text-pink-700">Drag and drop your photo here</p>
                                                <p class="text-sm text-gray-600 mt-1">or click to browse files</p>
                                            </div>
                                            <p class="text-xs text-gray-500 mt-2">JPEG, PNG, JPG up to 5MB</p>
                                        </div>
                                    </div>
                                </div>
                                <x-input-error class="mt-2" :messages="$errors->get('image')" />
                            </div>

                            <!-- Image Preview Section -->
                            <div id="imagePreviewContainer" class="hidden mt-6">
                                <div class="bg-white rounded-xl overflow-hidden border border-pink-200 shadow-md">
                                    <div class="bg-gradient-to-r from-pink-500 to-pink-600 text-white text-sm font-medium px-4 py-2 flex justify-between items-center">
                                        <span>Image Preview</span>
                                        <button type="button" id="cancelPreview" class="text-white hover:text-pink-200 focus:outline-none transition-colors duration-200">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                            </svg>
                                        </button>
                                    </div>
                                    <div class="p-4">
                                        <div class="relative rounded-lg overflow-hidden bg-gray-100">
                                            <img id="imagePreview" src="#" alt="Preview" class="w-full h-64 object-contain">
                                        </div>
                                        <div class="mt-4">
                                            <label for="is_main" class="inline-flex items-center cursor-pointer">
                                                <input id="is_main" type="checkbox" class="rounded border-gray-300 text-pink-600 shadow-sm focus:ring-pink-500 focus:ring-offset-2" name="is_main" value="1">
                                                <span class="ml-2 text-sm text-gray-800 font-medium">{{ __('Set as main profile image') }}</span>
                                            </label>
                                            <p class="mt-1 text-xs text-gray-600 ml-6">This image will be displayed as your primary photo on your profile</p>
                                            <x-input-error class="mt-2" :messages="$errors->get('is_main')" />
                                        </div>
                                        <div class="mt-4 flex justify-end">
                                            <button type="submit" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-pink-500 to-pink-600 border border-transparent rounded-lg font-semibold text-sm text-white uppercase tracking-widest hover:from-pink-600 hover:to-pink-700 focus:from-pink-600 focus:to-pink-700 active:from-pink-700 active:to-pink-800 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 transition-all duration-300 transform hover:scale-105 shadow-md">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                                                </svg>
                                                {{ __('Upload Photo') }}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                        @endif
                    </section>
                </div>
            </div>

            <div class="p-6 sm:p-8 bg-white shadow-lg sm:rounded-xl">
                <div>
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
                        <div>
                            <h2 class="text-xl font-bold text-gray-900 flex items-center">
                                <span class="bg-gradient-to-r from-pink-500 to-pink-600 w-1 h-6 rounded mr-2 inline-block"></span>
                                {{ __('Your Photos') }}
                            </h2>
                            <p class="text-sm text-gray-600 ml-3">{{ __('Manage your photo gallery') }}</p>
                        </div>
                        <div class="flex-shrink-0 bg-gradient-to-r from-pink-100 to-pink-200 rounded-full p-3 shadow-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-pink-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                        </div>
                    </div>

                    @if($images->count() > 0)
                        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5 sm:gap-6">
                            @foreach($images as $image)
                                <div class="relative group overflow-hidden rounded-xl shadow-md border border-gray-200 hover:shadow-xl transition-all duration-300 bg-white">
                                    <div class="aspect-w-3 aspect-h-4 bg-gray-100">
                                        <img src="{{ asset('storage/' . $image->path) }}" alt="Escort photo" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">
                                    </div>

                                    @if($image->is_main)
                                        <div class="absolute top-3 left-3 bg-gradient-to-r from-pink-500 to-pink-600 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-md z-10">
                                            <span class="flex items-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                                </svg>
                                                Main Photo
                                            </span>
                                        </div>
                                    @endif

                                    <!-- Overlay with actions -->
                                    <div class="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex flex-col justify-end p-4">
                                        <div class="transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300 space-y-2">
                                            @if(!$image->is_main)
                                                <form action="{{ route('escort.photos.set-main', $image->id) }}" method="POST" class="w-full">
                                                    @csrf
                                                    <button type="submit" class="w-full bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 focus:from-pink-600 focus:to-pink-700 active:from-pink-700 active:to-pink-800 text-white font-medium py-2 px-4 rounded-lg text-sm shadow-md transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-1 border border-transparent flex items-center justify-center">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                                        </svg>
                                                        Set as Main
                                                    </button>
                                                </form>
                                            @endif
                                            <form action="{{ route('escort.photos.destroy', $image->id) }}" method="POST" class="w-full">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 focus:from-red-600 focus:to-red-700 active:from-red-700 active:to-red-800 text-white font-medium py-2 px-4 rounded-lg text-sm shadow-md transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1 border border-transparent flex items-center justify-center" data-confirm="Are you sure you want to delete this photo?">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                    </svg>
                                                    Delete Photo
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="bg-gradient-to-r from-pink-50 to-pink-100 p-8 rounded-xl text-center border border-pink-200 shadow-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-pink-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <p class="text-gray-800 font-medium text-lg mb-2">You haven't uploaded any photos yet.</p>
                            <p class="text-gray-600 mb-6">Add photos to make your profile more attractive to clients.</p>
                            <a href="#" onclick="document.getElementById('image').click(); return false;" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-pink-500 to-pink-600 text-white font-semibold text-sm uppercase tracking-widest rounded-lg shadow-md hover:from-pink-600 hover:to-pink-700 focus:from-pink-600 focus:to-pink-700 active:from-pink-700 active:to-pink-800 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 transition-all duration-300 transform hover:scale-105 border border-transparent">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                </svg>
                                Add Your First Photo
                            </a>
                        </div>
                    @endif
                </div>
            </div>

            <div class="p-4 sm:p-8 bg-white shadow-lg sm:rounded-xl">
                <div class="max-w-xl mx-auto">
                    <div class="flex justify-between items-center">
                        <a href="{{ route('profile.escort.edit') }}" class="inline-flex items-center text-lg font-medium text-pink-600 hover:text-pink-800 transition-colors duration-200">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                            </svg>
                            {{ __('Back to Profile') }}
                        </a>
                        <a href="{{ route('profile.escort.edit') }}" class="inline-flex items-center px-4 py-2 bg-pink border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-pink-600 focus:bg-pink-600 active:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            {{ __('Profile Settings') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const imageInput = document.getElementById('image');
            const imagePreview = document.getElementById('imagePreview');
            const previewContainer = document.getElementById('imagePreviewContainer');
            const cancelPreviewBtn = document.getElementById('cancelPreview');
            const photoForm = document.getElementById('photoUploadForm');
            const dropArea = document.getElementById('dropArea');

            // Function to handle the file display
            function handleFile(file) {
                if (file) {
                    // Validate file type
                    const validTypes = ['image/jpeg', 'image/png', 'image/jpg'];
                    if (!validTypes.includes(file.type)) {
                        alert('Please select a valid image file (JPEG, PNG, JPG)');
                        return;
                    }

                    // Validate file size (5MB max)
                    if (file.size > 5 * 1024 * 1024) {
                        alert('File size exceeds 5MB limit');
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = function(e) {
                        imagePreview.src = e.target.result;
                        previewContainer.classList.remove('hidden');

                        // Add active state to drop area
                        dropArea.classList.add('border-pink-500', 'bg-pink-100');
                    }
                    reader.readAsDataURL(file);
                }
            }

            // Show preview when image is selected via input
            imageInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    handleFile(this.files[0]);
                }
            });

            // Cancel preview button
            cancelPreviewBtn.addEventListener('click', function() {
                imageInput.value = '';
                previewContainer.classList.add('hidden');
                imagePreview.src = '#';
                dropArea.classList.remove('border-pink-500', 'bg-pink-100');
            });

            // Drag and drop functionality
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            // Highlight drop area when item is dragged over it
            ['dragenter', 'dragover'].forEach(eventName => {
                dropArea.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, unhighlight, false);
            });

            function highlight() {
                dropArea.classList.add('border-pink-500', 'bg-pink-100', 'border-solid');
                dropArea.classList.remove('border-dashed');
            }

            function unhighlight() {
                dropArea.classList.remove('border-solid', 'border-pink-500', 'bg-pink-100');
                dropArea.classList.add('border-dashed');
            }

            // Handle dropped files
            dropArea.addEventListener('drop', handleDrop, false);

            function handleDrop(e) {
                const dt = e.dataTransfer;
                const file = dt.files[0];

                // Set the file to the input element
                if (file) {
                    const dataTransfer = new DataTransfer();
                    dataTransfer.items.add(file);
                    imageInput.files = dataTransfer.files;

                    handleFile(file);
                }
            }
        });
    </script>
</x-app-layout>
