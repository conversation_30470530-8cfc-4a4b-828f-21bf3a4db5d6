<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Manage Locations') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <div class="p-4 sm:p-8 bg-white shadow sm:rounded-lg">
                <div>
                    @if(session('success'))
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                            <span class="block sm:inline">{{ session('success') }}</span>
                        </div>
                    @endif

                    <section>
                        <header>
                            <h2 class="text-lg font-medium text-gray-900">
                                {{ __('Select Your Locations') }}
                            </h2>

                            <p class="mt-1 text-sm text-gray-600">
                                {{ __('Choose the locations where you offer your services.') }}
                            </p>
                        </header>

                        <form method="post" action="{{ route('escort.locations.update') }}" class="mt-6">
                            @csrf

                            <div class="space-y-6">
                                @foreach($countries as $country)
                                    <div>
                                        <h3 class="text-md font-medium text-gray-900 mb-2">{{ $country->name }}</h3>

                                        <div class="ml-4">
                                            <div class="flex items-start mb-2">
                                                <div class="flex items-center h-5">
                                                    <input id="location_{{ $country->id }}" name="locations[]" type="checkbox" value="{{ $country->id }}" class="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-pink-300" {{ in_array($country->id, $selectedLocations) ? 'checked' : '' }}>
                                                </div>
                                                <div class="ml-3 text-sm">
                                                    <label for="location_{{ $country->id }}" class="font-medium text-gray-900">Entire {{ $country->name }}</label>
                                                </div>
                                            </div>

                                            @if($country->children->count() > 0)
                                                <div class="ml-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                                                    @foreach($country->children as $city)
                                                        <div class="flex items-start">
                                                            <div class="flex items-center h-5">
                                                                <input id="location_{{ $city->id }}" name="locations[]" type="checkbox" value="{{ $city->id }}" class="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-pink-300" {{ in_array($city->id, $selectedLocations) ? 'checked' : '' }}>
                                                            </div>
                                                            <div class="ml-3 text-sm">
                                                                <label for="location_{{ $city->id }}" class="font-medium text-gray-900">{{ $city->name }}</label>
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <div class="flex items-center gap-4 mt-6">
                                <x-primary-button>{{ __('Save Locations') }}</x-primary-button>
                            </div>
                        </form>
                    </section>
                </div>
            </div>

            <div class="p-4 sm:p-8 bg-white shadow sm:rounded-lg">
                <div class="max-w-xl">
                    <div class="flex justify-between items-center">
                        <a href="{{ route('profile.escort.edit') }}" class="inline-flex items-center text-lg font-medium text-pink-600 hover:text-pink-800 transition-colors duration-200">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                            </svg>
                            {{ __('Back to Profile') }}
                        </a>
                        <a href="{{ route('profile.escort.edit') }}" class="inline-flex items-center px-4 py-2 bg-pink border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-pink-600 focus:bg-pink-600 active:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            {{ __('Profile Settings') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
