<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Edit Escort Profile') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <div class="p-4 sm:p-8 bg-white shadow sm:rounded-lg">
                <div class="max-w-xl">
                    @if(session('status') === 'profile-updated')
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                            <span class="block sm:inline">Profile updated successfully.</span>
                        </div>
                    @endif

                    <section>
                        <header>
                            <h2 class="text-lg font-medium text-gray-900">
                                {{ __('Profile Information') }}
                            </h2>

                            <p class="mt-1 text-sm text-gray-600">
                                {{ __('Update your escort profile information.') }}
                            </p>
                        </header>

                        <form method="post" action="{{ route('profile.escort.update') }}" class="mt-6 space-y-6">
                            @csrf
                            @method('patch')

                            <div>
                                <x-input-label for="name" :value="__('Display Name')" />
                                <x-text-input id="name" name="name" type="text" class="mt-1 block w-full" :value="old('name', $escort->name)" required autofocus />
                                <x-input-error class="mt-2" :messages="$errors->get('name')" />
                            </div>

                            <div>
                                <x-input-label for="gender" :value="__('Gender')" />
                                <select id="gender" name="gender" class="mt-1 block w-full border-gray-300 focus:border-pink-500 focus:ring-pink-500 rounded-md shadow-sm">
                                    <option value="female" {{ old('gender', $escort->gender) === 'female' ? 'selected' : '' }}>Female</option>
                                    <option value="male" {{ old('gender', $escort->gender) === 'male' ? 'selected' : '' }}>Male</option>
                                    <option value="couple" {{ old('gender', $escort->gender) === 'couple' ? 'selected' : '' }}>Couple</option>
                                    <option value="gay" {{ old('gender', $escort->gender) === 'gay' ? 'selected' : '' }}>Gay</option>
                                    <option value="transsexual" {{ old('gender', $escort->gender) === 'transsexual' ? 'selected' : '' }}>Transsexual</option>
                                </select>
                                <x-input-error class="mt-2" :messages="$errors->get('gender')" />
                            </div>

                            <div>
                                <x-input-label for="date_of_birth" :value="__('Date of Birth')" />
                                <x-text-input id="date_of_birth" name="date_of_birth" type="date" class="mt-1 block w-full" :value="old('date_of_birth', $escort->date_of_birth->format('Y-m-d'))" required />
                                <x-input-error class="mt-2" :messages="$errors->get('date_of_birth')" />
                            </div>

                            <div>
                                <x-input-label for="ethnicity" :value="__('Ethnicity')" />
                                <select id="ethnicity" name="ethnicity" class="mt-1 block w-full border-gray-300 focus:border-pink-500 focus:ring-pink-500 rounded-md shadow-sm">
                                    <option value="Latin" {{ old('ethnicity', $escort->ethnicity) === 'Latin' ? 'selected' : '' }}>Latin</option>
                                    <option value="Caucasian" {{ old('ethnicity', $escort->ethnicity) === 'Caucasian' ? 'selected' : '' }}>Caucasian</option>
                                    <option value="Black" {{ old('ethnicity', $escort->ethnicity) === 'Black' ? 'selected' : '' }}>Black</option>
                                    <option value="White" {{ old('ethnicity', $escort->ethnicity) === 'White' ? 'selected' : '' }}>White</option>
                                    <option value="MiddleEast" {{ old('ethnicity', $escort->ethnicity) === 'MiddleEast' ? 'selected' : '' }}>Middle Eastern</option>
                                    <option value="Asian" {{ old('ethnicity', $escort->ethnicity) === 'Asian' ? 'selected' : '' }}>Asian</option>
                                    <option value="Indian" {{ old('ethnicity', $escort->ethnicity) === 'Indian' ? 'selected' : '' }}>Indian</option>
                                    <option value="Aborigine" {{ old('ethnicity', $escort->ethnicity) === 'Aborigine' ? 'selected' : '' }}>Aborigine</option>
                                    <option value="Native American" {{ old('ethnicity', $escort->ethnicity) === 'Native American' ? 'selected' : '' }}>Native American</option>
                                    <option value="Other" {{ old('ethnicity', $escort->ethnicity) === 'Other' ? 'selected' : '' }}>Other</option>
                                </select>
                                <x-input-error class="mt-2" :messages="$errors->get('ethnicity')" />
                            </div>

                            <div>
                                <x-input-label for="hair_color" :value="__('Hair Color')" />
                                <select id="hair_color" name="hair_color" class="mt-1 block w-full border-gray-300 focus:border-pink-500 focus:ring-pink-500 rounded-md shadow-sm">
                                    <option value="Black" {{ old('hair_color', $escort->hair_color) === 'Black' ? 'selected' : '' }}>Black</option>
                                    <option value="Blonde" {{ old('hair_color', $escort->hair_color) === 'Blonde' ? 'selected' : '' }}>Blonde</option>
                                    <option value="Brown" {{ old('hair_color', $escort->hair_color) === 'Brown' ? 'selected' : '' }}>Brown</option>
                                    <option value="Brunette" {{ old('hair_color', $escort->hair_color) === 'Brunette' ? 'selected' : '' }}>Brunette</option>
                                    <option value="Chestnut" {{ old('hair_color', $escort->hair_color) === 'Chestnut' ? 'selected' : '' }}>Chestnut</option>
                                    <option value="Auburn" {{ old('hair_color', $escort->hair_color) === 'Auburn' ? 'selected' : '' }}>Auburn</option>
                                    <option value="Dark-blonde" {{ old('hair_color', $escort->hair_color) === 'Dark-blonde' ? 'selected' : '' }}>Dark Blonde</option>
                                    <option value="Golden" {{ old('hair_color', $escort->hair_color) === 'Golden' ? 'selected' : '' }}>Golden</option>
                                    <option value="Red" {{ old('hair_color', $escort->hair_color) === 'Red' ? 'selected' : '' }}>Red</option>
                                    <option value="Grey" {{ old('hair_color', $escort->hair_color) === 'Grey' ? 'selected' : '' }}>Grey</option>
                                    <option value="Silver" {{ old('hair_color', $escort->hair_color) === 'Silver' ? 'selected' : '' }}>Silver</option>
                                    <option value="White" {{ old('hair_color', $escort->hair_color) === 'White' ? 'selected' : '' }}>White</option>
                                    <option value="Other" {{ old('hair_color', $escort->hair_color) === 'Other' ? 'selected' : '' }}>Other</option>
                                </select>
                                <x-input-error class="mt-2" :messages="$errors->get('hair_color')" />
                            </div>

                            <div>
                                <x-input-label for="hair_length" :value="__('Hair Length')" />
                                <select id="hair_length" name="hair_length" class="mt-1 block w-full border-gray-300 focus:border-pink-500 focus:ring-pink-500 rounded-md shadow-sm">
                                    <option value="Bald" {{ old('hair_length', $escort->hair_length) === 'Bald' ? 'selected' : '' }}>Bald</option>
                                    <option value="Short" {{ old('hair_length', $escort->hair_length) === 'Short' ? 'selected' : '' }}>Short</option>
                                    <option value="Shoulder" {{ old('hair_length', $escort->hair_length) === 'Shoulder' ? 'selected' : '' }}>Shoulder</option>
                                    <option value="Long" {{ old('hair_length', $escort->hair_length) === 'Long' ? 'selected' : '' }}>Long</option>
                                    <option value="Very Long" {{ old('hair_length', $escort->hair_length) === 'Very Long' ? 'selected' : '' }}>Very Long</option>
                                </select>
                                <x-input-error class="mt-2" :messages="$errors->get('hair_length')" />
                            </div>

                            <div>
                                <x-input-label for="bust_size" :value="__('Bust Size')" />
                                <select id="bust_size" name="bust_size" class="mt-1 block w-full border-gray-300 focus:border-pink-500 focus:ring-pink-500 rounded-md shadow-sm">
                                    <option value="">Not Applicable</option>
                                    <option value="Very small" {{ old('bust_size', $escort->bust_size) === 'Very small' ? 'selected' : '' }}>Very small</option>
                                    <option value="Small(A)" {{ old('bust_size', $escort->bust_size) === 'Small(A)' ? 'selected' : '' }}>Small (A)</option>
                                    <option value="Medium(B)" {{ old('bust_size', $escort->bust_size) === 'Medium(B)' ? 'selected' : '' }}>Medium (B)</option>
                                    <option value="Large(C)" {{ old('bust_size', $escort->bust_size) === 'Large(C)' ? 'selected' : '' }}>Large (C)</option>
                                    <option value="Very Large(D)" {{ old('bust_size', $escort->bust_size) === 'Very Large(D)' ? 'selected' : '' }}>Very Large (D)</option>
                                    <option value="Enormous(E+)" {{ old('bust_size', $escort->bust_size) === 'Enormous(E+)' ? 'selected' : '' }}>Enormous (E+)</option>
                                </select>
                                <x-input-error class="mt-2" :messages="$errors->get('bust_size')" />
                            </div>

                            <div>
                                <x-input-label for="height_cm" :value="__('Height (cm)')" />
                                <x-text-input id="height_cm" name="height_cm" type="number" class="mt-1 block w-full" :value="old('height_cm', $escort->height_cm)" required min="140" max="220" />
                                <x-input-error class="mt-2" :messages="$errors->get('height_cm')" />
                            </div>

                            <div>
                                <x-input-label for="weight_kg" :value="__('Weight (kg)')" />
                                <x-text-input id="weight_kg" name="weight_kg" type="number" class="mt-1 block w-full" :value="old('weight_kg', $escort->weight_kg)" required min="40" max="150" />
                                <x-input-error class="mt-2" :messages="$errors->get('weight_kg')" />
                            </div>

                            <div>
                                <x-input-label for="build" :value="__('Build')" />
                                <select id="build" name="build" class="mt-1 block w-full border-gray-300 focus:border-pink-500 focus:ring-pink-500 rounded-md shadow-sm">
                                    <option value="Skinny" {{ old('build', $escort->build) === 'Skinny' ? 'selected' : '' }}>Skinny</option>
                                    <option value="Slim" {{ old('build', $escort->build) === 'Slim' ? 'selected' : '' }}>Slim</option>
                                    <option value="Regular" {{ old('build', $escort->build) === 'Regular' ? 'selected' : '' }}>Regular</option>
                                    <option value="Curvy" {{ old('build', $escort->build) === 'Curvy' ? 'selected' : '' }}>Curvy</option>
                                    <option value="Fat" {{ old('build', $escort->build) === 'Fat' ? 'selected' : '' }}>Fat</option>
                                </select>
                                <x-input-error class="mt-2" :messages="$errors->get('build')" />
                            </div>

                            <div>
                                <x-input-label for="looks" :value="__('Looks')" />
                                <select id="looks" name="looks" class="mt-1 block w-full border-gray-300 focus:border-pink-500 focus:ring-pink-500 rounded-md shadow-sm">
                                    <option value="Nothing Special" {{ old('looks', $escort->looks) === 'Nothing Special' ? 'selected' : '' }}>Nothing Special</option>
                                    <option value="Average" {{ old('looks', $escort->looks) === 'Average' ? 'selected' : '' }}>Average</option>
                                    <option value="Sexy" {{ old('looks', $escort->looks) === 'Sexy' ? 'selected' : '' }}>Sexy</option>
                                    <option value="Ultra Sexy" {{ old('looks', $escort->looks) === 'Ultra Sexy' ? 'selected' : '' }}>Ultra Sexy</option>
                                </select>
                                <x-input-error class="mt-2" :messages="$errors->get('looks')" />
                            </div>

                            <div>
                                <label for="smoker" class="inline-flex items-center">
                                    <input id="smoker" type="checkbox" class="rounded border-gray-300 text-pink-600 shadow-sm focus:ring-pink-500" name="smoker" value="1" {{ old('smoker', $escort->smoker) ? 'checked' : '' }}>
                                    <span class="ml-2 text-sm text-gray-600">{{ __('Smoker') }}</span>
                                </label>
                                <x-input-error class="mt-2" :messages="$errors->get('smoker')" />
                            </div>

                            <div>
                                <x-input-label for="about" :value="__('About Me')" />
                                <textarea id="about" name="about" class="mt-1 block w-full border-gray-300 focus:border-pink-500 focus:ring-pink-500 rounded-md shadow-sm" rows="5" required>{{ old('about', $escort->about) }}</textarea>
                                <x-input-error class="mt-2" :messages="$errors->get('about')" />
                            </div>

                            <div>
                                <x-input-label for="education" :value="__('Education')" />
                                <x-text-input id="education" name="education" type="text" class="mt-1 block w-full" :value="old('education', $escort->education)" />
                                <x-input-error class="mt-2" :messages="$errors->get('education')" />
                            </div>

                            <div>
                                <x-input-label for="sports" :value="__('Sports')" />
                                <x-text-input id="sports" name="sports" type="text" class="mt-1 block w-full" :value="old('sports', is_array($escort->sports) ? implode(', ', $escort->sports) : $escort->sports)" />
                                <x-input-error class="mt-2" :messages="$errors->get('sports')" />
                            </div>

                            <div>
                                <x-input-label for="hobbies" :value="__('Hobbies')" />
                                <x-text-input id="hobbies" name="hobbies" type="text" class="mt-1 block w-full" :value="old('hobbies', is_array($escort->hobbies) ? implode(', ', $escort->hobbies) : $escort->hobbies)" />
                                <x-input-error class="mt-2" :messages="$errors->get('hobbies')" />
                            </div>

                            <div>
                                <x-input-label for="zodiac_sign" :value="__('Zodiac Sign')" />
                                <x-text-input id="zodiac_sign" name="zodiac_sign" type="text" class="mt-1 block w-full" :value="old('zodiac_sign', $escort->zodiac_sign)" />
                                <x-input-error class="mt-2" :messages="$errors->get('zodiac_sign')" />
                            </div>

                            <div>
                                <x-input-label for="sexual_orientation" :value="__('Sexual Orientation')" />
                                <x-text-input id="sexual_orientation" name="sexual_orientation" type="text" class="mt-1 block w-full" :value="old('sexual_orientation', $escort->sexual_orientation)" />
                                <x-input-error class="mt-2" :messages="$errors->get('sexual_orientation')" />
                            </div>

                            <div>
                                <x-input-label for="occupation" :value="__('Occupation')" />
                                <x-text-input id="occupation" name="occupation" type="text" class="mt-1 block w-full" :value="old('occupation', $escort->occupation)" />
                                <x-input-error class="mt-2" :messages="$errors->get('occupation')" />
                            </div>

                            <div id="contact-info" class="p-4 bg-pink-50 rounded-lg border border-pink-200 mb-4">
                                <h3 class="text-lg font-medium text-pink-800 mb-3">Contact Information</h3>
                                <p class="text-sm text-pink-600 mb-4">This information will be displayed on your profile for clients to contact you.</p>

                                <div class="space-y-4">
                                    <div>
                                        <x-input-label for="phone_number" :value="__('Phone Number')" />
                                        <x-text-input id="phone_number" name="phone_number" type="text" class="mt-1 block w-full" :value="old('phone_number', $escort->phone_number)" placeholder="+256 XXX XXX XXX" />
                                        <x-input-error class="mt-2" :messages="$errors->get('phone_number')" />

                                        <div class="mt-2">
                                            <label for="show_phone_number" class="inline-flex items-center">
                                                <input id="show_phone_number" type="checkbox" class="rounded border-gray-300 text-pink-600 shadow-sm focus:ring-pink-500" name="show_phone_number" value="1" {{ old('show_phone_number', $escort->show_phone_number) ? 'checked' : '' }}>
                                                <span class="ml-2 text-sm text-gray-600">{{ __('Show phone number on my profile') }}</span>
                                            </label>
                                        </div>
                                    </div>

                                    <div>
                                        <x-input-label for="whatsapp_number" :value="__('WhatsApp Number')" />
                                        <x-text-input id="whatsapp_number" name="whatsapp_number" type="text" class="mt-1 block w-full" :value="old('whatsapp_number', $escort->whatsapp_number)" placeholder="+256 XXX XXX XXX" />
                                        <x-input-error class="mt-2" :messages="$errors->get('whatsapp_number')" />

                                        <div class="mt-2">
                                            <label for="show_whatsapp" class="inline-flex items-center">
                                                <input id="show_whatsapp" type="checkbox" class="rounded border-gray-300 text-pink-600 shadow-sm focus:ring-pink-500" name="show_whatsapp" value="1" {{ old('show_whatsapp', $escort->show_whatsapp) ? 'checked' : '' }}>
                                                <span class="ml-2 text-sm text-gray-600">{{ __('Show WhatsApp on my profile') }}</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <x-input-label :value="__('Availability')" />
                                <div class="mt-2 space-y-2">
                                    <label for="incall_available" class="inline-flex items-center">
                                        <input id="incall_available" type="checkbox" class="rounded border-gray-300 text-pink-600 shadow-sm focus:ring-pink-500" name="incall_available" value="1" {{ old('incall_available', $escort->incall_available) ? 'checked' : '' }}>
                                        <span class="ml-2 text-sm text-gray-600">{{ __('Incall Available') }}</span>
                                    </label>
                                    <x-input-error class="mt-1" :messages="$errors->get('incall_available')" />

                                    <label for="outcall_available" class="inline-flex items-center">
                                        <input id="outcall_available" type="checkbox" class="rounded border-gray-300 text-pink-600 shadow-sm focus:ring-pink-500" name="outcall_available" value="1" {{ old('outcall_available', $escort->outcall_available) ? 'checked' : '' }}>
                                        <span class="ml-2 text-sm text-gray-600">{{ __('Outcall Available') }}</span>
                                    </label>
                                    <x-input-error class="mt-1" :messages="$errors->get('outcall_available')" />
                                </div>
                            </div>

                            <div class="flex items-center gap-4">
                                <x-primary-button>{{ __('Save') }}</x-primary-button>
                            </div>
                        </form>
                    </section>
                </div>
            </div>

            <div class="p-4 sm:p-8 bg-white shadow sm:rounded-lg">
                <div class="max-w-xl">
                    <div class="flex justify-between items-center">
                        <h2 class="text-lg font-medium text-gray-900">
                            {{ __('Other Profile Settings') }}
                        </h2>
                    </div>

                    <div class="mt-4 space-y-3">
                        <a href="{{ route('escort.photos') }}" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            {{ __('Manage Photos') }}
                        </a>

                        <a href="{{ route('escort.services') }}" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            {{ __('Manage Services') }}
                        </a>

                        <a href="{{ route('escort.rates') }}" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            {{ __('Manage Rates') }}
                        </a>

                        <a href="{{ route('escort.locations') }}" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            {{ __('Manage Locations') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
