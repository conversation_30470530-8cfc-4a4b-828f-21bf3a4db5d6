<div class="bg-gray-800 border border-gray-700 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
    <a href="{{ route('escorts.show', $escort->slug) }}" class="block relative">
        @if($escort->primaryImage)
            @php
                $primaryImagePath = $escort->primaryImage->image_path ?? $escort->primaryImage->path;
            @endphp
            <img src="{{ asset('storage/' . $primaryImagePath) }}" alt="{{ $escort->name }}" class="w-full h-48 sm:h-56 md:h-64 object-cover">
        @else
            <div class="w-full h-48 sm:h-56 md:h-64 bg-gray-700 flex items-center justify-center">
                <span class="text-gray-400">No image available</span>
            </div>
        @endif

        <!-- Badges -->
        <div class="absolute top-2 left-2 flex flex-col gap-1.5">
            @if($escort->is_verified)
                <span class="bg-green-500 text-white text-xs px-2.5 py-1 rounded-full font-medium shadow-md border border-green-600">Verified</span>
            @endif
            @if($escort->is_new)
                <span class="bg-blue-500 text-white text-xs px-2.5 py-1 rounded-full font-medium shadow-md border border-blue-600">New</span>
            @endif
        </div>

        <!-- Watermark -->
        <div class="watermark">GHB</div>
    </a>

    <div class="p-3 sm:p-4 md:p-5">
        <div class="flex justify-between items-start mb-3">
            <h3 class="text-base sm:text-lg font-semibold text-white">
                <a href="{{ route('escorts.show', $escort->slug) }}" class="hover:text-pink-400 transition-colors duration-300 line-clamp-1">
                    {{ $escort->name }}
                </a>
            </h3>
            <span class="text-xs sm:text-sm text-white bg-gray-700 px-2 sm:px-2.5 py-0.5 rounded-full shadow-sm border border-gray-600 whitespace-nowrap ml-1">{{ $escort->age }} yrs</span>
        </div>

        <div class="flex items-center text-xs sm:text-sm text-gray-300 mb-3 sm:mb-4">
            @if($escort->locations->count() > 0)
                <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-1.5 flex-shrink-0 text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span class="truncate">{{ $escort->locations->first()->name }}</span>
            @endif
        </div>

        <!-- View Profile Button -->
        <div class="mt-3 sm:mt-4 pt-2 sm:pt-3 border-t border-gray-600 text-right">
            <a href="{{ route('escorts.show', $escort->slug) }}" class="bg-pink hover:bg-pink-600 text-white text-xs sm:text-sm font-medium px-3 sm:px-4 py-1.5 sm:py-2 rounded-md transition-colors duration-300 inline-flex items-center shadow-md">
                <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                View Profile
            </a>
        </div>
    </div>
</div>
