<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <!-- Additional Head Content (includes SEO titles) -->
        @stack('head')

        <!-- Default title if no SEO title is set -->
        @if(!View::hasSection('title'))
            <title>{{ config('app.name', 'Get Hot Babes') }}</title>
        @endif

        <!-- SEO Meta Tags -->
        @if(isset($seoMeta))
            <x-seo-meta :meta="$seoMeta" :structured-data="$structuredData ?? null" />
        @endif

        <!-- Favicon -->
        <link rel="icon" href="{{ asset('favicon.svg') }}" type="image/svg+xml">
        <link rel="icon" href="{{ asset('favicon.ico') }}" sizes="any">

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet">

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <!-- Analytics and SEO Tracking -->
        <x-analytics-seo page="general" />
    </head>
    <body class="font-sans antialiased">
        <div class="min-h-screen bg-gray-50">
            @include('layouts.public-navigation')

            <!-- Page Content -->
            <main>
                @yield('content')
            </main>

            <!-- Unified Footer -->
            <x-unified-footer />

            <!-- Global Confirmation Modal -->
            <x-confirmation-modal />
        </div>
        <!-- Age Disclaimer Modal -->
        <x-age-disclaimer />
    </body>
</html>
