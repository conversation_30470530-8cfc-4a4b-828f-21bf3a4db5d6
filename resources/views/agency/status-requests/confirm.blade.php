<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Request Confirmation') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h3 class="mt-4 text-xl font-medium text-gray-900">Request Submitted Successfully</h3>
                        <p class="mt-2 text-gray-500">Your {{ $statusRequest->request_type }} request has been submitted and is pending approval.</p>
                    </div>

                    <div class="mt-8 bg-gray-50 p-6 rounded-lg">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Request Details</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-gray-500">Request Type</p>
                                <p class="text-base font-medium text-gray-900">{{ ucfirst($statusRequest->request_type) }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">Duration</p>
                                <p class="text-base font-medium text-gray-900">{{ ucfirst($statusRequest->duration) }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">Price</p>
                                <p class="text-base font-medium text-gray-900">UGX {{ number_format($statusRequest->price, 0) }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">Status</p>
                                <p class="text-base font-medium text-yellow-600">Pending</p>
                            </div>
                        </div>
                    </div>

                    <div class="mt-8 bg-blue-50 p-6 rounded-lg">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Payment Instructions</h4>
                        <p class="text-gray-700 mb-4">
                            Please make a payment to complete your request. Contact the admin using the information below:
                        </p>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-gray-500">Phone Number</p>
                                <p class="text-base font-medium text-gray-900">{{ $adminPhone }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">Payment Amount</p>
                                <p class="text-base font-medium text-gray-900">UGX {{ number_format($statusRequest->price, 0) }}</p>
                            </div>
                        </div>
                        <p class="mt-4 text-sm text-gray-500">
                            After making the payment, please inform the admin about your payment details. Your request will be processed once the payment is confirmed.
                        </p>
                    </div>

                    <div class="mt-8 flex justify-center">
                        <a href="{{ route('agency.status-requests.index') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            View All Requests
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
