<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Request Status Change') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    @if($errors->any())
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                            <ul class="list-disc pl-5">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('agency.status-requests.store') }}">
                        @csrf

                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Select Request Type</h3>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="border rounded-lg p-4 @if(!$agency->is_approved) bg-green-50 border-green-200 @else bg-gray-50 @endif">
                                    <div class="flex items-center mb-2">
                                        <input type="radio" id="approval" name="request_type" value="approval" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                            @if(!$agency->is_approved) checked @else disabled @endif
                                            @if($pendingApproval) disabled @endif>
                                        <label for="approval" class="ml-2 block text-sm font-medium text-gray-700">
                                            Agency Approval
                                        </label>
                                    </div>
                                    <p class="text-sm text-gray-500 ml-6">
                                        Request to be listed on the agencies page. Your agency will be visible to all users after approval.
                                    </p>
                                    @if($pendingApproval)
                                        <p class="text-sm text-yellow-600 mt-2 ml-6">
                                            You already have a pending approval request.
                                        </p>
                                    @elseif($agency->is_approved)
                                        <p class="text-sm text-green-600 mt-2 ml-6">
                                            Your agency is already approved.
                                        </p>
                                    @endif
                                </div>

                                <div class="border rounded-lg p-4 @if($agency->is_approved && !$agency->is_featured) bg-purple-50 border-purple-200 @else bg-gray-50 @endif">
                                    <div class="flex items-center mb-2">
                                        <input type="radio" id="featured" name="request_type" value="featured" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                            @if(!$agency->is_approved) disabled @elseif($agency->is_featured) disabled @else checked @endif
                                            @if($pendingFeatured) disabled @endif>
                                        <label for="featured" class="ml-2 block text-sm font-medium text-gray-700">
                                            Featured Status
                                        </label>
                                    </div>
                                    <p class="text-sm text-gray-500 ml-6">
                                        Request to be featured on the agencies page. Featured agencies appear at the top of the list.
                                    </p>
                                    @if(!$agency->is_approved)
                                        <p class="text-sm text-yellow-600 mt-2 ml-6">
                                            Your agency needs to be approved first.
                                        </p>
                                    @elseif($pendingFeatured)
                                        <p class="text-sm text-yellow-600 mt-2 ml-6">
                                            You already have a pending featured request.
                                        </p>
                                    @elseif($agency->is_featured)
                                        <p class="text-sm text-green-600 mt-2 ml-6">
                                            Your agency is already featured.
                                        </p>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Select Duration</h3>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                @if(!$agency->is_approved && !$pendingApproval)
                                    <!-- Agency Approval Pricing -->
                                    @forelse($approvalPricing as $pricing)
                                        <div class="border rounded-lg p-4 bg-gray-50 hover:bg-gray-100 transition-colors">
                                            <div class="flex items-center mb-2">
                                                <input type="radio" id="duration_{{ $pricing->duration }}" name="duration" value="{{ $pricing->duration }}" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" {{ $loop->first ? 'checked' : '' }}>
                                                <label for="duration_{{ $pricing->duration }}" class="ml-2 block text-sm font-medium text-gray-700">
                                                    {{ $pricing->formatted_duration }}
                                                </label>
                                            </div>
                                            <p class="text-sm text-gray-500 ml-6">
                                                {{ $pricing->formatted_price }}
                                            </p>
                                        </div>
                                    @empty
                                        <!-- Default pricing if no pricing is set -->
                                        <div class="border rounded-lg p-4 bg-gray-50 hover:bg-gray-100 transition-colors">
                                            <div class="flex items-center mb-2">
                                                <input type="radio" id="week" name="duration" value="week" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                                <label for="week" class="ml-2 block text-sm font-medium text-gray-700">
                                                    1 Week
                                                </label>
                                            </div>
                                            <p class="text-sm text-gray-500 ml-6">
                                                UGX 20,000
                                            </p>
                                        </div>
                                        <div class="border rounded-lg p-4 bg-gray-50 hover:bg-gray-100 transition-colors">
                                            <div class="flex items-center mb-2">
                                                <input type="radio" id="month" name="duration" value="month" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                                <label for="month" class="ml-2 block text-sm font-medium text-gray-700">
                                                    1 Month
                                                </label>
                                            </div>
                                            <p class="text-sm text-gray-500 ml-6">
                                                UGX 50,000
                                            </p>
                                        </div>
                                        <div class="border rounded-lg p-4 bg-gray-50 hover:bg-gray-100 transition-colors">
                                            <div class="flex items-center mb-2">
                                                <input type="radio" id="annual" name="duration" value="annual" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                                <label for="annual" class="ml-2 block text-sm font-medium text-gray-700">
                                                    1 Year
                                                </label>
                                            </div>
                                            <p class="text-sm text-gray-500 ml-6">
                                                UGX 500,000
                                            </p>
                                        </div>
                                    @endforelse
                                @elseif($agency->is_approved && !$agency->is_featured && !$pendingFeatured)
                                    <!-- Agency Featured Pricing -->
                                    @forelse($featuredPricing as $pricing)
                                        <div class="border rounded-lg p-4 bg-gray-50 hover:bg-gray-100 transition-colors">
                                            <div class="flex items-center mb-2">
                                                <input type="radio" id="duration_{{ $pricing->duration }}" name="duration" value="{{ $pricing->duration }}" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" {{ $loop->first ? 'checked' : '' }}>
                                                <label for="duration_{{ $pricing->duration }}" class="ml-2 block text-sm font-medium text-gray-700">
                                                    {{ $pricing->formatted_duration }}
                                                </label>
                                            </div>
                                            <p class="text-sm text-gray-500 ml-6">
                                                {{ $pricing->formatted_price }}
                                            </p>
                                        </div>
                                    @empty
                                        <!-- Default pricing if no pricing is set -->
                                        <div class="border rounded-lg p-4 bg-gray-50 hover:bg-gray-100 transition-colors">
                                            <div class="flex items-center mb-2">
                                                <input type="radio" id="week" name="duration" value="week" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                                <label for="week" class="ml-2 block text-sm font-medium text-gray-700">
                                                    1 Week
                                                </label>
                                            </div>
                                            <p class="text-sm text-gray-500 ml-6">
                                                UGX 20,000
                                            </p>
                                        </div>
                                        <div class="border rounded-lg p-4 bg-gray-50 hover:bg-gray-100 transition-colors">
                                            <div class="flex items-center mb-2">
                                                <input type="radio" id="month" name="duration" value="month" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                                <label for="month" class="ml-2 block text-sm font-medium text-gray-700">
                                                    1 Month
                                                </label>
                                            </div>
                                            <p class="text-sm text-gray-500 ml-6">
                                                UGX 50,000
                                            </p>
                                        </div>
                                        <div class="border rounded-lg p-4 bg-gray-50 hover:bg-gray-100 transition-colors">
                                            <div class="flex items-center mb-2">
                                                <input type="radio" id="annual" name="duration" value="annual" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                                <label for="annual" class="ml-2 block text-sm font-medium text-gray-700">
                                                    1 Year
                                                </label>
                                            </div>
                                            <p class="text-sm text-gray-500 ml-6">
                                                UGX 500,000
                                            </p>
                                        </div>
                                    @endforelse
                                @else
                                    <!-- Default pricing options when no request can be made -->
                                    <div class="border rounded-lg p-4 bg-gray-50">
                                        <div class="flex items-center mb-2">
                                            <input type="radio" id="week" name="duration" value="week" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked disabled>
                                            <label for="week" class="ml-2 block text-sm font-medium text-gray-500">
                                                1 Week
                                            </label>
                                        </div>
                                        <p class="text-sm text-gray-400 ml-6">
                                            UGX 20,000
                                        </p>
                                    </div>
                                    <div class="border rounded-lg p-4 bg-gray-50">
                                        <div class="flex items-center mb-2">
                                            <input type="radio" id="month" name="duration" value="month" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" disabled>
                                            <label for="month" class="ml-2 block text-sm font-medium text-gray-500">
                                                1 Month
                                            </label>
                                        </div>
                                        <p class="text-sm text-gray-400 ml-6">
                                            UGX 50,000
                                        </p>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <div class="flex justify-end">
                            <a href="{{ route('agency.status-requests.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150 mr-2">
                                Cancel
                            </a>
                            <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                Submit Request
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
