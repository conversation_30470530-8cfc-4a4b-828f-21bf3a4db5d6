<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Manage Locations') }}
            </h2>
            <a href="{{ route('dashboard') }}" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                {{ __('Dashboard') }}
            </a>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <!-- Locations Banner -->
            <div class="bg-gradient-to-r from-indigo-500 to-blue-600 rounded-lg shadow-lg overflow-hidden mb-6">
                <div class="px-6 py-8 md:px-8 md:flex md:items-center md:justify-between">
                    <div>
                        <h2 class="text-2xl font-bold text-white">{{ __('Agency Locations') }}</h2>
                        <p class="mt-2 text-indigo-100">{{ __('Set the areas where your agency operates') }}</p>
                    </div>
                    <div class="mt-4 md:mt-0">
                        <div class="bg-white bg-opacity-20 px-4 py-2 rounded-lg text-white">
                            <span class="font-bold text-2xl">{{ count($selectedLocations) }}</span>
                            <span class="ml-1">{{ __('Locations') }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-4 sm:p-8 bg-white shadow sm:rounded-lg">
                <div>
                    @if(session('success'))
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                            <span class="block sm:inline">{{ session('success') }}</span>
                        </div>
                    @endif

                    <section>
                        <header class="flex items-center justify-between">
                            <div>
                                <h2 class="text-lg font-medium text-gray-900">
                                    {{ __('Select Your Agency Locations') }}
                                </h2>

                                <p class="mt-1 text-sm text-gray-600">
                                    {{ __('Choose the locations where your agency operates.') }}
                                </p>
                            </div>
                            <div class="flex-shrink-0 bg-indigo-100 rounded-md p-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            </div>
                        </header>

                        <form method="post" action="{{ route('agency.locations.update') }}" class="mt-6">
                            @csrf

                            <div class="space-y-6">
                                @foreach($countries as $country)
                                    <div>
                                        <h3 class="text-md font-medium text-gray-900 mb-2">{{ $country->name }}</h3>

                                        <div class="ml-4">
                                            <div class="flex items-start mb-2">
                                                <div class="flex items-center h-5">
                                                    <input id="location_{{ $country->id }}" name="locations[]" type="checkbox" value="{{ $country->id }}" class="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-pink-300" {{ in_array($country->id, $selectedLocations) ? 'checked' : '' }}>
                                                </div>
                                                <div class="ml-3 text-sm">
                                                    <label for="location_{{ $country->id }}" class="font-medium text-gray-900">Entire {{ $country->name }}</label>
                                                </div>
                                            </div>

                                            @if($country->children->count() > 0)
                                                <div class="ml-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                                                    @foreach($country->children as $city)
                                                        <div class="flex items-start">
                                                            <div class="flex items-center h-5">
                                                                <input id="location_{{ $city->id }}" name="locations[]" type="checkbox" value="{{ $city->id }}" class="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-pink-300" {{ in_array($city->id, $selectedLocations) ? 'checked' : '' }}>
                                                            </div>
                                                            <div class="ml-3 text-sm">
                                                                <label for="location_{{ $city->id }}" class="font-medium text-gray-900">{{ $city->name }}</label>
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <div class="flex items-center gap-4 mt-6">
                                <button type="submit" class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    {{ __('Save Locations') }}
                                </button>
                            </div>
                        </form>
                    </section>
                </div>
            </div>

            <div class="flex justify-end">
                <a href="{{ route('profile.agency.edit') }}" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    {{ __('Agency Profile') }}
                </a>
            </div>
        </div>
    </div>
</x-app-layout>
