<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Escort Dashboard') }}
            </h2>
            <a href="{{ route('profile.escort.edit') }}" class="inline-flex items-center px-4 py-2 bg-pink-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-pink-700 focus:bg-pink-700 active:bg-pink-900 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
                {{ __('Edit Profile') }}
            </a>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Welcome Banner -->
            <div class="bg-gradient-to-r from-pink-500 to-purple-600 rounded-lg shadow-lg overflow-hidden mb-6">
                <div class="px-4 py-6 sm:px-6 md:px-8 md:py-8 md:flex md:items-center md:justify-between">
                    <div class="w-full">
                        <h2 class="text-xl sm:text-2xl font-bold text-white">Welcome back, {{ Auth::user()->username }}!</h2>
                        <p class="mt-2 text-pink-100 text-sm sm:text-base">Your profile is {{ $metrics['profile_completion'] }}% complete</p>
                        <div class="w-full max-w-xs bg-white bg-opacity-20 rounded-full h-2.5 mt-2">
                            <div class="bg-white h-2.5 rounded-full" style="width: {{ $metrics['profile_completion'] }}%"></div>
                        </div>
                    </div>
                    <div class="mt-4 md:mt-0 flex-shrink-0">
                        <a href="{{ route('escort.photos') }}" class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 bg-white bg-opacity-20 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-opacity-30 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-pink-500 transition ease-in-out duration-150">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            {{ __('Manage Photos') }}
                        </a>
                        <a href="{{ route('escort.videos') }}" class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 bg-white bg-opacity-20 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-opacity-30 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-pink-500 transition ease-in-out duration-150">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                            {{ __('Manage Videos') }}
                        </a>
                    </div>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6">
                <div class="bg-white rounded-lg shadow-md overflow-hidden border-t-4 border-pink-500">
                    <div class="p-4 sm:p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 bg-pink-100 rounded-md p-2 sm:p-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6 text-pink-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                            </div>
                            <div class="ml-3 sm:ml-4">
                                <h3 class="text-base sm:text-lg font-medium text-gray-900">Profile Views</h3>
                                <p class="text-2xl sm:text-3xl font-bold text-gray-700">{{ $escort->profile_views ?? 0 }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md overflow-hidden border-t-4 border-yellow-500">
                    <div class="p-4 sm:p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 bg-yellow-100 rounded-md p-2 sm:p-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="ml-3 sm:ml-4">
                                <h3 class="text-base sm:text-lg font-medium text-gray-900">Member Since</h3>
                                <p class="text-lg sm:text-xl font-bold text-gray-700">{{ Auth::user()->created_at->format('M Y') }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md overflow-hidden border-t-4 border-green-500 sm:col-span-2 lg:col-span-1">
                    <div class="p-4 sm:p-5">
                        <div class="flex flex-wrap sm:flex-nowrap items-start sm:items-center justify-between mb-3 sm:mb-4">
                            <div class="flex items-center w-full sm:w-auto mb-2 sm:mb-0">
                                <div class="flex-shrink-0 bg-green-100 rounded-md p-2 sm:p-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                    </svg>
                                </div>
                                <div class="ml-3 sm:ml-4">
                                    <h3 class="text-base sm:text-lg font-medium text-gray-900">Contact Information</h3>
                                    <p class="text-xs sm:text-sm text-gray-500">Your contact details for clients</p>
                                </div>
                            </div>
                            <a href="{{ route('profile.escort.edit') }}#contact-info" class="text-xs sm:text-sm text-green-600 hover:text-green-800 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                </svg>
                                Edit
                            </a>
                        </div>

                        <div class="space-y-2 sm:space-y-3">
                            @if($escort->phone_number)
                                <div class="flex flex-wrap items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 text-gray-400 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                    </svg>
                                    <span class="text-gray-700 text-sm sm:text-base mr-2">{{ $escort->phone_number }}</span>
                                    <span class="mt-1 sm:mt-0 px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $escort->show_phone_number ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                        {{ $escort->show_phone_number ? 'Visible' : 'Hidden' }}
                                    </span>
                                </div>
                            @else
                                <div class="flex items-center text-yellow-600">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                    </svg>
                                    <span class="text-sm">No phone number added</span>
                                </div>
                            @endif

                            @if($escort->whatsapp_number)
                                <div class="flex flex-wrap items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 text-green-500 mr-2 flex-shrink-0" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"/>
                                    </svg>
                                    <span class="text-gray-700 text-sm sm:text-base mr-2">{{ $escort->whatsapp_number }}</span>
                                    <span class="mt-1 sm:mt-0 px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $escort->show_whatsapp ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                        {{ $escort->show_whatsapp ? 'Visible' : 'Hidden' }}
                                    </span>
                                </div>
                            @else
                                <div class="flex items-center text-yellow-600">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                    </svg>
                                    <span class="text-sm">No WhatsApp number added</span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
                <!-- Left Column -->
                <div class="lg:col-span-2 space-y-4 sm:space-y-6">
                    <!-- Profile Stats -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden">
                        <div class="border-b border-gray-200">
                            <div class="p-4 sm:p-5">
                                <h3 class="text-base sm:text-lg font-medium text-gray-900">Profile Statistics</h3>
                                <p class="text-xs sm:text-sm text-gray-500">Your profile performance</p>
                            </div>
                        </div>
                        <div class="p-4 sm:p-5">
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                                <div class="bg-gray-50 p-3 sm:p-4 rounded-lg">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 bg-pink-100 rounded-md p-1.5 sm:p-2 mr-2 sm:mr-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 text-pink-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-xs sm:text-sm text-gray-500">Weekly Views</p>
                                            <p class="text-base sm:text-lg font-semibold text-gray-800">{{ rand(50, 200) }}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-gray-50 p-3 sm:p-4 rounded-lg">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 bg-indigo-100 rounded-md p-1.5 sm:p-2 mr-2 sm:mr-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-xs sm:text-sm text-gray-500">Last Updated</p>
                                            <p class="text-base sm:text-lg font-semibold text-gray-800">{{ $escort->updated_at->diffForHumans() }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden">
                        <div class="border-b border-gray-200">
                            <div class="p-4 sm:p-5">
                                <h3 class="text-base sm:text-lg font-medium text-gray-900">Quick Actions</h3>
                            </div>
                        </div>
                        <div class="p-4 sm:p-5">
                            <div class="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 gap-3 sm:gap-4">
                                <a href="{{ route('escort.photos') }}" class="flex items-center p-3 sm:p-4 bg-pink-50 rounded-lg hover:bg-pink-100 transition-colors">
                                    <div class="flex-shrink-0 bg-pink-100 rounded-md p-2 sm:p-3">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6 text-pink-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </svg>
                                    </div>
                                    <div class="ml-3 sm:ml-4">
                                        <h4 class="text-sm font-medium text-gray-900">Manage Photos</h4>
                                        <p class="text-xs text-gray-500">Upload and organize your photos</p>
                                    </div>
                                </a>
                                <a href="{{ route('escort.videos') }}" class="flex items-center p-3 sm:p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                                    <div class="flex-shrink-0 bg-purple-100 rounded-md p-2 sm:p-3">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                        </svg>
                                    </div>
                                    <div class="ml-3 sm:ml-4">
                                        <h4 class="text-sm font-medium text-gray-900">Manage Videos</h4>
                                        <p class="text-xs text-gray-500">Upload and organize your videos</p>
                                    </div>
                                </a>
                                <a href="{{ route('escort.services') }}" class="flex items-center p-3 sm:p-4 bg-indigo-50 rounded-lg hover:bg-indigo-100 transition-colors">
                                    <div class="flex-shrink-0 bg-indigo-100 rounded-md p-2 sm:p-3">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                        </svg>
                                    </div>
                                    <div class="ml-3 sm:ml-4">
                                        <h4 class="text-sm font-medium text-gray-900">Update Services</h4>
                                        <p class="text-xs text-gray-500">Manage your service offerings</p>
                                    </div>
                                </a>
                                <a href="{{ route('escort.rates') }}" class="flex items-center p-3 sm:p-4 bg-indigo-50 rounded-lg hover:bg-indigo-100 transition-colors">
                                    <div class="flex-shrink-0 bg-indigo-100 rounded-md p-2 sm:p-3">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <div class="ml-3 sm:ml-4">
                                        <h4 class="text-sm font-medium text-gray-900">Set Rates</h4>
                                        <p class="text-xs text-gray-500">Update your pricing information</p>
                                    </div>
                                </a>
                                <a href="{{ route('escort.locations') }}" class="flex items-center p-3 sm:p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                                    <div class="flex-shrink-0 bg-green-100 rounded-md p-2 sm:p-3">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                        </svg>
                                    </div>
                                    <div class="ml-3 sm:ml-4">
                                        <h4 class="text-sm font-medium text-gray-900">Manage Locations</h4>
                                        <p class="text-xs text-gray-500">Set your working locations</p>
                                    </div>
                                </a>

                                <a href="{{ route('profile.escort.edit') }}#contact-info" class="flex items-center p-3 sm:p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                                    <div class="flex-shrink-0 bg-blue-100 rounded-md p-2 sm:p-3">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                        </svg>
                                    </div>
                                    <div class="ml-3 sm:ml-4">
                                        <h4 class="text-sm font-medium text-gray-900">Update Contact Info</h4>
                                        <p class="text-xs text-gray-500">Add your phone and WhatsApp</p>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="space-y-4 sm:space-y-6">
                    <!-- Account Settings -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden">
                        <div class="border-b border-gray-200">
                            <div class="p-4 sm:p-5">
                                <h3 class="text-base sm:text-lg font-medium text-gray-900">Account Settings</h3>
                            </div>
                        </div>
                        <div class="p-4 sm:p-5">
                            <nav class="space-y-1 sm:space-y-2">
                                <a href="{{ route('profile.escort.edit') }}" class="flex items-center px-2 sm:px-3 py-1.5 sm:py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-50 hover:text-pink-600">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 mr-1.5 sm:mr-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                    <span class="text-xs sm:text-sm">Edit Profile</span>
                                </a>
                                <a href="{{ route('profile.edit') }}" class="flex items-center px-2 sm:px-3 py-1.5 sm:py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-50 hover:text-pink-600">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 mr-1.5 sm:mr-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    <span class="text-xs sm:text-sm">Account Settings</span>
                                </a>
                                <a href="{{ route('password.change') }}" class="flex items-center px-2 sm:px-3 py-1.5 sm:py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-50 hover:text-pink-600">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 mr-1.5 sm:mr-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                    </svg>
                                    <span class="text-xs sm:text-sm">Change Password</span>
                                </a>

                                <a href="{{ route('notifications.index') }}" class="flex items-center px-2 sm:px-3 py-1.5 sm:py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-50 hover:text-pink-600">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 mr-1.5 sm:mr-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                                    </svg>
                                    <span class="text-xs sm:text-sm">Notifications</span>
                                </a>
                                <a href="{{ route('escort.status-requests.index') }}" class="flex items-center px-2 sm:px-3 py-1.5 sm:py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-50 hover:text-pink-600">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 mr-1.5 sm:mr-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span class="text-xs sm:text-sm">Verification & Featured</span>
                                </a>
                            </nav>
                        </div>
                    </div>

                    <!-- Profile Status -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden">
                        <div class="border-b border-gray-200">
                            <div class="p-4 sm:p-5">
                                <h3 class="text-base sm:text-lg font-medium text-gray-900">Profile Status</h3>
                            </div>
                        </div>
                        <div class="p-4 sm:p-5">
                            <div class="space-y-3 sm:space-y-4">
                                <div class="flex items-center justify-between">
                                    <span class="text-xs sm:text-sm font-medium text-gray-700">Verification Status</span>
                                    @if($escort && $escort->is_verified)
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Verified</span>
                                    @else
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Not Verified</span>
                                    @endif
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-xs sm:text-sm font-medium text-gray-700">Featured Status</span>
                                    @if($escort && $escort->is_featured)
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">Featured</span>
                                    @else
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Not Featured</span>
                                    @endif
                                </div>

                                <div class="mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-gray-200">
                                    <a href="{{ route('escort.status-requests.create') }}" class="w-full inline-flex justify-center items-center px-3 sm:px-4 py-1.5 sm:py-2 bg-pink-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-pink-700 focus:bg-pink-700 active:bg-pink-900 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        Request Verification or Featured
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Earnings Overview -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden mt-4 sm:mt-6">
                        <div class="border-b border-gray-200">
                            <div class="p-4 sm:p-5">
                                <h3 class="text-base sm:text-lg font-medium text-gray-900">Performance Metrics</h3>
                            </div>
                        </div>
                        <div class="p-4 sm:p-5">
                            <div class="space-y-4">
                                <!-- Profile Completion -->
                                <div>
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="text-xs sm:text-sm font-medium text-gray-700">Profile Completion</span>
                                        <span class="text-xs font-semibold text-gray-700">{{ $metrics['profile_completion'] }}%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-pink-600 h-2 rounded-full" style="width: {{ $metrics['profile_completion'] }}%"></div>
                                    </div>
                                    <p class="mt-1 text-xs text-gray-500">Complete your profile to attract more clients</p>
                                </div>

                                <!-- Profile Views -->
                                <div>
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="text-xs sm:text-sm font-medium text-gray-700">Weekly Profile Views</span>
                                        <span class="text-xs font-semibold text-gray-700">{{ rand(50, 200) }}</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-purple-600 h-2 rounded-full" style="width: {{ rand(30, 90) }}%"></div>
                                    </div>
                                    <p class="mt-1 text-xs text-gray-500">
                                        @php
                                            $trend = rand(-20, 30);
                                            $trendClass = $trend > 0 ? 'text-green-600' : ($trend < 0 ? 'text-red-600' : 'text-gray-600');
                                            $trendIcon = $trend > 0 ? '↑' : ($trend < 0 ? '↓' : '→');
                                        @endphp
                                        <span class="{{ $trendClass }}">{{ $trendIcon }} {{ abs($trend) }}%</span> compared to last week
                                    </p>
                                </div>

                                <!-- Contact Rate -->
                                <div>
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="text-xs sm:text-sm font-medium text-gray-700">Contact Rate</span>
                                        <span class="text-xs font-semibold text-gray-700">{{ rand(10, 40) }}%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: {{ rand(10, 40) }}%"></div>
                                    </div>
                                    <p class="mt-1 text-xs text-gray-500">Percentage of views that result in contact</p>
                                </div>
                            </div>

                            <div class="mt-4 pt-4 border-t border-gray-200">
                                <div class="flex justify-between items-center">
                                    <span class="text-xs sm:text-sm font-medium text-gray-700">Improve Your Performance</span>
                                    <a href="{{ route('profile.escort.edit') }}" class="text-xs text-pink-600 hover:text-pink-800">Update Profile</a>
                                </div>
                                <ul class="mt-2 space-y-1">
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-pink-500 mt-0.5 mr-1.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <span class="text-xs text-gray-600">Add more photos to increase engagement</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-pink-500 mt-0.5 mr-1.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <span class="text-xs text-gray-600">Complete your service offerings</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-pink-500 mt-0.5 mr-1.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <span class="text-xs text-gray-600">Request verification to build trust</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
