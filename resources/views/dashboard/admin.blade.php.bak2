<x-app-layout>
    <x-slot name="header">
        <style>
            .bg-pattern {
                background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.2'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            }
            .bg-dots-darker {
                background-image: url("data:image/svg+xml,%3Csvg width='30' height='30' viewBox='0 0 30 30' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1.22676 0C1.91374 0 2.45351 0.539773 2.45351 1.22676C2.45351 1.91374 1.91374 2.45351 1.22676 2.45351C0.539773 2.45351 0 1.91374 0 1.22676C0 0.539773 0.539773 0 1.22676 0Z' fill='rgba(0,0,0,0.07)'/%3E%3C/svg%3E");
            }
        </style>
        <div class="flex justify-between items-center">
            <div class="flex items-center">
                <div class="bg-gradient-to-r from-purple-600 to-indigo-600 p-2 rounded-lg mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <h2 class="font-bold text-xl text-gray-800 leading-tight">
                    {{ __('Admin Dashboard') }}
                </h2>
            </div>
            <div class="flex space-x-2">
                <x-info-button href="{{ route('admin.users.create') }}" class="text-xs uppercase tracking-widest">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                    </svg>
                    {{ __('Add User') }}
                </x-info-button>
                <x-primary-button href="{{ route('admin.announcements.create') }}" class="text-xs uppercase tracking-widest">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
                    </svg>
                    {{ __('Announcement') }}
                </x-primary-button>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Welcome Banner -->
            <div class="bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-500 rounded-xl shadow-xl overflow-hidden mb-6 relative">
                <div class="absolute inset-0 bg-dots-darker opacity-10"></div>
                <div class="px-4 py-6 sm:px-6 md:px-8 md:py-8 md:flex md:items-center md:justify-between relative z-10">
                    <div class="w-full">
                        <h2 class="text-2xl sm:text-3xl font-bold text-white">Welcome to Admin Dashboard</h2>
                        <p class="mt-2 text-sm sm:text-base text-indigo-100">Manage your platform and monitor business performance</p>
                        <div class="mt-4 flex flex-wrap gap-2">
                            <span class="inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium bg-indigo-800 bg-opacity-50 text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                                {{ \App\Models\User::count() }} Users
                            </span>
                            <span class="inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium bg-purple-800 bg-opacity-50 text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                                {{ \App\Models\Escort::count() }} Escorts
                            </span>
                            <span class="inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium bg-pink-800 bg-opacity-50 text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                </svg>
                                {{ \App\Models\Agency::count() }} Agencies
                            </span>
                            <span class="inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium bg-emerald-800 bg-opacity-50 text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                {{ \App\Models\EscortStatusRequest::where('status', 'pending')->count() + \App\Models\AgencyStatusRequest::where('status', 'pending')->count() }} Pending Requests
                            </span>
                        </div>
                    </div>
                    <div class="mt-4 md:mt-0 flex flex-wrap sm:flex-nowrap gap-2 sm:space-y-0 sm:space-x-2">
                        <a href="{{ route('admin.users') }}" class="w-full sm:w-auto inline-flex items-center justify-center px-3 sm:px-4 py-1.5 sm:py-2 bg-white text-indigo-700 border border-transparent rounded-md font-semibold text-xs sm:text-sm tracking-wider hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-indigo-600 transition ease-in-out duration-150 shadow-md">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                            </svg>
                            {{ __('Manage Users') }}
                        </a>
                        <a href="{{ route('admin.status-requests.index') }}" class="w-full sm:w-auto inline-flex items-center justify-center px-3 sm:px-4 py-1.5 sm:py-2 bg-indigo-800 bg-opacity-50 text-white border border-transparent rounded-md font-semibold text-xs sm:text-sm tracking-wider hover:bg-opacity-70 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-indigo-600 transition ease-in-out duration-150 shadow-md">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {{ __('Review Requests') }}
                        </a>
                    </div>
                </div>
            </div>

            <!-- Earnings Overview -->
            <x-admin-card title="Earnings Overview" subtitle="Revenue from verification and featured requests" iconBg="bg-emerald-50" iconColor="text-emerald-500" class="mb-4 sm:mb-6">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />

                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
                    <!-- Total Earnings -->
                    <x-admin-earnings-card
                        title="Total Earnings"
                        value="UGX {{ number_format(\App\Models\EscortStatusRequest::where('status', 'approved')->sum('price') + \App\Models\AgencyStatusRequest::where('status', 'approved')->sum('price'), 0) }}"
                        badgeText="All Time"
                        link="{{ route('admin.status-requests.index') }}"
                        fromColor="from-emerald-50"
                        toColor="to-teal-50"
                        borderColor="border-emerald-100"
                        textColor="text-emerald-900"
                        badgeBgColor="bg-emerald-100"
                        badgeTextColor="text-emerald-600"
                        linkColor="text-emerald-600"
                        linkHoverColor="text-emerald-900"
                        iconBgColor="bg-emerald-100"
                        iconColor="text-emerald-600"
                        icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />'
                    />

                    <!-- Monthly Earnings -->
                    <x-admin-earnings-card
                        title="Monthly Earnings"
                        value="UGX {{ number_format(\App\Models\EscortStatusRequest::where('status', 'approved')->where('approved_at', '>=', now()->startOfMonth())->sum('price') + \App\Models\AgencyStatusRequest::where('status', 'approved')->where('approved_at', '>=', now()->startOfMonth())->sum('price'), 0) }}"
                        badgeText="This Month"
                        link="{{ route('admin.status-requests.index') }}?approved_after={{ now()->startOfMonth()->toDateString() }}"
                        fromColor="from-blue-50"
                        toColor="to-indigo-50"
                        borderColor="border-blue-100"
                        textColor="text-blue-900"
                        badgeBgColor="bg-blue-100"
                        badgeTextColor="text-blue-600"
                        linkColor="text-blue-600"
                        linkHoverColor="text-blue-900"
                        iconBgColor="bg-blue-100"
                        iconColor="text-blue-600"
                        icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />'
                    />

                    <!-- Verification Earnings -->
                    <x-admin-earnings-card
                        title="Verification Revenue"
                        value="UGX {{ number_format(\App\Models\EscortStatusRequest::where('status', 'approved')->where('request_type', 'verification')->sum('price') + \App\Models\AgencyStatusRequest::where('status', 'approved')->where('request_type', 'approval')->sum('price'), 0) }}"
                        badgeText="All Time"
                        link="{{ route('admin.status-requests.index') }}?request_type=verification"
                        fromColor="from-purple-50"
                        toColor="to-violet-50"
                        borderColor="border-purple-100"
                        textColor="text-purple-900"
                        badgeBgColor="bg-purple-100"
                        badgeTextColor="text-purple-600"
                        linkColor="text-purple-600"
                        linkHoverColor="text-purple-900"
                        iconBgColor="bg-purple-100"
                        iconColor="text-purple-600"
                        icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />'
                    />

                    <!-- Featured Earnings -->
                    <x-admin-earnings-card
                        title="Featured Revenue"
                        value="UGX {{ number_format(\App\Models\EscortStatusRequest::where('status', 'approved')->where('request_type', 'featured')->sum('price') + \App\Models\AgencyStatusRequest::where('status', 'approved')->where('request_type', 'featured')->sum('price'), 0) }}"
                        badgeText="All Time"
                        link="{{ route('admin.status-requests.index') }}?request_type=featured"
                        fromColor="from-amber-50"
                        toColor="to-yellow-50"
                        borderColor="border-amber-100"
                        textColor="text-amber-900"
                        badgeBgColor="bg-amber-100"
                        badgeTextColor="text-amber-600"
                        linkColor="text-amber-600"
                        linkHoverColor="text-amber-900"
                        iconBgColor="bg-amber-100"
                        iconColor="text-amber-600"
                        icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />'
                    />
                </div>

                    <!-- Earnings Chart Placeholder -->
                    <div class="mt-4 sm:mt-6 bg-gray-50 rounded-xl p-3 sm:p-4 border border-gray-100">
                        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 sm:mb-4">
                            <h4 class="text-xs sm:text-sm font-medium text-gray-700 mb-2 sm:mb-0">Monthly Revenue Trend</h4>
                            <div class="flex flex-wrap gap-2">
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                    <span class="w-2 h-2 bg-purple-500 rounded-full mr-1"></span>
                                    Verification
                                </span>
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-amber-100 text-amber-800">
                                    <span class="w-2 h-2 bg-amber-500 rounded-full mr-1"></span>
                                    Featured
                                </span>
                            </div>
                        </div>
                        <div class="h-48 sm:h-64 flex items-center justify-center">
                            <div class="text-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 sm:h-12 sm:w-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                </svg>
                                <p class="mt-2 text-xs sm:text-sm text-gray-500">Revenue chart will be displayed here</p>
                                <p class="text-xs text-gray-400">Integrate with a charting library for visualization</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </x-admin-card>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 overflow-hidden">
                <!-- Users Card -->
                <x-admin-stat-card
                    title="Total Users"
                    value="{{ \App\Models\User::count() }}"
                    subtitle="<span class='font-medium'>{{ \App\Models\User::where('user_type', 'admin')->count() }}</span> Admins"
                    link="{{ route('admin.users') }}"
                    linkText="Manage"
                    fromColor="from-indigo-500"
                    toColor="to-indigo-600"
                    textColor="text-indigo-100"
                    iconBgColor="bg-indigo-400 bg-opacity-30"
                    icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />'
                />

                <!-- Escorts Card -->
                <x-admin-stat-card
                    title="Total Escorts"
                    value="{{ \App\Models\Escort::count() }}"
                    subtitle="<span class='font-medium'>{{ \App\Models\Escort::where('is_verified', true)->count() }}</span> Verified"
                    link="{{ route('admin.escorts') }}"
                    linkText="Manage"
                    fromColor="from-pink-500"
                    toColor="to-rose-500"
                    textColor="text-pink-100"
                    iconBgColor="bg-pink-400 bg-opacity-30"
                    icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />'
                />

                <!-- Agencies Card -->
                <x-admin-stat-card
                    title="Total Agencies"
                    value="{{ \App\Models\Agency::count() }}"
                    subtitle="<span class='font-medium'>{{ \App\Models\Agency::where('is_verified', true)->count() }}</span> Verified"
                    link="{{ route('admin.agencies') }}"
                    linkText="Manage"
                    fromColor="from-cyan-500"
                    toColor="to-blue-600"
                    textColor="text-blue-100"
                    iconBgColor="bg-blue-400 bg-opacity-30"
                    icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />'
                />

                <!-- Pending Requests Card -->
                <x-admin-stat-card
                    title="Pending Requests"
                    value="{{ \App\Models\EscortStatusRequest::where('status', 'pending')->count() }}"
                    subtitle="<span class='font-medium'>{{ \App\Models\AgencyStatusRequest::where('status', 'pending')->count() }}</span> Agency Requests"
                    link="{{ route('admin.status-requests.index') }}"
                    linkText="Review"
                    fromColor="from-amber-500"
                    toColor="to-orange-600"
                    textColor="text-amber-100"
                    iconBgColor="bg-amber-400 bg-opacity-30"
                    icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />'
                />
            </div>

            <!-- Main Content -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 overflow-hidden">
                <!-- Left Column -->
                <div class="lg:col-span-2 space-y-4 sm:space-y-6">
                    <!-- Site Statistics -->
                    <x-admin-card title="Site Statistics" subtitle="Overview of your site's content" iconBg="bg-indigo-50" iconColor="text-indigo-500">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            <!-- Main Statistics -->
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6 mb-4 sm:mb-6">
                                <!-- Locations -->
                                <div class="bg-white p-3 sm:p-4 rounded-xl border border-indigo-200 shadow-sm hover:shadow-md transition-shadow duration-300">
                                    <div class="flex items-center mb-2 sm:mb-3">
                                        <div class="bg-indigo-100 p-1.5 sm:p-2 rounded-lg mr-2 sm:mr-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                            </svg>
                                        </div>
                                        <h4 class="text-xs sm:text-sm font-medium text-gray-700">Locations</h4>
                                    </div>
                                    <p class="text-xl sm:text-2xl font-bold text-indigo-700">{{ \App\Models\Location::count() }}</p>
                                    <div class="mt-2 sm:mt-3">
                                        <a href="{{ route('admin.locations') }}" class="text-xs sm:text-sm font-medium text-indigo-600 hover:text-indigo-900 flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="mr-1 h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            </svg>
                                            Manage
                                            <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                            </svg>
                                        </a>
                                    </div>
                                </div>

                                <!-- Services -->
                                <div class="bg-white p-4 rounded-xl border border-purple-200 shadow-sm hover:shadow-md transition-shadow duration-300">
                                    <div class="flex items-center mb-3">
                                        <div class="bg-purple-100 p-2 rounded-lg mr-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                            </svg>
                                        </div>
                                        <h4 class="text-sm font-medium text-gray-700">Services</h4>
                                    </div>
                                    <p class="text-2xl font-bold text-purple-700">{{ \App\Models\Service::count() }}</p>
                                    <div class="mt-3">
                                        <a href="{{ route('admin.services') }}" class="text-sm font-medium text-purple-600 hover:text-purple-900 flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            </svg>
                                            Manage
                                            <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                            </svg>
                                        </a>
                                    </div>
                                </div>

                                <!-- Active Profiles -->
                                <div class="bg-white p-4 rounded-xl border border-violet-200 shadow-sm hover:shadow-md transition-shadow duration-300">
                                    <div class="flex items-center mb-3">
                                        <div class="bg-violet-100 p-2 rounded-lg mr-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-violet-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                            </svg>
                                        </div>
                                        <h4 class="text-sm font-medium text-gray-700">Active Profiles</h4>
                                    </div>
                                    <p class="text-2xl font-bold text-violet-700">{{ \App\Models\Escort::whereHas('user', function($query) { $query->where('is_active', true); })->count() }}</p>
                                    <div class="mt-3">
                                        <a href="{{ route('admin.escorts') }}?user_active=1" class="text-sm font-medium text-violet-600 hover:text-violet-900 flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                            </svg>
                                            View
                                            <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                            </svg>
                                        </a>
                                    </div>
                                </div>

                                <!-- Status Pricing -->
                                <div class="bg-white p-4 rounded-xl border border-emerald-200 shadow-sm hover:shadow-md transition-shadow duration-300">
                                    <div class="flex items-center mb-3">
                                        <div class="bg-emerald-100 p-2 rounded-lg mr-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <h4 class="text-sm font-medium text-gray-700">Status Pricing</h4>
                                    </div>
                                    <p class="text-2xl font-bold text-emerald-700">{{ \App\Models\StatusPricing::count() }}</p>
                                    <div class="mt-3">
                                        <a href="{{ route('admin.status-pricing.index') }}" class="text-sm font-medium text-emerald-600 hover:text-emerald-900 flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            Manage
                                            <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <x-admin-card title="Quick Actions" subtitle="Common administrative tasks" iconBg="bg-rose-50" iconColor="text-rose-500">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />

                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                            <x-admin-action-button
                                title="Add New Escort"
                                subtitle="Create a new escort profile"
                                link="{{ route('admin.escorts.create') }}"
                                fromColor="from-pink-500"
                                toColor="to-rose-500"
                                icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />'
                            />

                            <x-admin-action-button
                                title="Add New Agency"
                                subtitle="Create a new agency profile"
                                link="{{ route('admin.agencies.create') }}"
                                fromColor="from-blue-500"
                                toColor="to-indigo-600"
                                icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />'
                            />

                            <x-admin-action-button
                                title="Review Requests"
                                subtitle="Manage verification requests"
                                link="{{ route('admin.status-requests.index') }}"
                                fromColor="from-amber-500"
                                toColor="to-yellow-500"
                                icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />'
                            />

                            <x-admin-action-button
                                title="Agency Requests"
                                subtitle="Manage agency approvals"
                                link="{{ route('admin.agency-status-requests.index') }}"
                                fromColor="from-cyan-500"
                                toColor="to-blue-600"
                                icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />'
                            />

                            <x-admin-action-button
                                title="Add Location"
                                subtitle="Create a new location"
                                link="{{ route('admin.locations.create') }}"
                                fromColor="from-emerald-500"
                                toColor="to-green-600"
                                icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />'
                            />

                            <x-admin-action-button
                                title="Add Service"
                                subtitle="Create a new service"
                                link="{{ route('admin.services.create') }}"
                                fromColor="from-purple-500"
                                toColor="to-violet-600"
                                icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />'
                            />
                        </div>
                    </x-admin-card>

                    <!-- Recent Transactions -->
                    <x-admin-card title="Recent Transactions" subtitle="Latest approved status requests" iconBg="bg-blue-50" iconColor="text-blue-500">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            @php
                                // Get recent escort status requests
                                $escortRequests = \App\Models\EscortStatusRequest::with('escort')
                                    ->where('status', 'approved')
                                    ->latest('approved_at')
                                    ->take(3)
                                    ->get();

                                // Get recent agency status requests
                                $agencyRequests = \App\Models\AgencyStatusRequest::with('agency')
                                    ->where('status', 'approved')
                                    ->latest('approved_at')
                                    ->take(2)
                                    ->get();

                                // Combine and sort by approved_at
                                $allRequests = $escortRequests->concat($agencyRequests)
                                    ->sortByDesc('approved_at')
                                    ->take(5);
                            @endphp

                            @if($allRequests->isEmpty())
                                <div class="text-center py-8">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <p class="mt-2 text-gray-500">No transactions found</p>
                                </div>
                            @else
                                <div class="space-y-3 sm:space-y-4">
                                    @foreach($allRequests as $request)
                                        <div class="flex items-start p-2 sm:p-3 rounded-lg border border-gray-100 hover:bg-gray-50 transition-colors">
                                            <div class="flex-shrink-0">
                                                @if(get_class($request) === 'App\Models\EscortStatusRequest')
                                                    @if($request->request_type === 'verification')
                                                        <div class="bg-purple-100 rounded-full p-1.5 sm:p-2">
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                                            </svg>
                                                        </div>
                                                    @else
                                                        <div class="bg-amber-100 rounded-full p-1.5 sm:p-2">
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 text-amber-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                                                            </svg>
                                                        </div>
                                                    @endif
                                                @else
                                                    @if($request->request_type === 'approval')
                                                        <div class="bg-blue-100 rounded-full p-1.5 sm:p-2">
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                                            </svg>
                                                        </div>
                                                    @else
                                                        <div class="bg-emerald-100 rounded-full p-1.5 sm:p-2">
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                                                            </svg>
                                                        </div>
                                                    @endif
                                                @endif
                                            </div>
                                            <div class="ml-2 sm:ml-3 flex-1">
                                                <div class="flex justify-between">
                                                    <p class="text-xs sm:text-sm font-medium text-gray-900 truncate max-w-[120px] sm:max-w-none">
                                                        @if(get_class($request) === 'App\Models\EscortStatusRequest')
                                                            {{ $request->escort->name ?? 'Unknown Escort' }} - {{ ucfirst($request->request_type) }}
                                                        @else
                                                            {{ $request->agency->name ?? 'Unknown Agency' }} - {{ ucfirst($request->request_type) }}
                                                        @endif
                                                    </p>
                                                    <p class="text-xs sm:text-sm font-semibold text-emerald-600">UGX {{ number_format($request->price, 0) }}</p>
                                                </div>
                                                <div class="flex justify-between mt-1">
                                                    <p class="text-xs text-gray-500">{{ $request->formatted_duration ?? $request->duration }}</p>
                                                    <p class="text-xs text-gray-500">{{ $request->approved_at ? $request->approved_at->diffForHumans() : 'Recently' }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @endif

                            <div class="mt-4 sm:mt-5 text-center">
                                <a href="{{ route('admin.status-requests.index') }}?status=approved" class="inline-flex items-center text-xs sm:text-sm font-medium text-indigo-600 hover:text-indigo-900">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="mr-1 h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    View all transactions
                                    <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                </x-admin-card>

                <!-- Right Column -->
                <div class="space-y-4 sm:space-y-6">
                    <!-- Admin Tools -->
                    <x-admin-card title="Admin Tools" subtitle="Quick access to management features" iconBg="bg-gradient-to-r from-indigo-500 to-purple-500" iconColor="text-white">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />

                        <div class="grid grid-cols-1 gap-2 sm:gap-3">
                                <a href="{{ route('admin.users') }}" class="group flex items-center px-3 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm font-medium rounded-xl transition-all duration-300 bg-gradient-to-r from-indigo-50 to-indigo-100 hover:from-indigo-100 hover:to-indigo-200 border border-indigo-100">
                                    <div class="flex-shrink-0 bg-indigo-500 text-white p-1.5 sm:p-2 rounded-lg mr-2 sm:mr-3 group-hover:bg-indigo-600 transition-colors">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <span class="text-indigo-900 group-hover:text-indigo-950 font-medium">Manage Users</span>
                                        <p class="text-xs text-indigo-600 mt-0.5">View and manage user accounts</p>
                                    </div>
                                </a>

                                <a href="{{ route('admin.escorts') }}" class="group flex items-center px-3 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm font-medium rounded-xl transition-all duration-300 bg-gradient-to-r from-pink-50 to-pink-100 hover:from-pink-100 hover:to-pink-200 border border-pink-100">
                                    <div class="flex-shrink-0 bg-pink-500 text-white p-1.5 sm:p-2 rounded-lg mr-2 sm:mr-3 group-hover:bg-pink-600 transition-colors">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <span class="text-pink-900 group-hover:text-pink-950 font-medium">Manage Escorts</span>
                                        <p class="text-xs text-pink-600 mt-0.5">View and manage escort profiles</p>
                                    </div>
                                </a>

                                <a href="{{ route('admin.agencies') }}" class="group flex items-center px-3 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm font-medium rounded-xl transition-all duration-300 bg-gradient-to-r from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 border border-blue-100">
                                    <div class="flex-shrink-0 bg-blue-500 text-white p-1.5 sm:p-2 rounded-lg mr-2 sm:mr-3 group-hover:bg-blue-600 transition-colors">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                        </svg>
                                    </div>
                                    <div>
                                        <span class="text-blue-900 group-hover:text-blue-950 font-medium">Manage Agencies</span>
                                        <p class="text-xs text-blue-600 mt-0.5">View and manage agency profiles</p>
                                    </div>
                                </a>

                                <a href="{{ route('admin.locations') }}" class="group flex items-center px-3 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm font-medium rounded-xl transition-all duration-300 bg-gradient-to-r from-emerald-50 to-emerald-100 hover:from-emerald-100 hover:to-emerald-200 border border-emerald-100">
                                    <div class="flex-shrink-0 bg-emerald-500 text-white p-1.5 sm:p-2 rounded-lg mr-2 sm:mr-3 group-hover:bg-emerald-600 transition-colors">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <span class="text-emerald-900 group-hover:text-emerald-950 font-medium">Manage Locations</span>
                                        <p class="text-xs text-emerald-600 mt-0.5">View and manage locations</p>
                                    </div>
                                </a>

                                <a href="{{ route('admin.services') }}" class="group flex items-center px-3 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm font-medium rounded-xl transition-all duration-300 bg-gradient-to-r from-purple-50 to-purple-100 hover:from-purple-100 hover:to-purple-200 border border-purple-100">
                                    <div class="flex-shrink-0 bg-purple-500 text-white p-1.5 sm:p-2 rounded-lg mr-2 sm:mr-3 group-hover:bg-purple-600 transition-colors">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                        </svg>
                                    </div>
                                    <div>
                                        <span class="text-purple-900 group-hover:text-purple-950 font-medium">Manage Services</span>
                                        <p class="text-xs text-purple-600 mt-0.5">View and manage services</p>
                                    </div>
                                </a>

                                <a href="{{ route('admin.announcements.create') }}" class="group flex items-center px-3 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm font-medium rounded-xl transition-all duration-300 bg-gradient-to-r from-rose-50 to-rose-100 hover:from-rose-100 hover:to-rose-200 border border-rose-100">
                                    <div class="flex-shrink-0 bg-rose-500 text-white p-1.5 sm:p-2 rounded-lg mr-2 sm:mr-3 group-hover:bg-rose-600 transition-colors">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <span class="text-rose-900 group-hover:text-rose-950 font-medium">Send Announcement</span>
                                        <p class="text-xs text-rose-600 mt-0.5">Create and send announcements</p>
                                    </div>
                                </a>

                                <a href="{{ route('admin.status-requests.index') }}" class="group flex items-center px-3 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm font-medium rounded-xl transition-all duration-300 bg-gradient-to-r from-amber-50 to-amber-100 hover:from-amber-100 hover:to-amber-200 border border-amber-100">
                                    <div class="flex-shrink-0 bg-amber-500 text-white p-1.5 sm:p-2 rounded-lg mr-2 sm:mr-3 group-hover:bg-amber-600 transition-colors">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <span class="text-amber-900 group-hover:text-amber-950 font-medium">Escort Status Requests</span>
                                        <p class="text-xs text-amber-600 mt-0.5">Manage verification and featured requests</p>
                                    </div>
                                </a>

                                <a href="{{ route('admin.agency-status-requests.index') }}" class="group flex items-center px-3 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm font-medium rounded-xl transition-all duration-300 bg-gradient-to-r from-cyan-50 to-cyan-100 hover:from-cyan-100 hover:to-cyan-200 border border-cyan-100">
                                    <div class="flex-shrink-0 bg-cyan-500 text-white p-1.5 sm:p-2 rounded-lg mr-2 sm:mr-3 group-hover:bg-cyan-600 transition-colors">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4" />
                                        </svg>
                                    </div>
                                    <div>
                                        <span class="text-cyan-900 group-hover:text-cyan-950 font-medium">Agency Status Requests</span>
                                        <p class="text-xs text-cyan-600 mt-0.5">Manage agency approval requests</p>
                                    </div>
                                </a>

                                <a href="{{ route('admin.status-pricing.index') }}" class="group flex items-center px-3 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm font-medium rounded-xl transition-all duration-300 bg-gradient-to-r from-lime-50 to-lime-100 hover:from-lime-100 hover:to-lime-200 border border-lime-100">
                                    <div class="flex-shrink-0 bg-lime-500 text-white p-1.5 sm:p-2 rounded-lg mr-2 sm:mr-3 group-hover:bg-lime-600 transition-colors">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <span class="text-lime-900 group-hover:text-lime-950 font-medium">Status Pricing</span>
                                        <p class="text-xs text-lime-600 mt-0.5">Manage verification and featured pricing</p>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </x-admin-card>

                    <!-- System Status -->
                    <x-admin-card title="System Status" subtitle="Current system information" iconBg="bg-gradient-to-r from-teal-500 to-emerald-500" iconColor="text-white">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />

                        <div class="space-y-4">
                            <div class="bg-gradient-to-r from-teal-50 to-emerald-50 p-4 rounded-xl border border-teal-100 shadow-sm hover:shadow-md transition-all duration-300">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="bg-teal-100 p-2 rounded-lg mr-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-teal-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                                            </svg>
                                        </div>
                                        <span class="text-sm font-medium text-teal-900">PHP Version</span>
                                    </div>
                                    <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-teal-100 text-teal-800">{{ phpversion() }}</span>
                                </div>
                            </div>

                            <div class="bg-gradient-to-r from-red-50 to-rose-50 p-4 rounded-xl border border-red-100 shadow-sm hover:shadow-md transition-all duration-300">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="bg-red-100 p-2 rounded-lg mr-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                                            </svg>
                                        </div>
                                        <span class="text-sm font-medium text-red-900">Laravel Version</span>
                                    </div>
                                    <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-red-100 text-red-800">{{ app()->version() }}</span>
                                </div>
                            </div>

                            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-100 shadow-sm hover:shadow-md transition-all duration-300">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="bg-blue-100 p-2 rounded-lg mr-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z" />
                                            </svg>
                                        </div>
                                        <span class="text-sm font-medium text-blue-900">Environment</span>
                                    </div>
                                    <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">{{ app()->environment() }}</span>
                                </div>
                            </div>

                            <div class="bg-gradient-to-r from-purple-50 to-violet-50 p-4 rounded-xl border border-purple-100 shadow-sm hover:shadow-md transition-all duration-300">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="bg-purple-100 p-2 rounded-lg mr-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <span class="text-sm font-medium text-purple-900">Server Time</span>
                                    </div>
                                    <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">{{ now()->format('Y-m-d H:i') }}</span>
                                </div>
                            </div>
                        </div>
                    </x-admin-card>

                    <!-- Quick Stats -->
                    <x-admin-card title="Quick Stats" subtitle="Last 7 days activity" iconBg="bg-gradient-to-r from-orange-500 to-amber-500" iconColor="text-white">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />

                        <div class="grid grid-cols-2 gap-4">
                            <div class="bg-white p-4 rounded-xl border border-amber-200 shadow-sm hover:shadow-md transition-all duration-300 transform hover:scale-105">
                                <div class="flex items-center justify-center mb-2">
                                    <div class="bg-amber-100 p-2 rounded-full">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-amber-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="text-amber-600 font-bold text-2xl text-center">{{ \App\Models\User::where('created_at', '>=', now()->subDays(7))->count() }}</div>
                                <div class="text-gray-700 text-sm font-medium mt-1 text-center">New Users</div>
                            </div>

                            <div class="bg-white p-4 rounded-xl border border-pink-200 shadow-sm hover:shadow-md transition-all duration-300 transform hover:scale-105">
                                <div class="flex items-center justify-center mb-2">
                                    <div class="bg-pink-100 p-2 rounded-full">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-pink-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="text-pink-600 font-bold text-2xl text-center">{{ \App\Models\Escort::where('created_at', '>=', now()->subDays(7))->count() }}</div>
                                <div class="text-gray-700 text-sm font-medium mt-1 text-center">New Escorts</div>
                            </div>

                            <div class="bg-white p-4 rounded-xl border border-blue-200 shadow-sm hover:shadow-md transition-all duration-300 transform hover:scale-105">
                                <div class="flex items-center justify-center mb-2">
                                    <div class="bg-blue-100 p-2 rounded-full">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="text-blue-600 font-bold text-2xl text-center">{{ \App\Models\Agency::where('created_at', '>=', now()->subDays(7))->count() }}</div>
                                <div class="text-gray-700 text-sm font-medium mt-1 text-center">New Agencies</div>
                            </div>

                            <div class="bg-white p-4 rounded-xl border border-emerald-200 shadow-sm hover:shadow-md transition-all duration-300 transform hover:scale-105">
                                <div class="flex items-center justify-center mb-2">
                                    <div class="bg-emerald-100 p-2 rounded-full">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="text-emerald-600 font-bold text-2xl text-center">{{ \App\Models\EscortStatusRequest::where('created_at', '>=', now()->subDays(7))->count() }}</div>
                                <div class="text-gray-700 text-sm font-medium mt-1 text-center">New Requests</div>
                            </div>
                        </div>
                    </x-admin-card>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
