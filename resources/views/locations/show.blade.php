<x-home-layout>
    <!-- Enhanced SEO Component -->
    <x-location-seo-enhanced type="show" :location="$location" :escort-count="$escorts->total()" />

    <!-- Breadcrumb Navigation -->
    <x-location-breadcrumb :location="$location" />

    <!-- Hero Section -->
    <div class="relative bg-gradient-to-r from-indigo-800 via-purple-700 to-pink-700 text-white py-16 sm:py-20 overflow-hidden">
        <!-- Pattern overlay -->
        <div class="absolute inset-0 opacity-20" style="background-image: url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="1"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');"></div>

        <!-- Dot Pattern Overlay -->
        <div class="absolute inset-0 bg-pattern opacity-40" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgdmlld0JveD0iMCAwIDYwIDYwIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC42Ij48cGF0aCBkPSJNMzYgMzRjMC0yLjIxLTEuNzktNC00LTRzLTQgMS43OS00IDQgMS43OSA0IDQgNCA0LTEuNzkgNC00eiIvPjwvZz48L2c+PC9zdmc+');"></div>

        <!-- Dark overlay gradient -->
        <div class="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-black/20"></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <!-- Breadcrumb -->
            <nav class="mb-6">
                <ol class="flex text-sm">
                    <li>
                        <a href="{{ route('locations.index') }}" class="text-white hover:text-pink-200 transition-colors duration-300 flex items-center font-medium">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                            </svg>
                            Locations
                        </a>
                    </li>

                    @if($location->parent)
                        <li class="mx-2 text-white">/</li>
                        <li>
                            <a href="{{ route('locations.show', $location->parent->slug) }}" class="text-white hover:text-pink-200 transition-colors duration-300 font-medium">
                                {{ $location->parent->name }}
                            </a>
                        </li>
                    @endif

                    <li class="mx-2 text-white">/</li>
                    <li class="text-white font-medium">{{ $location->name }}</li>
                </ol>
            </nav>

            @if(isset($seoContent['h1']))
                <h1 class="text-3xl md:text-5xl font-bold mb-4 text-white drop-shadow-lg tracking-tight">{{ $seoContent['h1'] }}</h1>
            @else
                <h1 class="text-3xl md:text-5xl font-bold mb-4 text-white drop-shadow-lg tracking-tight">Escorts in {{ $location->name }}</h1>
            @endif

            @if(isset($seoContent['intro_text']))
                <p class="text-xl text-white max-w-3xl mb-6 drop-shadow-md leading-relaxed">{{ $seoContent['intro_text'] }}</p>
            @else
                <p class="text-xl text-white max-w-3xl mb-6 drop-shadow-md leading-relaxed">Find the best escorts in {{ $location->name }}</p>
            @endif

            @if($escorts->count() > 0)
                <div class="mt-6 bg-white/15 backdrop-blur-sm rounded-lg px-6 py-4 inline-flex items-center shadow-lg border border-white/20">
                    <svg class="w-6 h-6 text-pink-300 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    <span class="text-white font-semibold text-lg">{{ $escorts->count() }} escorts available in this location</span>
                </div>
            @endif
        </div>
    </div>

    <div class="py-12 relative">
        <!-- Subtle pattern background -->
        <div class="absolute inset-0 opacity-10" style="background-image: url('data:image/svg+xml,%3Csvg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%239C92AC" fill-opacity="0.4" fill-rule="evenodd"%3E%3Ccircle cx="3" cy="3" r="1"%2F%3E%3Ccircle cx="13" cy="13" r="1"%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E');"></div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">

            <!-- Location Information -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 mb-8 hover:shadow-xl transition-all duration-300">
                <div class="bg-gradient-to-r from-indigo-600 to-purple-600 py-5 px-6">
                    <h2 class="text-xl font-bold text-white flex items-center drop-shadow-md">
                        <svg class="w-5 h-5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        About {{ $location->name }}
                    </h2>
                </div>

                <div class="p-6 md:p-8">
                    <div class="flex flex-col md:flex-row gap-8">
                        <div class="md:w-2/3">
                            <p class="text-gray-800 mb-6 leading-relaxed text-base">
                                {{ $location->name }} is a popular destination for those seeking companionship and entertainment.
                                Our verified escorts in {{ $location->name }} offer a range of services to meet your desires.
                            </p>

                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-5 mb-4">
                                <div class="bg-gray-50 p-5 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 hover:border-indigo-200">
                                    <h3 class="font-semibold text-gray-800 mb-2 flex items-center">
                                        <svg class="w-4 h-4 text-indigo-500 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                        </svg>
                                        Location Type
                                    </h3>
                                    <p class="text-gray-800">{{ ucfirst($location->type) }}</p>
                                </div>

                                @if($location->parent)
                                <div class="bg-gray-50 p-5 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 hover:border-indigo-200">
                                    <h3 class="font-semibold text-gray-800 mb-2 flex items-center">
                                        <svg class="w-4 h-4 text-indigo-500 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        Part of
                                    </h3>
                                    <p class="text-gray-800">
                                        <a href="{{ route('locations.show', $location->parent->slug) }}" class="text-indigo-600 hover:text-pink-600 font-medium">
                                            {{ $location->parent->name }}
                                        </a>
                                    </p>
                                </div>
                                @endif

                                <div class="bg-gray-50 p-5 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 hover:border-indigo-200">
                                    <h3 class="font-semibold text-gray-800 mb-2 flex items-center">
                                        <svg class="w-4 h-4 text-indigo-500 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"></path>
                                        </svg>
                                        Available Escorts
                                    </h3>
                                    <p class="text-gray-800">{{ $escorts->total() }} verified escorts</p>
                                </div>

                                @if(isset($popularServices) && $popularServices->count() > 0)
                                <div class="bg-gray-50 p-5 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 hover:border-indigo-200">
                                    <h3 class="font-semibold text-gray-800 mb-2 flex items-center">
                                        <svg class="w-4 h-4 text-indigo-500 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"></path>
                                        </svg>
                                        Popular Services
                                    </h3>
                                    <p class="text-gray-800">{{ $popularServices->first()->name }} and more</p>
                                </div>
                                @endif
                            </div>
                        </div>

                        <div class="md:w-1/3">
                            <div class="bg-gradient-to-br from-indigo-50 to-purple-50 p-6 rounded-lg border border-indigo-200 shadow-sm h-full">
                                <h3 class="font-semibold text-gray-800 mb-4 flex items-center">
                                    <svg class="w-5 h-5 text-indigo-500 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                    Why Choose {{ $location->name }}?
                                </h3>
                                <ul class="space-y-3">
                                    <li class="flex items-start">
                                        <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-gray-800">Verified and professional escorts</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-gray-800">Discreet and confidential service</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-gray-800">Wide range of services available</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-gray-800">Convenient and accessible locations</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Child Locations (if any) -->
            @if(count($childLocations) > 0)
                <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 mb-8 hover:shadow-xl transition-all duration-300">
                    <div class="bg-gradient-to-r from-indigo-600 to-purple-600 py-5 px-6">
                        <h2 class="text-xl font-bold text-white flex items-center drop-shadow-md">
                            <svg class="w-5 h-5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            Browse {{ $location->name }} by {{ $location->type == 'country' ? 'City' : 'Area' }}
                        </h2>
                    </div>

                    <div class="p-6 md:p-8">
                        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                            @foreach($childLocations as $childLocation)
                                <a href="{{ route('locations.show', $childLocation->slug) }}" class="text-indigo-600 hover:text-pink-600 transition-colors duration-300 flex items-center py-2 px-3 rounded-md hover:bg-indigo-50 group border border-transparent hover:border-indigo-100 shadow-sm">
                                    <span class="w-2.5 h-2.5 bg-indigo-500 rounded-full mr-2.5 flex-shrink-0 group-hover:bg-pink-500 transition-colors duration-300"></span>
                                    <span class="font-medium text-gray-800 group-hover:text-indigo-700">{{ $childLocation->name }}</span>
                                </a>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif

            <!-- Escorts List -->
            @if($escorts->count() > 0)
                <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 mb-8 hover:shadow-xl transition-all duration-300">
                    <div class="bg-gradient-to-r from-pink-600 to-pink-800 py-6 px-6">
                        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                            <div>
                                <h2 class="text-xl font-bold text-white flex items-center drop-shadow-md">
                                    <svg class="w-5 h-5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                    </svg>
                                    Escorts in {{ $location->name }}
                                </h2>
                                <p class="text-white mt-2 font-medium">{{ $escorts->count() }} escorts available for booking</p>
                            </div>
                            <button id="filterToggle" class="inline-flex items-center px-5 py-2.5 bg-white/15 hover:bg-white/25 text-white rounded-lg transition-colors duration-300 shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-white/30 border border-white/20">
                                <svg class="w-5 h-5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                                </svg>
                                Filter Escorts
                            </button>
                        </div>
                    </div>

                    <div class="p-6 md:p-8">
                        <!-- Filter Form -->
                        <div id="filterForm" class="bg-gray-50 rounded-lg p-6 mb-8 hidden border border-gray-200 shadow-md">
                            <form action="{{ route('locations.show', $location->slug) }}" method="GET" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                                <div>
                                    <label for="gender" class="block text-sm font-semibold text-gray-800 mb-2">Gender</label>
                                    <select id="gender" name="gender" class="w-full border border-gray-300 rounded-lg py-3 px-4 text-gray-800 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 shadow-sm">
                                        <option value="">All Genders</option>
                                        <option value="female">Female</option>
                                        <option value="male">Male</option>
                                        <option value="trans">Trans</option>
                                        <option value="couple">Couple</option>
                                    </select>
                                </div>

                                <div>
                                    <label for="age" class="block text-sm font-semibold text-gray-800 mb-2">Age Range</label>
                                    <select id="age" name="age" class="w-full border border-gray-300 rounded-lg py-3 px-4 text-gray-800 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 shadow-sm">
                                        <option value="">All Ages</option>
                                        <option value="18-25">18-25</option>
                                        <option value="26-35">26-35</option>
                                        <option value="36-45">36-45</option>
                                        <option value="46+">46+</option>
                                    </select>
                                </div>

                                <div>
                                    <label for="service" class="block text-sm font-semibold text-gray-800 mb-2">Service</label>
                                    <select id="service" name="service" class="w-full border border-gray-300 rounded-lg py-3 px-4 text-gray-800 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 shadow-sm">
                                        <option value="">All Services</option>
                                        @if(isset($popularServices))
                                            @foreach($popularServices as $service)
                                                <option value="{{ $service->id }}">{{ $service->name }}</option>
                                            @endforeach
                                        @endif
                                    </select>
                                </div>

                                <div>
                                    <label for="sort" class="block text-sm font-semibold text-gray-800 mb-2">Sort By</label>
                                    <select id="sort" name="sort" class="w-full border border-gray-300 rounded-lg py-3 px-4 text-gray-800 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 shadow-sm">
                                        <option value="newest">Newest First</option>
                                        <option value="oldest">Oldest First</option>
                                        <option value="price_low">Price: Low to High</option>
                                        <option value="price_high">Price: High to Low</option>
                                        <option value="rating">Rating</option>
                                    </select>
                                </div>

                                <div class="sm:col-span-2 lg:col-span-4 flex justify-end space-x-4 mt-3">
                                    <button type="reset" class="px-6 py-3 border border-gray-300 rounded-lg text-gray-800 hover:bg-gray-50 transition-colors duration-300 shadow-md font-medium focus:outline-none focus:ring-2 focus:ring-gray-300">
                                        Reset
                                    </button>
                                    <button type="submit" class="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors duration-300 shadow-md font-medium focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                                        Apply Filters
                                    </button>
                                </div>
                            </form>
                        </div>

                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                            @foreach($escorts as $escort)
                                @include('escorts.partials.escort-card', ['escort' => $escort])
                            @endforeach
                        </div>

                        <!-- Pagination -->
                        <div class="mt-12 pt-4 border-t border-gray-200">
                            {{ $escorts->links() }}
                        </div>
                    </div>
                </div>
            @else
                <div class="bg-white rounded-lg shadow-md p-12 text-center border border-gray-200 mb-8 hover:shadow-xl transition-all duration-300">
                    <svg class="w-24 h-24 text-gray-300 mx-auto mb-8" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                    </svg>
                    <h3 class="text-2xl font-bold text-gray-800 mb-4">No escorts found</h3>
                    <p class="text-gray-800 max-w-md mx-auto mb-8 leading-relaxed text-lg">There are currently no escorts available in {{ $location->name }}. Please check back later or browse other locations.</p>
                    <div class="mt-6">
                        <a href="{{ route('locations.index') }}" class="inline-flex items-center px-8 py-4 bg-indigo-600 text-white font-medium rounded-lg shadow-md hover:bg-indigo-700 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 text-lg">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                            </svg>
                            Browse All Locations
                        </a>
                    </div>
                </div>
            @endif

            <!-- Popular Services Section -->
            @if(isset($popularServices) && $popularServices->count() > 0)
                <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 mb-8 hover:shadow-xl transition-all duration-300">
                    <div class="bg-gradient-to-r from-pink-600 to-pink-800 py-5 px-6">
                        <h2 class="text-xl font-bold text-white flex items-center drop-shadow-md">
                            <svg class="w-5 h-5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            Popular Services in {{ $location->name }}
                        </h2>
                    </div>

                    <div class="p-6 md:p-8">
                        <div class="flex flex-wrap gap-3">
                            @foreach($popularServices as $service)
                                <span class="inline-flex items-center px-4 py-2 bg-indigo-100 text-indigo-800 rounded-full text-sm font-medium shadow-md hover:bg-indigo-200 transition-colors duration-300 border border-indigo-200">
                                    {{ $service->name }}
                                    <span class="ml-2 bg-indigo-200 text-indigo-800 py-0.5 px-2 text-xs rounded-full font-bold">{{ $service->escorts_count }}</span>
                                </span>
                            @endforeach
                        </div>

                        <p class="mt-6 text-gray-800 text-base leading-relaxed border-t border-gray-100 pt-4">
                            These are the most popular services offered by escorts in {{ $location->name }}.
                            The number indicates how many escorts offer each service.
                        </p>
                    </div>
                </div>
            @endif

            <!-- Upcoming Tours Section -->
            @if(isset($upcomingTours) && $upcomingTours->count() > 0)
                <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 mb-8 hover:shadow-xl transition-all duration-300">
                    <div class="bg-gradient-to-r from-indigo-600 to-purple-600 py-5 px-6">
                        <h2 class="text-xl font-bold text-white flex items-center drop-shadow-md">
                            <svg class="w-5 h-5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                            </svg>
                            Upcoming Tours to {{ $location->name }}
                        </h2>
                    </div>

                    <div class="p-6 md:p-8">
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                            @foreach($upcomingTours as $tour)
                                <div class="border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 hover:border-indigo-300 transform hover:-translate-y-1">
                                    <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-4 py-3 border-b border-gray-200">
                                        <div class="flex justify-between items-center">
                                            <h3 class="font-semibold text-gray-800">{{ $tour->escort->name }}</h3>
                                            <span class="text-xs bg-pink-100 text-pink-800 px-2.5 py-1 rounded-full font-medium shadow-md border border-pink-200">Tour</span>
                                        </div>
                                    </div>
                                    <div class="p-5">
                                        <div class="flex items-center text-sm text-gray-800 mb-3">
                                            <svg class="w-4 h-4 mr-1.5 text-indigo-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                            </svg>
                                            {{ $tour->start_date->format('M d') }} - {{ $tour->end_date->format('M d, Y') }}
                                        </div>
                                        <p class="text-sm text-gray-800 line-clamp-2 mb-4 leading-relaxed">
                                            {{ $tour->description ?? 'Special tour to ' . $location->name . '. Book now for exclusive time with ' . $tour->escort->name . '.' }}
                                        </p>
                                        <a href="{{ route('escorts.show', $tour->escort->slug) }}" class="text-sm text-indigo-600 hover:text-pink-600 font-medium inline-flex items-center group">
                                            View Escort Profile
                                            <svg class="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif

            <!-- Related Locations Section -->
            @if(isset($relatedLocations) && $relatedLocations->count() > 0)
                <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 hover:shadow-xl transition-all duration-300">
                    <div class="bg-gradient-to-r from-indigo-600 to-purple-600 py-5 px-6">
                        <h2 class="text-xl font-bold text-white flex items-center drop-shadow-md">
                            <svg class="w-5 h-5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            Related Locations
                        </h2>
                    </div>

                    <div class="p-6 md:p-8">
                        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                            @foreach($relatedLocations as $relatedLocation)
                                <a href="{{ route('locations.show', $relatedLocation->slug) }}" class="text-indigo-600 hover:text-pink-600 transition-colors duration-300 flex items-center py-2 px-3 rounded-md hover:bg-indigo-50 group border border-transparent hover:border-indigo-100 shadow-sm">
                                    <span class="w-2.5 h-2.5 bg-indigo-500 rounded-full mr-2.5 flex-shrink-0 group-hover:bg-pink-500 transition-colors duration-300"></span>
                                    <span class="font-medium text-gray-800 group-hover:text-indigo-700">{{ $relatedLocation->name }}</span>
                                </a>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Local SEO Content -->
    <x-local-seo-content
        :location="$location"
        :escort-count="$escorts->total()"
        :seo-content="$seoContent ?? []"
    />

    <!-- FAQ Section -->
    <x-location-faq
        :location="$location"
        :faq-data="$faqData ?? null"
    />

    <!-- Internal Links -->
    <x-internal-links
        type="location"
        :location="$location"
    />

    <!-- JavaScript for Filter Toggle -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const filterToggle = document.getElementById('filterToggle');
            const filterForm = document.getElementById('filterForm');

            if (filterToggle && filterForm) {
                // Function to toggle filter form visibility with animation
                function toggleFilterForm() {
                    if (filterForm.classList.contains('hidden')) {
                        // Show the form with animation
                        filterForm.classList.remove('hidden');
                        filterForm.style.opacity = '0';
                        filterForm.style.transform = 'translateY(-20px)';

                        setTimeout(() => {
                            filterForm.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                            filterForm.style.opacity = '1';
                            filterForm.style.transform = 'translateY(0)';
                        }, 10);

                        // Update button
                        filterToggle.innerHTML = `
                            <svg class="w-5 h-5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Hide Filters
                        `;
                        filterToggle.classList.add('bg-white/25');
                        filterToggle.classList.remove('bg-white/15');

                        // Smooth scroll to filter form
                        filterForm.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    } else {
                        // Hide the form with animation
                        filterForm.style.opacity = '0';
                        filterForm.style.transform = 'translateY(-20px)';

                        setTimeout(() => {
                            filterForm.classList.add('hidden');
                            filterForm.style.transition = '';
                        }, 300);

                        // Update button
                        filterToggle.innerHTML = `
                            <svg class="w-5 h-5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                            </svg>
                            Filter Escorts
                        `;
                        filterToggle.classList.remove('bg-white/25');
                        filterToggle.classList.add('bg-white/15');
                    }
                }

                // Add click event listener
                filterToggle.addEventListener('click', toggleFilterForm);

                // Initialize form with URL parameters if any
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.toString()) {
                    // Show the form
                    filterForm.classList.remove('hidden');

                    // Set form values based on URL parameters
                    for (const [key, value] of urlParams.entries()) {
                        const input = document.querySelector(`[name="${key}"]`);
                        if (input) {
                            input.value = value;
                        }
                    }

                    // Update button
                    filterToggle.innerHTML = `
                        <svg class="w-5 h-5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Hide Filters
                    `;
                    filterToggle.classList.add('bg-white/25');
                    filterToggle.classList.remove('bg-white/15');

                    // Add active filter indicator
                    const filterCount = Object.keys(Object.fromEntries(urlParams)).length;
                    if (filterCount > 0) {
                        const indicator = document.createElement('span');
                        indicator.className = 'absolute -top-2 -right-2 bg-pink-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center';
                        indicator.textContent = filterCount;
                        filterToggle.style.position = 'relative';
                        filterToggle.appendChild(indicator);
                    }
                }

                // Add reset button functionality
                const resetButton = filterForm.querySelector('button[type="reset"]');
                if (resetButton) {
                    resetButton.addEventListener('click', function(e) {
                        // Clear all form fields
                        const formInputs = filterForm.querySelectorAll('select, input');
                        formInputs.forEach(input => {
                            input.value = '';
                        });

                        // Submit the form to clear URL parameters
                        setTimeout(() => {
                            filterForm.querySelector('button[type="submit"]').click();
                        }, 100);
                    });
                }
            }
        });
    </script>
</x-home-layout>