<x-home-layout>
    <!-- Comprehensive SEO Component -->
    <x-locations-index-seo
        :seo-meta="$seoMeta ?? []"
        :structured-data="$structuredData ?? []"
        :total-cities="$totalCities ?? 0"
        :total-escorts="$totalEscorts ?? 0"
        :search="request('search')"
    />

    <!-- Breadcrumb Navigation -->
    <x-location-breadcrumb :search="request('search')" />

    <!-- Hero Section -->
    <div class="relative bg-gradient-to-r from-indigo-800 via-purple-700 to-pink-700 text-white py-16 sm:py-20 md:py-24 overflow-hidden">
        <!-- Pattern overlay -->
        <div class="absolute inset-0 opacity-20" style="background-image: url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="1"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');"></div>

        <!-- Dot Pattern Overlay -->
        <div class="absolute inset-0 bg-pattern opacity-40" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgdmlld0JveD0iMCAwIDYwIDYwIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC42Ij48cGF0aCBkPSJNMzYgMzRjMC0yLjIxLTEuNzktNC00LTRzLTQgMS43OS00IDQgMS43OSA0IDQgNCA0LTEuNzkgNC00eiIvPjwvZz48L2c+PC9zdmc+');"></div>

        <!-- Dark overlay gradient -->
        <div class="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-black/20"></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
            <h1 class="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 sm:mb-8 text-white drop-shadow-lg tracking-tight">Browse by Location</h1>
            <p class="text-lg sm:text-xl md:text-2xl text-white max-w-3xl mx-auto mb-8 sm:mb-10 drop-shadow-md leading-relaxed">Find escorts and agencies in your area or anywhere you plan to visit</p>

            <!-- Location Search Form -->
            <div class="max-w-2xl mx-auto mb-8">
                <form action="{{ route('locations.index') }}" method="GET" class="relative">
                    <div class="relative">
                        <input
                            type="text"
                            name="search"
                            value="{{ request('search') }}"
                            placeholder="Search for a city, area, or region..."
                            class="w-full px-6 py-4 pl-14 pr-32 text-lg text-gray-900 bg-white/95 backdrop-blur-sm rounded-full border-2 border-white/20 focus:border-pink-400 focus:ring-4 focus:ring-pink-400/20 focus:outline-none shadow-xl transition-all duration-300"
                        >
                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <div class="absolute inset-y-0 right-0 flex items-center">
                            <button
                                type="submit"
                                class="mr-2 px-6 py-2 bg-gradient-to-r from-pink-500 to-pink-600 text-white font-semibold rounded-full hover:from-pink-600 hover:to-pink-700 focus:outline-none focus:ring-4 focus:ring-pink-400/20 shadow-lg transition-all duration-300 transform hover:scale-105"
                            >
                                Search
                            </button>
                        </div>
                    </div>
                </form>

                @if(request('search'))
                    <div class="mt-4 flex items-center justify-center">
                        <span class="text-white/80 text-sm mr-3">Searching for: "{{ request('search') }}"</span>
                        <a href="{{ route('locations.index') }}" class="text-pink-200 hover:text-white text-sm underline transition-colors duration-300">
                            Clear search
                        </a>
                    </div>
                @endif
            </div>

            <!-- Quick Location Links -->
            <div class="flex flex-wrap justify-center gap-3 mb-6">
                @if(count($countriesWithCities) > 0)
                    @foreach(array_slice($countriesWithCities, 0, 3) as $countryData)
                        <a href="{{ route('locations.show', $countryData['country']->slug) }}" class="inline-flex items-center px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-full text-sm font-medium transition-all duration-300 backdrop-blur-sm border border-white/20 hover:border-white/40">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                            </svg>
                            {{ $countryData['country']->name }}
                        </a>
                    @endforeach
                @endif
            </div>
        </div>
    </div>

    <div class="py-8 sm:py-12 relative">
        <!-- Subtle pattern background -->
        <div class="absolute inset-0 opacity-10" style="background-image: url('data:image/svg+xml,%3Csvg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%239C92AC" fill-opacity="0.4" fill-rule="evenodd"%3E%3Ccircle cx="3" cy="3" r="1"%2F%3E%3Ccircle cx="13" cy="13" r="1"%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E');"></div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">

            @if(session('error'))
                <div class="mb-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
                    <div class="flex items-center justify-center mb-4">
                        <svg class="w-12 h-12 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-yellow-800 mb-2">Locations Update in Progress</h3>
                    <p class="text-yellow-700">{{ session('error') }}</p>
                    <div class="mt-4">
                        <a href="{{ route('escorts.index') }}" class="inline-flex items-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors duration-300">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            Browse All Escorts
                        </a>
                    </div>
                </div>
            @elseif(count($countriesWithCities) > 0)
                @if(request('search'))
                    <div class="mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <svg class="w-6 h-6 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            <h3 class="text-lg font-semibold text-blue-800">Search Results for "{{ request('search') }}"</h3>
                        </div>
                        <p class="text-blue-700">Found {{ count($countriesWithCities) }} location(s) matching your search.</p>
                    </div>
                @endif

                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6">
                    @foreach($countriesWithCities as $countryData)
                        <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 border border-gray-200 transform hover:-translate-y-1">
                            <div class="bg-gradient-to-r from-indigo-600 to-purple-600 py-4 sm:py-5 px-4 sm:px-6">
                                <h2 class="text-xl sm:text-2xl font-bold text-white drop-shadow-sm">{{ $countryData['country']->name }}</h2>
                            </div>
                            <div class="p-4 sm:p-6">
                                <div class="space-y-4 sm:space-y-5">
                                    @foreach($countryData['cities'] as $cityData)
                                        <div class="border-b border-gray-200 pb-4 sm:pb-5 last:border-b-0 last:pb-0">
                                            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3">
                                                <h3 class="text-base sm:text-lg font-semibold flex items-center">
                                                    <svg class="w-4 h-4 sm:w-5 sm:h-5 text-indigo-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    </svg>
                                                    <a href="{{ route('locations.show', $cityData['city']->slug) }}" class="text-indigo-600 hover:text-pink-600 transition-colors duration-300 font-medium">
                                                        {{ $cityData['city']->name }}
                                                    </a>
                                                </h3>
                                                @if(isset($cityData['escort_count']) && $cityData['escort_count'] > 0)
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800 mt-1 sm:mt-0">
                                                        {{ $cityData['escort_count'] }} {{ $cityData['escort_count'] == 1 ? 'escort' : 'escorts' }}
                                                    </span>
                                                @endif
                                            </div>

                                            @if(isset($cityData['areas']) && count($cityData['areas']) > 0)
                                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3 pl-6 sm:pl-7">
                                                @foreach($cityData['areas'] as $area)
                                                    <a href="{{ route('locations.show', $area->slug) }}" class="text-gray-800 hover:text-pink-600 text-sm flex items-center transition-colors duration-300 py-1">
                                                        <span class="w-1.5 h-1.5 bg-indigo-500 rounded-full mr-2 flex-shrink-0"></span>
                                                        <span class="truncate">{{ $area->name }}</span>
                                                    </a>
                                                @endforeach

                                                @if(isset($cityData['area_count']) && $cityData['area_count'] > 6)
                                                    <a href="{{ route('locations.show', $cityData['city']->slug) }}" class="text-pink-600 hover:text-pink-700 text-sm font-medium transition-colors duration-300 flex items-center py-1">
                                                        <svg class="w-3.5 h-3.5 mr-1.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                            <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                                        </svg>
                                                        {{ $cityData['area_count'] - 6 }} more areas
                                                    </a>
                                                @endif
                                            </div>
                                            @else
                                            <div class="pl-6 sm:pl-7 text-sm text-gray-500 italic">
                                                No areas available yet
                                            </div>
                                            @endif
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-12">
                    <div class="max-w-md mx-auto">
                        @if(request('search'))
                            <svg class="w-24 h-24 text-gray-300 mx-auto mb-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            <h3 class="text-2xl font-bold text-gray-800 mb-4">No Search Results</h3>
                            <p class="text-gray-600 mb-8 leading-relaxed">No locations found matching "{{ request('search') }}". Try searching for a different city, area, or region.</p>
                            <div class="space-y-4">
                                <a href="{{ route('locations.index') }}" class="inline-flex items-center px-6 py-3 bg-indigo-600 text-white font-medium rounded-lg shadow-md hover:bg-indigo-700 transition-colors duration-300">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    View All Locations
                                </a>
                                <a href="{{ route('escorts.index') }}" class="inline-flex items-center px-6 py-3 bg-pink-600 text-white font-medium rounded-lg shadow-md hover:bg-pink-700 transition-colors duration-300 ml-4">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                    Browse All Escorts
                                </a>
                            </div>
                        @else
                            <svg class="w-24 h-24 text-gray-300 mx-auto mb-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <h3 class="text-2xl font-bold text-gray-800 mb-4">No Locations Available</h3>
                            <p class="text-gray-600 mb-8 leading-relaxed">We're currently updating our location database. Please check back soon or browse our escorts directly.</p>
                            <a href="{{ route('escorts.index') }}" class="inline-flex items-center px-6 py-3 bg-indigo-600 text-white font-medium rounded-lg shadow-md hover:bg-indigo-700 transition-colors duration-300">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                Browse All Escorts
                            </a>
                        @endif
                    </div>
                </div>
            @endif

            <!-- Call to Action Section -->
            <div class="mt-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg shadow-xl overflow-hidden border-2 border-indigo-700">
                <div class="px-6 py-12 md:py-16 md:px-12 text-center sm:text-left sm:flex sm:items-center sm:justify-between">
                    <div>
                        <h3 class="text-2xl sm:text-3xl font-bold text-white drop-shadow-md">Can't find your location?</h3>
                        <p class="mt-3 text-white text-sm sm:text-base max-w-md leading-relaxed">Contact us to request adding a new location or browse our full selection of escorts.</p>
                    </div>
                    <div class="mt-8 sm:mt-0">
                        <a href="{{ route('escorts.index') }}" class="inline-flex items-center justify-center px-6 py-3 bg-white text-indigo-600 font-semibold rounded-lg shadow-md hover:bg-gray-50 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 text-base">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            Browse All Escorts
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-home-layout>