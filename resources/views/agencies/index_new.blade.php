<x-public-layout>
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-gray-900 via-pink-900 to-gray-900 text-white">
        <div class="absolute inset-0 bg-pattern opacity-10"></div>
        <div class="container mx-auto px-4 py-16 md:py-24 relative z-10">
            <div class="max-w-4xl mx-auto text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-4">
                    Find the Perfect <span class="text-pink-300">Escort Agency</span>
                </h1>
                <p class="text-xl text-gray-200 mb-8">
                    Discover top-rated escort agencies offering premium services in Uganda
                </p>

                <!-- Search Form -->
                <div class="bg-white bg-opacity-10 backdrop-blur-sm p-4 rounded-lg shadow-lg mb-10">
                    <form action="{{ route('agencies.search') }}" method="GET" class="flex flex-col md:flex-row gap-3">
                        <div class="flex-grow">
                            <input
                                type="text"
                                name="query"
                                placeholder="Search by name, location or services..."
                                class="w-full px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-pink text-gray-900"
                                value="{{ $query ?? '' }}"
                            >
                        </div>
                        <button type="submit" class="btn btn-primary flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Search
                        </button>
                    </form>
                </div>

                <!-- Stats -->
                <div class="grid grid-cols-3 gap-4 max-w-lg mx-auto">
                    <div class="bg-white bg-opacity-10 rounded-lg p-3 text-center">
                        <div class="text-3xl font-bold">{{ count($agencies) + (isset($premiumAgencies) ? count($premiumAgencies) : 0) }}</div>
                        <div class="text-sm text-gray-300">Agencies</div>
                    </div>
                    <div class="bg-white bg-opacity-10 rounded-lg p-3 text-center">
                        <div class="text-3xl font-bold">{{ isset($premiumAgencies) ? count($premiumAgencies) : 0 }}</div>
                        <div class="text-sm text-gray-300">Premium</div>
                    </div>
                    <div class="bg-white bg-opacity-10 rounded-lg p-3 text-center">
                        <div class="text-3xl font-bold">24/7</div>
                        <div class="text-sm text-gray-300">Service</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            @if(isset($query))
                <div class="mb-10 text-center">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">
                        Search Results for "{{ $query }}"
                    </h2>
                    <p class="text-gray-600">
                        Found {{ $agencies->total() }} {{ Str::plural('agency', $agencies->total()) }}
                    </p>
                </div>
            @endif

            <!-- Premium Agencies Section -->
            @if(isset($premiumAgencies) && $premiumAgencies->count() > 0)
                <div class="mb-16">
                    <div class="flex items-center justify-between mb-8">
                        <div>
                            <span class="inline-block px-3 py-1 bg-pink-100 text-pink rounded-full text-xs font-medium">FEATURED</span>
                            <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mt-2">Premium Agencies</h2>
                        </div>
                        <a href="#all-agencies" class="text-pink hover:text-pink-700 font-medium flex items-center">
                            View All
                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($premiumAgencies as $agency)
                            <div class="agency-card">
                                <a href="{{ route('agencies.show', ['slug' => $agency->slug ?? $agency->id]) }}" class="block">
                                    <div class="relative h-48 bg-gray-200 overflow-hidden">
                                        @if($agency->logo_path)
                                            <img
                                                src="{{ asset('storage/' . $agency->logo_path) }}"
                                                alt="{{ $agency->name }}"
                                                class="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                                            >
                                        @else
                                            <div class="w-full h-full flex items-center justify-center bg-gray-100">
                                                <svg class="w-16 h-16 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                                </svg>
                                            </div>
                                        @endif

                                        <!-- Premium Badge -->
                                        <div class="absolute top-3 right-3 badge badge-premium shadow-md">
                                            PREMIUM
                                        </div>

                                        <!-- Verified Badge (if applicable) -->
                                        @if($agency->is_verified)
                                            <div class="absolute bottom-3 left-3 badge badge-verified flex items-center shadow-md">
                                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                </svg>
                                                VERIFIED
                                            </div>
                                        @endif
                                    </div>

                                    <div class="p-5">
                                        <h3 class="text-xl font-bold text-gray-900 mb-2 hover:text-pink transition-colors duration-300">{{ $agency->name }}</h3>

                                        @if($agency->address)
                                            <div class="flex items-center text-gray-600 mb-3">
                                                <svg class="w-4 h-4 mr-2 text-pink" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                </svg>
                                                {{ $agency->address }}
                                            </div>
                                        @endif

                                        <p class="text-gray-600 line-clamp-2 mb-4">
                                            {{ $agency->description ?? 'No description available.' }}
                                        </p>

                                        <div class="flex justify-between items-center pt-3 border-t border-gray-100">
                                            <div class="flex items-center space-x-4">
                                                <div class="flex items-center text-gray-500 text-sm">
                                                    <svg class="w-4 h-4 mr-1 text-pink" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                                                    </svg>
                                                    {{ $agency->escorts->count() }}
                                                </div>

                                                <div class="flex items-center text-gray-500 text-sm">
                                                    <svg class="w-4 h-4 mr-1 text-pink" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                    </svg>
                                                    {{ $agency->profile_views ?? 0 }}
                                                </div>
                                            </div>

                                            <span class="text-pink font-medium text-sm flex items-center">
                                                View Details
                                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                </svg>
                                            </span>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- All Agencies Section -->
            <div id="all-agencies" class="mb-10">
                @if(!isset($query))
                    <div class="flex items-center justify-between mb-8">
                        <div>
                            <span class="inline-block px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-xs font-medium">DIRECTORY</span>
                            <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mt-2">All Agencies</h2>
                        </div>
                        <div class="flex items-center">
                            <span class="text-gray-500 mr-2">Sort by:</span>
                            <select class="bg-white border border-gray-200 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-pink">
                                <option>Newest</option>
                                <option>Most Popular</option>
                                <option>Alphabetical</option>
                            </select>
                        </div>
                    </div>
                @endif

                @if($agencies->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($agencies as $agency)
                            <div class="agency-card">
                                <a href="{{ route('agencies.show', ['slug' => $agency->slug ?? $agency->id]) }}" class="block">
                                    <div class="relative h-48 bg-gray-200 overflow-hidden">
                                        @if($agency->logo_path)
                                            <img
                                                src="{{ asset('storage/' . $agency->logo_path) }}"
                                                alt="{{ $agency->name }}"
                                                class="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                                            >
                                        @else
                                            <div class="w-full h-full flex items-center justify-center bg-gray-100">
                                                <svg class="w-16 h-16 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                                </svg>
                                            </div>
                                        @endif

                                        @if($agency->is_premium)
                                            <div class="absolute top-3 right-3 badge badge-premium shadow-md">
                                                PREMIUM
                                            </div>
                                        @endif

                                        @if($agency->is_verified)
                                            <div class="absolute bottom-3 left-3 badge badge-verified flex items-center shadow-md">
                                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                </svg>
                                                VERIFIED
                                            </div>
                                        @endif
                                    </div>

                                    <div class="p-5">
                                        <h3 class="text-xl font-bold text-gray-900 mb-2 hover:text-pink transition-colors duration-300">{{ $agency->name }}</h3>

                                        @if($agency->address)
                                            <div class="flex items-center text-gray-600 mb-3">
                                                <svg class="w-4 h-4 mr-2 text-pink" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                </svg>
                                                {{ $agency->address }}
                                            </div>
                                        @endif

                                        <p class="text-gray-600 line-clamp-2 mb-4">
                                            {{ $agency->description ?? 'No description available.' }}
                                        </p>

                                        <div class="flex justify-between items-center pt-3 border-t border-gray-100">
                                            <div class="flex items-center space-x-4">
                                                <div class="flex items-center text-gray-500 text-sm">
                                                    <svg class="w-4 h-4 mr-1 text-pink" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                                                    </svg>
                                                    {{ $agency->escorts->count() }}
                                                </div>

                                                <div class="flex items-center text-gray-500 text-sm">
                                                    <svg class="w-4 h-4 mr-1 text-pink" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                    </svg>
                                                    {{ $agency->profile_views ?? 0 }}
                                                </div>
                                            </div>

                                            <span class="text-pink font-medium text-sm flex items-center">
                                                View Details
                                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                </svg>
                                            </span>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="mt-10 flex justify-center">
                        <div class="bg-white px-4 py-3 rounded-lg shadow">
                            {{ $agencies->links() }}
                        </div>
                    </div>
                @else
                    <div class="bg-white rounded-lg shadow p-8 text-center max-w-2xl mx-auto">
                        <div class="bg-pink bg-opacity-10 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg class="w-10 h-10 text-pink" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-3">No agencies found</h3>
                        <p class="text-gray-600 mb-6 text-lg">
                            @if(isset($query))
                                We couldn't find any agencies matching "{{ $query }}".
                            @else
                                There are no agencies available at the moment.
                            @endif
                        </p>
                        <a href="{{ route('agencies.index') }}" class="btn btn-primary shadow">
                            View all agencies
                        </a>
                    </div>
                @endif
            </div>

            <!-- Call to Action Section -->
            @if($agencies->count() > 0)
                <div class="mt-16 bg-gray-900 rounded-lg p-8 md:p-10 shadow-lg relative overflow-hidden">
                    <div class="absolute inset-0 bg-pattern opacity-10"></div>
                    <div class="relative z-10 md:flex items-center justify-between">
                        <div class="md:w-2/3 mb-6 md:mb-0">
                            <h3 class="text-2xl md:text-3xl font-bold text-white mb-3">Are you an agency owner?</h3>
                            <p class="text-gray-300 text-lg">Join our platform and showcase your escorts to thousands of potential clients.</p>
                        </div>
                        <div>
                            <a href="#" class="btn btn-primary font-bold shadow">
                                Register Now
                            </a>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </section>
</x-public-layout>
