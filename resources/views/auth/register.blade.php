<x-guest-layout>
    <x-slot name="title">Create Account</x-slot>

    <form method="POST" action="{{ route('register') }}">
        @csrf

        <!-- Username -->
        <div class="mb-4">
            <x-input-label for="username" :value="__('Username')" />
            <x-text-input id="username" type="text" name="username" :value="old('username')" required autofocus autocomplete="username" placeholder="Choose a username" />
            <x-input-error :messages="$errors->get('username')" class="mt-2" />
        </div>

        <!-- User Type -->
        <div class="mb-4">
            <x-input-label for="user_type" :value="__('Register as')" />
            <select id="user_type" name="user_type" class="form-input">
                <option value="escort" {{ (old('user_type') == 'escort' || request()->query('user_type') == 'escort') ? 'selected' : '' }}>Escort</option>
                <option value="agency" {{ (old('user_type') == 'agency' || request()->query('user_type') == 'agency') ? 'selected' : '' }}>Agency</option>
            </select>
            <x-input-error :messages="$errors->get('user_type')" class="mt-2" />
        </div>

        <!-- Email Address -->
        <div class="mb-4">
            <x-input-label for="email" :value="__('Email')" />
            <x-text-input id="email" type="email" name="email" :value="old('email')" required autocomplete="username" placeholder="Your email address" />
            <x-input-error :messages="$errors->get('email')" class="mt-2" />
        </div>

        <!-- Password -->
        <div class="mb-4">
            <x-input-label for="password" :value="__('Password')" />
            <x-text-input id="password" type="password" name="password" required autocomplete="new-password" placeholder="Create a strong password" />
            <p class="mt-1 text-xs text-gray-400">Must be at least 8 characters long and include a mix of letters, numbers, and symbols</p>
            <x-input-error :messages="$errors->get('password')" class="mt-2" />
        </div>

        <!-- Confirm Password -->
        <div class="mb-4">
            <x-input-label for="password_confirmation" :value="__('Confirm Password')" />
            <x-text-input id="password_confirmation" type="password" name="password_confirmation" required autocomplete="new-password" placeholder="Confirm your password" />
            <x-input-error :messages="$errors->get('password_confirmation')" class="mt-2" />
        </div>

        <!-- Terms Agreement -->
        <div class="mb-6">
            <label class="flex items-center">
                <input type="checkbox" name="terms" class="rounded bg-gray-700 border-gray-600 text-pink focus:ring-pink" required>
                <span class="ml-2 text-sm text-gray-300">I agree to the <a href="#" class="text-pink hover:text-pink-400">Terms of Service</a> and <a href="#" class="text-pink hover:text-pink-400">Privacy Policy</a></span>
            </label>
        </div>

        <!-- Submit Button -->
        <div class="mb-6">
            <button type="submit" class="btn-primary w-full">
                Create Account
            </button>
        </div>

        <div class="pt-4 border-t border-gray-700 text-center">
            <p class="text-sm text-gray-400 mb-4">Already have an account?</p>
            <a href="{{ route('login') }}" class="btn-dark w-full inline-block">
                Sign In
            </a>
            <p class="mt-4 text-sm text-pink font-medium">Must be 18+ to register</p>
        </div>
    </form>
</x-guest-layout>
