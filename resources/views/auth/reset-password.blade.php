<x-guest-layout>
    <x-slot name="title">Set New Password</x-slot>

    <div class="mb-6 text-sm text-gray-300">
        {{ __('Please create a new password for your account. Make sure it\'s secure and different from your previous password.') }}
    </div>

    <form method="POST" action="{{ route('password.store') }}">
        @csrf

        <!-- Password Reset Token -->
        <input type="hidden" name="token" value="{{ $request->route('token') }}">

        <!-- Email Address -->
        <div class="mb-4">
            <x-input-label for="email" :value="__('Email Address')" />
            <x-text-input id="email" type="email" name="email" :value="old('email', $request->email)" required autofocus autocomplete="username" readonly />
            <x-input-error :messages="$errors->get('email')" class="mt-2" />
        </div>

        <!-- Password -->
        <div class="mb-4">
            <x-input-label for="password" :value="__('New Password')" />
            <x-text-input id="password" type="password" name="password" required autocomplete="new-password" placeholder="Enter your new password" />
            <p class="mt-1 text-xs text-gray-400">Must be at least 8 characters long and include a mix of letters, numbers, and symbols</p>
            <x-input-error :messages="$errors->get('password')" class="mt-2" />
        </div>

        <!-- Confirm Password -->
        <div class="mb-6">
            <x-input-label for="password_confirmation" :value="__('Confirm New Password')" />
            <x-text-input id="password_confirmation" type="password" name="password_confirmation" required autocomplete="new-password" placeholder="Confirm your new password" />
            <x-input-error :messages="$errors->get('password_confirmation')" class="mt-2" />
        </div>

        <!-- Submit Button -->
        <div class="mb-6">
            <button type="submit" class="btn-primary w-full">
                Reset Password
            </button>
        </div>

        <div class="text-center">
            <a class="text-sm text-gray-400 hover:text-pink" href="{{ route('login') }}">
                {{ __('← Back to login') }}
            </a>
        </div>
    </form>
</x-guest-layout>
