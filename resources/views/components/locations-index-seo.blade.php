@props(['seoMeta' => [], 'structuredData' => [], 'totalCities' => 0, 'totalEscorts' => 0, 'search' => null])

@php
    $title = $seoMeta['title'] ?? 'Browse Escorts by Location | Uganda & East Africa Directory';
    $description = $seoMeta['description'] ?? 'Find professional escorts by location in Uganda and East Africa.';
    $keywords = $seoMeta['keywords'] ?? 'escort locations Uganda, escorts by city';
    $canonical = $seoMeta['canonical'] ?? route('locations.index');
    $image = $seoMeta['image'] ?? asset('images/locations-og.jpg');
    
    if ($search) {
        $canonical .= '?search=' . urlencode($search);
    }
@endphp

@push('head')
    <!-- Primary SEO Meta Tags -->
    <title>{{ $title }}</title>
    <meta name="description" content="{{ $description }}">
    <meta name="keywords" content="{{ $keywords }}">
    <meta name="robots" content="index, follow, max-image-preview:large">
    <link rel="canonical" href="{{ $canonical }}">
    
    <!-- Geographic Meta Tags -->
    <meta name="geo.region" content="UG">
    <meta name="geo.placename" content="Uganda">
    <meta name="ICBM" content="0.3476, 32.5825">
    <meta name="geo.position" content="0.3476;32.5825">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="{{ $title }}">
    <meta property="og:description" content="{{ $description }}">
    <meta property="og:url" content="{{ $canonical }}">
    <meta property="og:site_name" content="{{ config('app.name') }}">
    <meta property="og:locale" content="en_UG">
    <meta property="og:image" content="{{ $image }}">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:alt" content="Escort Locations in Uganda and East Africa">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ $title }}">
    <meta name="twitter:description" content="{{ $description }}">
    <meta name="twitter:image" content="{{ $image }}">
    <meta name="twitter:image:alt" content="Browse escort locations across Uganda">
    
    <!-- Additional SEO Meta Tags -->
    <meta name="author" content="{{ config('app.name') }}">
    <meta name="publisher" content="{{ config('app.name') }}">
    <meta name="copyright" content="{{ config('app.name') }}">
    <meta name="distribution" content="global">
    <meta name="rating" content="adult">
    <meta name="audience" content="adult">
    
    <!-- Hreflang Tags -->
    <link rel="alternate" hreflang="en-ug" href="{{ $canonical }}">
    <link rel="alternate" hreflang="en-ke" href="{{ $canonical }}">
    <link rel="alternate" hreflang="en-tz" href="{{ $canonical }}">
    <link rel="alternate" hreflang="en" href="{{ $canonical }}">
    
    <!-- Structured Data -->
    @if(!empty($structuredData))
        <script type="application/ld+json">
            {!! json_encode($structuredData, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) !!}
        </script>
    @endif
    
    <!-- Additional Structured Data for Website -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "{{ config('app.name') }}",
        "url": "{{ config('app.url') }}",
        "description": "{{ $description }}",
        "potentialAction": {
            "@type": "SearchAction",
            "target": {
                "@type": "EntryPoint",
                "urlTemplate": "{{ route('locations.index') }}?search={search_term_string}"
            },
            "query-input": "required name=search_term_string"
        },
        "publisher": {
            "@type": "Organization",
            "name": "{{ config('app.name') }}",
            "url": "{{ config('app.url') }}"
        }
    }
    </script>
    
    <!-- Organization Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "{{ config('app.name') }}",
        "url": "{{ config('app.url') }}",
        "logo": "{{ asset('images/logo.png') }}",
        "description": "Uganda's premier escort platform connecting discerning clients with professional companions",
        "address": {
            "@type": "PostalAddress",
            "addressCountry": "Uganda",
            "addressRegion": "Central Region"
        },
        "areaServed": [
            {
                "@type": "Country",
                "name": "Uganda"
            },
            {
                "@type": "Country", 
                "name": "Kenya"
            },
            {
                "@type": "Country",
                "name": "Tanzania"
            }
        ],
        "serviceType": "Professional Companion Services"
    }
    </script>
    
    @if($search)
        <!-- Search Results Structured Data -->
        <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "SearchResultsPage",
            "name": "Search Results for '{{ $search }}'",
            "description": "Search results for escort locations matching '{{ $search }}' in Uganda",
            "url": "{{ $canonical }}",
            "mainEntity": {
                "@type": "ItemList",
                "name": "Location Search Results",
                "description": "Locations matching search query '{{ $search }}'"
            }
        }
        </script>
    @endif
    
    <!-- FAQ Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "mainEntity": [
            {
                "@type": "Question",
                "name": "How many cities are covered in Uganda?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "We cover {{ $totalCities }} cities across Uganda and East Africa, with {{ $totalEscorts }} verified professional escorts available."
                }
            },
            {
                "@type": "Question",
                "name": "Which are the most popular escort locations in Uganda?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "The most popular locations include Kampala, Entebbe, Jinja, Mbarara, and Gulu, with the highest concentration of verified professional escorts."
                }
            },
            {
                "@type": "Question",
                "name": "Are all escorts verified in these locations?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Yes, all escorts listed on our platform go through a comprehensive verification process to ensure authenticity and professionalism."
                }
            },
            {
                "@type": "Question",
                "name": "Can I search for escorts in specific areas?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Yes, you can search for escorts by country, city, or specific area using our location search feature."
                }
            }
        ]
    }
    </script>
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="{{ asset('css/app.css') }}" as="style">
    <link rel="preload" href="{{ asset('js/app.js') }}" as="script">
    
    <!-- DNS Prefetch -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//www.google-analytics.com">
    <link rel="dns-prefetch" href="//www.googletagmanager.com">
    
    <!-- Preconnect to External Domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Additional Meta Tags for Performance -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="theme-color" content="#ec4899">
    
    <!-- Viewport Meta Tag -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
@endpush
