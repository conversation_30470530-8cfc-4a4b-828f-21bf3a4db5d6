@props(['agency' => null, 'type' => 'index'])

@php
    $siteName = setting('site_name', 'Get Hot Babes');
    $siteDescription = setting('site_description', 'Premium escort directory in Uganda and East Africa');

    // Generate agency-specific SEO content
    if ($type === 'show' && $agency) {
        // Individual agency page SEO
        $title = "{$agency->name} - Premium Escort Agency in Uganda | {$siteName}";

        // Generate description
        $description = "{$agency->name} is a premium escort agency in Uganda offering professional companion services. ";
        if ($agency->description) {
            $description .= substr(strip_tags($agency->description), 0, 100) . "... ";
        }
        $description .= "Verified agency with high-quality escorts in Kampala, Entebbe, and other major Ugandan cities.";

        // Generate keywords
        $keywords = [
            $agency->name,
            'escort agency',
            'Uganda escort agency',
            'Kampala escort agency',
            'premium agency',
            'verified agency',
            'professional escorts',
            'companion agency',
            'Uganda',
            'East Africa',
            'escort services',
            'high class agency'
        ];

        $canonicalUrl = route('agencies.show', $agency->slug ?? $agency->id);

    } else {
        // Agencies index page SEO
        $title = "Premium Escort Agencies in Uganda | {$siteName}";
        $description = "Find verified premium escort agencies in Uganda. Browse professional agencies offering high-quality companion services in Kampala, Entebbe, Jinja, Mbarara and across Uganda. Trusted escort agency directory.";

        $keywords = [
            'escort agencies Uganda',
            'Kampala escort agencies',
            'Uganda escort services',
            'premium agencies',
            'verified agencies',
            'professional escort agencies',
            'companion agencies',
            'East Africa agencies',
            'escort agency directory',
            'high class agencies',
            'elite agencies Uganda',
            'trusted agencies',
            'Entebbe agencies',
            'Jinja agencies',
            'Mbarara agencies'
        ];

        $canonicalUrl = route('agencies.index');
    }

    $keywordString = implode(', ', array_unique($keywords));
@endphp

<!-- SEO Meta Tags -->
<title>{{ $title }}</title>
<meta name="description" content="{{ $description }}">
<meta name="keywords" content="{{ $keywordString }}">
<meta name="robots" content="index, follow">
<meta name="author" content="{{ $siteName }}">

<!-- Canonical URL -->
<link rel="canonical" href="{{ $canonicalUrl }}">

<!-- Open Graph Meta Tags -->
<meta property="og:title" content="{{ $title }}">
<meta property="og:description" content="{{ $description }}">
<meta property="og:type" content="website">
<meta property="og:url" content="{{ $canonicalUrl }}">
<meta property="og:site_name" content="{{ $siteName }}">
<meta property="og:locale" content="en_UG">

@if($type === 'show' && $agency && $agency->logo_path)
<meta property="og:image" content="{{ asset('storage/' . $agency->logo_path) }}">
<meta property="og:image:width" content="1200">
<meta property="og:image:height" content="630">
<meta property="og:image:alt" content="{{ $agency->name }} - Premium Escort Agency in Uganda">
@endif

<!-- Twitter Card Meta Tags -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="{{ $title }}">
<meta name="twitter:description" content="{{ $description }}">
<meta name="twitter:site" content="@{{ strtolower(str_replace(' ', '', $siteName)) }}">

<!-- Hreflang for Regional SEO -->
<link rel="alternate" hreflang="en-ug" href="{{ $canonicalUrl }}">
<link rel="alternate" hreflang="en-ke" href="{{ $canonicalUrl }}">
<link rel="alternate" hreflang="en-tz" href="{{ $canonicalUrl }}">
<link rel="alternate" hreflang="en-rw" href="{{ $canonicalUrl }}">
<link rel="alternate" hreflang="en" href="{{ $canonicalUrl }}">

@if($type === 'show' && $agency && $agency->logo_path)
<meta name="twitter:image" content="{{ asset('storage/' . $agency->logo_path) }}">
<meta name="twitter:image:alt" content="{{ $agency->name }} - Premium Escort Agency in Uganda">
@endif

<!-- Uganda-Specific Local SEO Meta Tags -->
<meta name="geo.region" content="UG">
<meta name="geo.country" content="Uganda">
<meta name="location" content="Uganda, East Africa">
<meta name="coverage" content="Uganda">
<meta name="distribution" content="local">
<meta name="target" content="Uganda, East Africa">

<!-- Structured Data - Organization (Main Site) -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "{{ $siteName }}",
    "url": "{{ url('/') }}",
    "logo": "{{ asset('favicon.svg') }}",
    "description": "{{ $siteDescription }}",
    "address": {
        "@type": "PostalAddress",
        "addressCountry": "UG",
        "addressRegion": "Uganda"
    },
    "areaServed": [
        {
            "@type": "Country",
            "name": "Uganda"
        },
        {
            "@type": "Country",
            "name": "East Africa"
        }
    ],
    "serviceArea": {
        "@type": "Country",
        "name": "Uganda"
    }
}
</script>

@if($type === 'show' && $agency)
<!-- Structured Data - Organization (Agency) -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "{{ $agency->name }}",
    "url": "{{ route('agencies.show', $agency->slug ?? $agency->id) }}",
    @if($agency->logo_path)
    "logo": "{{ asset('storage/' . $agency->logo_path) }}",
    @endif
    "description": "{{ $description }}",
    @if($agency->website)
    "url": "{{ $agency->website }}",
    @endif
    @if($agency->phone)
    "telephone": "{{ $agency->phone }}",
    @endif
    @if($agency->email)
    "email": "{{ $agency->email }}",
    @endif
    "address": {
        "@type": "PostalAddress",
        "addressCountry": "UG",
        "addressRegion": "Uganda"
    },
    "areaServed": {
        "@type": "Country",
        "name": "Uganda"
    },
    "serviceType": "Escort Agency Services",
    "foundingDate": "{{ $agency->created_at->format('Y') }}",
    "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "5",
        "bestRating": "5",
        "worstRating": "1",
        "ratingCount": "1"
    }
}
</script>

<!-- Structured Data - Service -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Premium Escort Services by {{ $agency->name }}",
    "description": "Professional escort and companion services in Uganda",
    "provider": {
        "@type": "Organization",
        "name": "{{ $agency->name }}"
    },
    "areaServed": {
        "@type": "Country",
        "name": "Uganda"
    },
    "serviceType": "Escort Agency Services",
    "category": "Companion Services"
}
</script>

@else
<!-- Structured Data - ItemList for Agencies Index -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": "Premium Escort Agencies in Uganda",
    "description": "Directory of verified premium escort agencies in Uganda and East Africa",
    "url": "{{ route('agencies.index') }}",
    "numberOfItems": "{{ isset($agencies) ? $agencies->total() : 0 }}",
    "itemListOrder": "https://schema.org/ItemListOrderDescending"
}
</script>

<!-- Structured Data - WebPage -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Premium Escort Agencies in Uganda",
    "description": "{{ $description }}",
    "url": "{{ route('agencies.index') }}",
    "mainEntity": {
        "@type": "ItemList",
        "name": "Uganda Escort Agency Directory",
        "description": "Comprehensive directory of premium escort agencies in Uganda"
    },
    "breadcrumb": {
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "{{ url('/') }}"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "Agencies",
                "item": "{{ route('agencies.index') }}"
            }
        ]
    }
}
</script>
@endif

<!-- Local Business Schema for Uganda Focus -->
@if($type === 'show' && $agency)
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "{{ $agency->name }}",
    "description": "{{ $description }}",
    "url": "{{ route('agencies.show', $agency->slug ?? $agency->id) }}",
    @if($agency->logo_path)
    "image": "{{ asset('storage/' . $agency->logo_path) }}",
    @endif
    "address": {
        "@type": "PostalAddress",
        "addressCountry": "UG",
        "addressRegion": "Uganda",
        "addressLocality": "Kampala"
    },
    "geo": {
        "@type": "GeoCoordinates",
        "latitude": "0.3476",
        "longitude": "32.5825"
    },
    "areaServed": [
        "Kampala",
        "Entebbe",
        "Jinja",
        "Mbarara",
        "Uganda"
    ],
    "serviceArea": {
        "@type": "Country",
        "name": "Uganda"
    },
    "priceRange": "$$$",
    "currenciesAccepted": "UGX, USD",
    "paymentAccepted": "Cash, Mobile Money"
}
</script>
@endif
