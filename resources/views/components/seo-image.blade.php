@props([
    'src' => '',
    'alt' => '',
    'escort' => null,
    'imageIndex' => 0,
    'class' => '',
    'loading' => 'lazy',
    'width' => null,
    'height' => null
])

@php
    // Generate SEO-optimized alt text if escort is provided
    if ($escort && !$alt) {
        $seoService = app(\App\Services\SeoService::class);
        $alt = $seoService->generateImageAltText($escort, $imageIndex);
    }
    
    // Ensure alt text is not empty
    if (!$alt) {
        $alt = 'Professional escort service image';
    }
    
    // Optimize image path
    $optimizedSrc = $src;
    if (str_starts_with($src, 'escorts/') || str_starts_with($src, 'agencies/')) {
        $optimizedSrc = asset('storage/' . $src);
    }
@endphp

<img 
    src="{{ $optimizedSrc }}" 
    alt="{{ $alt }}"
    class="{{ $class }}"
    loading="{{ $loading }}"
    @if($width) width="{{ $width }}" @endif
    @if($height) height="{{ $height }}" @endif
    {{ $attributes }}
>
