@props(['location', 'escortCount', 'seoContent' => []])

<div class="bg-white py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Main SEO Content -->
        <div class="prose prose-lg max-w-none">
            @if(isset($seoContent['h1']))
                <h1 class="text-3xl font-bold text-gray-900 mb-6">{{ $seoContent['h1'] }}</h1>
            @endif
            
            @if(isset($seoContent['intro_text']))
                <p class="text-lg text-gray-700 mb-6 leading-relaxed">{{ $seoContent['intro_text'] }}</p>
            @endif
            
            @if(isset($seoContent['location_description']))
                <p class="text-gray-600 mb-6">{{ $seoContent['location_description'] }}</p>
            @endif
        </div>
        
        <!-- Location Stats -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8 mb-8">
            <div class="bg-gradient-to-r from-pink-50 to-purple-50 p-6 rounded-lg border border-pink-100">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-pink-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM9 9a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Available Escorts</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $escortCount }}+</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-lg border border-green-100">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Verification Status</p>
                        <p class="text-lg font-bold text-green-700">100% Verified</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-lg border border-purple-100">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Safety & Privacy</p>
                        <p class="text-lg font-bold text-purple-700">Guaranteed</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Safety Information -->
        @if(isset($seoContent['safety_text']))
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-blue-600 mt-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-blue-900 mb-2">Safety & Verification</h3>
                        <p class="text-blue-800">{{ $seoContent['safety_text'] }}</p>
                    </div>
                </div>
            </div>
        @endif
        
        <!-- Location Guide -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
            <div>
                <h2 class="text-2xl font-bold text-gray-900 mb-4">About {{ $location->name }}</h2>
                <div class="prose text-gray-600">
                    <p>{{ $location->name }} is a premier destination for professional escort services in {{ $location->parent?->name ?? 'Uganda' }}. Our platform connects discerning clients with verified, professional companions who provide exceptional service.</p>
                    
                    <p>All escorts in {{ $location->name }} are carefully verified through our comprehensive screening process, ensuring authenticity, professionalism, and safety for all parties involved.</p>
                </div>
            </div>
            
            <div>
                <h2 class="text-2xl font-bold text-gray-900 mb-4">Why Choose Our Platform</h2>
                <ul class="space-y-3 text-gray-600">
                    <li class="flex items-start">
                        <svg class="h-5 w-5 text-green-500 mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span>100% verified escort profiles</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="h-5 w-5 text-green-500 mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span>Safe and discreet platform</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="h-5 w-5 text-green-500 mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span>Professional customer support</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="h-5 w-5 text-green-500 mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span>Easy booking and communication</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
