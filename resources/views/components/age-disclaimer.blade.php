<!-- Age Disclaimer Modal -->
<div id="age-disclaimer-modal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 bg-gray-900 bg-opacity-90 transition-opacity backdrop-blur-sm" aria-hidden="true"></div>

        <!-- Modal panel -->
        <div class="inline-block align-bottom bg-gradient-to-br from-white to-gray-50 rounded-xl px-4 pt-5 pb-4 text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6 border border-gray-200">
            <div class="sm:flex sm:items-start">
                <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <!-- Warning Icon -->
                    <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                </div>
                <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <div class="flex items-center mb-2">
                        <!-- Left Icon - Two People Silhouette -->
                        <svg class="w-5 h-5 mr-2 text-pink-500" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M7 4c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zm10 0c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zM5 9h4c.6 0 1 .4 1 1v9c0 .6-.4 1-1 1H7v-6H6v6H5c-.6 0-1-.4-1-1v-9c0-.6.4-1 1-1zm10 0h4c.6 0 1 .4 1 1v9c0 .6-.4 1-1 1h-1v-6h-1v6h-2c-.6 0-1-.4-1-1v-9c0-.6.4-1 1-1z"/>
                        </svg>
                        <h3 class="text-lg leading-6 font-bold text-gray-900" id="modal-title">
                            Get Hot Babes - Age Verification
                        </h3>
                        <!-- Right Icon - Bed -->
                        <svg class="w-5 h-5 ml-2 text-pink-500" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M21 10.78V8c0-1.65-1.35-3-3-3h-4c-.77 0-1.47.3-2 .78-.53-.48-1.23-.78-2-.78H6c-1.65 0-3 1.35-3 3v2.78c-.61.55-1 1.34-1 2.22v6h2v-2h16v2h2v-6c0-.88-.39-1.67-1-2.22zM14 7h4c.55 0 1 .45 1 1v2h-6V8c0-.55.45-1 1-1zM5 8c0-.55.45-1 1-1h4c.55 0 1 .45 1 1v2H5V8zm-1 4h16v2H4v-2z"/>
                        </svg>
                    </div>
                    <div class="mt-2">
                        <p class="text-sm text-gray-500 mb-4">
                            This website contains adult content and is intended for adults only.
                        </p>
                        <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h4 class="text-sm font-medium text-yellow-800">
                                        Important Notice
                                    </h4>
                                    <div class="mt-1 text-sm text-yellow-700">
                                        <ul class="list-disc list-inside space-y-1">
                                            <li>You must be 18 years or older to access this website</li>
                                            <li>This site contains explicit adult content</li>
                                            <li>By continuing, you confirm you are of legal age</li>
                                            <li>You agree to our terms and conditions</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <p class="text-xs text-gray-400 text-center">
                            If you are under 18 or do not wish to view adult content, please leave this site immediately.
                        </p>
                    </div>
                </div>
            </div>
            <div class="mt-6 sm:mt-5 sm:flex sm:flex-row-reverse gap-3">
                <button type="button" id="confirm-age" class="w-full inline-flex justify-center items-center rounded-lg border border-transparent shadow-lg px-6 py-3 bg-gradient-to-r from-pink-600 to-pink-700 text-base font-semibold text-white hover:from-pink-700 hover:to-pink-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 sm:w-auto sm:text-sm transition-all duration-300 transform hover:scale-105">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                    Yes, I am 18+ - Enter Site
                </button>
                <button type="button" id="deny-age" class="mt-3 w-full inline-flex justify-center items-center rounded-lg border border-red-300 shadow-lg px-6 py-3 bg-gradient-to-r from-red-50 to-red-100 text-base font-semibold text-red-700 hover:from-red-100 hover:to-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:w-auto sm:text-sm transition-all duration-300 transform hover:scale-105">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                    No, I am under 18 - Exit
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check if user has already confirmed age (valid for 24 hours)
    const ageConfirmed = localStorage.getItem('age_confirmed');
    const ageConfirmedDate = localStorage.getItem('age_confirmed_date');
    const modal = document.getElementById('age-disclaimer-modal');

    // Check if age confirmation is still valid (24 hours)
    let isValidConfirmation = false;
    if (ageConfirmed && ageConfirmedDate) {
        const confirmDate = new Date(ageConfirmedDate);
        const now = new Date();
        const hoursDiff = (now - confirmDate) / (1000 * 60 * 60);
        isValidConfirmation = hoursDiff < 24;
    }

    if (!isValidConfirmation) {
        // Clear old confirmation and show modal
        localStorage.removeItem('age_confirmed');
        localStorage.removeItem('age_confirmed_date');

        // Show modal with fade-in effect
        setTimeout(() => {
            modal.classList.remove('hidden');
            modal.style.opacity = '0';
            modal.style.transition = 'opacity 0.3s ease-in-out';
            setTimeout(() => {
                modal.style.opacity = '1';
            }, 10);
        }, 500); // Small delay to ensure page is loaded

        document.body.style.overflow = 'hidden'; // Prevent scrolling
    }

    // Handle age confirmation
    document.getElementById('confirm-age').addEventListener('click', function() {
        // Store confirmation with timestamp
        localStorage.setItem('age_confirmed', 'true');
        localStorage.setItem('age_confirmed_date', new Date().toISOString());

        // Fade out modal
        modal.style.opacity = '0';
        setTimeout(() => {
            modal.classList.add('hidden');
            document.body.style.overflow = 'auto'; // Restore scrolling
        }, 300);

        // Optional: Track age verification for analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', 'age_verification', {
                'event_category': 'compliance',
                'event_label': 'confirmed'
            });
        }
    });

    // Handle age denial
    document.getElementById('deny-age').addEventListener('click', function() {
        // Track denial for analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', 'age_verification', {
                'event_category': 'compliance',
                'event_label': 'denied'
            });
        }

        // Show exit message before redirect
        alert('Thank you for your honesty. You will be redirected to a safe website.');

        // Redirect to a safe site
        window.location.href = 'https://www.google.com';
    });

    // Prevent closing modal by clicking outside or pressing escape
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            e.preventDefault();
            e.stopPropagation();
        }
    });

    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !localStorage.getItem('age_confirmed')) {
            e.preventDefault();
        }
    });

    // Prevent right-click on modal to avoid bypassing
    modal.addEventListener('contextmenu', function(e) {
        e.preventDefault();
    });
});
</script>
