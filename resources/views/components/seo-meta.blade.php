@props(['meta' => [], 'structuredData' => null])

@php
    $seoService = app(\App\Services\SeoService::class);
    $meta = $seoService->generateMetaTags($meta);
@endphp

<!-- Basic Meta Tags -->
<title>{{ $meta['title'] }}</title>
<meta name="description" content="{{ $meta['description'] }}">
<meta name="keywords" content="{{ $meta['keywords'] }}">
<meta name="author" content="{{ $meta['site_name'] }}">
<meta name="robots" content="index, follow">
<meta name="language" content="English">
<meta name="revisit-after" content="7 days">

<!-- Canonical URL -->
<link rel="canonical" href="{{ $meta['canonical'] }}">

<!-- Open Graph Meta Tags -->
<meta property="og:title" content="{{ $meta['title'] }}">
<meta property="og:description" content="{{ $meta['description'] }}">
<meta property="og:type" content="{{ $meta['type'] }}">
<meta property="og:url" content="{{ $meta['url'] }}">
<meta property="og:site_name" content="{{ $meta['site_name'] }}">
<meta property="og:locale" content="{{ $meta['locale'] }}">
@if($meta['image'])
<meta property="og:image" content="{{ $meta['image'] }}">
<meta property="og:image:width" content="1200">
<meta property="og:image:height" content="630">
<meta property="og:image:alt" content="{{ $meta['title'] }}">
@endif

<!-- Twitter Card Meta Tags -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="{{ $meta['title'] }}">
<meta name="twitter:description" content="{{ $meta['description'] }}">
@if($meta['image'])
<meta name="twitter:image" content="{{ $meta['image'] }}">
<meta name="twitter:image:alt" content="{{ $meta['title'] }}">
@endif

<!-- Additional SEO Meta Tags -->
<meta name="theme-color" content="#ec4899">
<meta name="msapplication-TileColor" content="#ec4899">

<!-- Geographic Meta Tags for Uganda/East Africa -->
<meta name="geo.region" content="UG">
<meta name="geo.country" content="Uganda">
<meta name="geo.placename" content="Uganda">
<meta name="ICBM" content="1.3733, 32.2903"> <!-- Uganda coordinates -->

<!-- Adult Content Warning -->
<meta name="rating" content="adult">
<meta name="content-rating" content="mature">

<!-- Structured Data (JSON-LD) -->
@if($structuredData)
<script type="application/ld+json">
{!! json_encode($structuredData, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) !!}
</script>
@endif

<!-- Preconnect to external domains for performance -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link rel="preconnect" href="https://cdnjs.cloudflare.com">

<!-- DNS Prefetch for better performance -->
<link rel="dns-prefetch" href="//fonts.googleapis.com">
<link rel="dns-prefetch" href="//fonts.gstatic.com">
<link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
