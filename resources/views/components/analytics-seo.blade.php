@props(['page' => 'general'])

@php
    $gaId = setting('google_analytics_id', env('GOOGLE_ANALYTICS_ID'));
    $gscId = setting('google_search_console_id', env('GOOGLE_SEARCH_CONSOLE_ID'));
    $fbPixelId = setting('facebook_pixel_id', env('FACEBOOK_PIXEL_ID'));
    $hotjarId = setting('hotjar_id', env('HOTJAR_ID'));
@endphp

@if($gaId)
<!-- Google Analytics 4 -->
<script async src="https://www.googletagmanager.com/gtag/js?id={{ $gaId }}"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', '{{ $gaId }}', {
    // Uganda-specific configuration
    country: 'UG',
    region: 'Uganda',
    language: 'en-UG',
    currency: 'UGX',
    // Enhanced ecommerce for escort services
    enhanced_ecommerce: true,
    // Custom dimensions for Uganda SEO tracking
    custom_map: {
      'custom_dimension_1': 'location',
      'custom_dimension_2': 'service_type',
      'custom_dimension_3': 'user_type'
    }
  });

  // Track Uganda-specific events
  gtag('event', 'page_view', {
    page_title: document.title,
    page_location: window.location.href,
    country: 'Uganda',
    region: 'East Africa'
  });

  @if($page === 'escort_detail')
  // Track escort profile views
  gtag('event', 'view_item', {
    currency: 'UGX',
    value: 0,
    items: [{
      item_id: '{{ isset($escort) ? $escort->id : '' }}',
      item_name: '{{ isset($escort) ? $escort->stage_name : '' }}',
      item_category: 'Escort Profile',
      item_location: '{{ isset($escort) && $escort->locations->count() > 0 ? $escort->locations->first()->name : '' }}',
      quantity: 1
    }]
  });
  @elseif($page === 'agency_detail')
  // Track agency profile views
  gtag('event', 'view_item', {
    currency: 'UGX',
    value: 0,
    items: [{
      item_id: '{{ isset($agency) ? $agency->id : '' }}',
      item_name: '{{ isset($agency) ? $agency->name : '' }}',
      item_category: 'Agency Profile',
      quantity: 1
    }]
  });
  @elseif($page === 'location')
  // Track location page views
  gtag('event', 'view_item_list', {
    item_list_id: 'location_escorts',
    item_list_name: 'Escorts in {{ isset($location) ? $location->name : '' }}',
    items: [
      @if(isset($escorts))
      @foreach($escorts->take(5) as $escort)
      {
        item_id: '{{ $escort->id }}',
        item_name: '{{ $escort->stage_name }}',
        item_category: 'Escort',
        item_location: '{{ $location->name ?? '' }}',
        index: {{ $loop->index }}
      }@if(!$loop->last),@endif
      @endforeach
      @endif
    ]
  });
  @endif
</script>
@endif

@if($gscId)
<!-- Google Search Console Verification -->
<meta name="google-site-verification" content="{{ $gscId }}">
@endif

@if($fbPixelId)
<!-- Facebook Pixel -->
<script>
!function(f,b,e,v,n,t,s)
{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};
if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];
s.parentNode.insertBefore(t,s)}(window, document,'script',
'https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '{{ $fbPixelId }}');
fbq('track', 'PageView');

// Uganda-specific Facebook tracking
fbq('trackCustom', 'UgandaPageView', {
  country: 'Uganda',
  region: 'East Africa',
  page_type: '{{ $page }}'
});

@if($page === 'escort_detail')
// Track escort profile views
fbq('track', 'ViewContent', {
  content_type: 'escort_profile',
  content_ids: ['{{ isset($escort) ? $escort->id : '' }}'],
  content_name: '{{ isset($escort) ? $escort->stage_name : '' }}',
  content_category: 'Escort Services',
  value: 0,
  currency: 'UGX'
});
@elseif($page === 'contact')
// Track contact page views
fbq('track', 'Contact');
@endif
</script>
<noscript><img height="1" width="1" style="display:none"
src="https://www.facebook.com/tr?id={{ $fbPixelId }}&ev=PageView&noscript=1"
/></noscript>
@endif

@if($hotjarId)
<!-- Hotjar Tracking -->
<script>
    (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:{{ $hotjarId }},hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
</script>
@endif

<!-- Schema.org Tracking for Uganda SEO -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "{{ setting('site_name', 'Get Hot Babes') }}",
  "url": "{{ url('/') }}",
  "potentialAction": {
    "@type": "SearchAction",
    "target": {
      "@type": "EntryPoint",
      "urlTemplate": "{{ url('/') }}/escorts?search={search_term_string}"
    },
    "query-input": "required name=search_term_string"
  },
  "areaServed": {
    "@type": "Country",
    "name": "Uganda"
  },
  "inLanguage": "en-UG"
}
</script>

<!-- Uganda-specific Local Business Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "LocalBusiness",
  "name": "{{ setting('site_name', 'Get Hot Babes') }}",
  "description": "Premium escort directory serving Uganda and East Africa",
  "url": "{{ url('/') }}",
  "telephone": "+256-XXX-XXXXXX",
  "address": {
    "@type": "PostalAddress",
    "addressCountry": "UG",
    "addressRegion": "Central Region",
    "addressLocality": "Kampala"
  },
  "geo": {
    "@type": "GeoCoordinates",
    "latitude": "0.3476",
    "longitude": "32.5825"
  },
  "areaServed": [
    {
      "@type": "City",
      "name": "Kampala"
    },
    {
      "@type": "City", 
      "name": "Entebbe"
    },
    {
      "@type": "City",
      "name": "Jinja"
    },
    {
      "@type": "City",
      "name": "Mbarara"
    },
    {
      "@type": "Country",
      "name": "Uganda"
    }
  ],
  "serviceArea": {
    "@type": "Country",
    "name": "Uganda"
  },
  "openingHours": "Mo-Su 00:00-23:59",
  "currenciesAccepted": "UGX",
  "paymentAccepted": "Cash, Mobile Money",
  "priceRange": "$$$"
}
</script>

<!-- Performance Monitoring for Uganda Users -->
<script>
// Monitor page load performance for Uganda users
window.addEventListener('load', function() {
  if ('performance' in window) {
    const perfData = performance.getEntriesByType('navigation')[0];
    
    // Send performance data to analytics
    if (typeof gtag !== 'undefined') {
      gtag('event', 'timing_complete', {
        name: 'page_load',
        value: Math.round(perfData.loadEventEnd - perfData.fetchStart),
        event_category: 'Performance',
        event_label: 'Uganda User'
      });
    }
  }
});

// Track scroll depth for engagement
let maxScroll = 0;
window.addEventListener('scroll', function() {
  const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
  if (scrollPercent > maxScroll) {
    maxScroll = scrollPercent;
    
    // Track milestone scroll depths
    if ([25, 50, 75, 90].includes(scrollPercent)) {
      if (typeof gtag !== 'undefined') {
        gtag('event', 'scroll', {
          event_category: 'Engagement',
          event_label: scrollPercent + '%',
          value: scrollPercent
        });
      }
    }
  }
});
</script>
