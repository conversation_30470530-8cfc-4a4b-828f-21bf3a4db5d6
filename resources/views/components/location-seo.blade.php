@props(['location' => null, 'escorts' => null, 'type' => 'location'])

@php
    $siteName = setting('site_name', 'Get Hot Babes');
    $siteDescription = setting('site_description', 'Premium escort directory in Uganda and East Africa');

    // Generate location-specific content
    $locationPath = $location ? ($location->full_path ?? $location->name) : null;
    $escortCount = $escorts ? $escorts->count() : 0;

    // Generate meta title based on location type and content
    if ($type === 'index') {
        $title = "Browse Escorts by Location | {$siteName}";
        $description = "Find premium escorts in Uganda and East Africa. Browse by country, city, and area to discover verified companions in your preferred location.";
    } else {
        $title = $location ? "Escorts in {$location->name}" : "Browse Escorts by Location";
        if ($location && $location->parent) {
            $title .= ", {$location->parent->name}";
        }
        $title .= " | {$siteName}";

        if ($location) {
            $description = "Find {$escortCount} verified escorts in {$location->name}";
            if ($location->parent) {
                $description .= ", {$location->parent->name}";
            }
            $description .= ". Premium companions offering professional services. Browse profiles, rates, and contact information.";
        } else {
            $description = "Find premium escorts in Uganda and East Africa. Browse by country, city, and area to discover verified companions in your preferred location.";
        }
    }

    // Generate Uganda-focused keywords
    $keywords = [
        'escorts',
        'companions',
        'Uganda',
        'East Africa',
        'verified',
        'premium',
        'Kampala escorts',
        'Entebbe escorts',
        'Jinja escorts',
        'Mbarara escorts',
        'Uganda escort directory',
        'professional companions',
        'high class escorts'
    ];

    if ($location) {
        $keywords[] = $location->name;
        $keywords[] = $location->name . ' escorts';
        $keywords[] = $location->name . ' companions';

        if ($location->parent) {
            $keywords[] = $location->parent->name;
            $keywords[] = $location->parent->name . ' escorts';
        }

        // Add specific Uganda location keywords
        if ($location->name === 'Kampala') {
            $keywords = array_merge($keywords, [
                'Kampala city escorts',
                'Ntinda escorts',
                'Kololo escorts',
                'Nakasero escorts',
                'Bugolobi escorts'
            ]);
        } elseif ($location->name === 'Entebbe') {
            $keywords = array_merge($keywords, [
                'Entebbe airport escorts',
                'Kitoro escorts',
                'Katabi escorts'
            ]);
        } elseif ($location->name === 'Jinja') {
            $keywords = array_merge($keywords, [
                'Jinja city escorts',
                'Source of Nile escorts'
            ]);
        }
    }

    // Add location-specific keywords
    if ($location) {
        if ($location->type === 'country') {
            $keywords = array_merge($keywords, ['country', 'nationwide', 'East Africa', 'tourism', 'travel']);
        } elseif ($location->type === 'city') {
            $keywords = array_merge($keywords, ['city', 'urban', 'metropolitan', 'downtown', 'nightlife']);
        } elseif ($location->type === 'area') {
            $keywords = array_merge($keywords, ['area', 'district', 'neighborhood', 'local', 'community']);
        }

        // Add escort-related keywords
        $keywords = array_merge($keywords, [
            'escort services',
            'companionship',
            'adult entertainment',
            'professional escorts',
            'verified profiles',
            'booking',
            'rates',
            'contact'
        ]);
    }

    $keywordString = implode(', ', array_unique($keywords));

    // Generate canonical URL
    $canonicalUrl = $type === 'index' ? route('locations.index') : ($location ? route('locations.show', $location->slug) : route('locations.index'));
@endphp

<!-- SEO Meta Tags -->
<title>{{ $title }}</title>
<meta name="description" content="{{ $description }}">
<meta name="keywords" content="{{ $keywordString }}">
<meta name="robots" content="index, follow">
<meta name="author" content="{{ $siteName }}">

<!-- Canonical URL -->
<link rel="canonical" href="{{ $canonicalUrl }}">

<!-- Open Graph Meta Tags -->
<meta property="og:title" content="{{ $title }}">
<meta property="og:description" content="{{ $description }}">
<meta property="og:type" content="website">
<meta property="og:url" content="{{ $canonicalUrl }}">
<meta property="og:site_name" content="{{ $siteName }}">
<meta property="og:locale" content="en_UG">

<!-- Twitter Card Meta Tags -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="{{ $title }}">
<meta name="twitter:description" content="{{ $description }}">
<meta name="twitter:site" content="@{{ strtolower(str_replace(' ', '', $siteName)) }}">

<!-- Location-Specific Meta Tags -->
@if($type !== 'index' && $location)
<meta name="geo.region" content="UG">
<meta name="geo.placename" content="{{ $location->name }}">
<meta name="geo.country" content="Uganda">
@if($location->type === 'city' && $location->parent)
<meta name="geo.position" content="{{ $location->parent->name }}, {{ $location->name }}">
@endif

<!-- Additional Local SEO Meta Tags -->
<meta name="location" content="{{ $location->name }}, Uganda">
<meta name="coverage" content="Uganda">
<meta name="distribution" content="local">
<meta name="target" content="Uganda, East Africa">
@endif

<!-- Structured Data - Organization -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "{{ $siteName }}",
    "url": "{{ url('/') }}",
    "logo": "{{ asset('favicon.svg') }}",
    "description": "{{ $siteDescription }}",
    "address": {
        "@type": "PostalAddress",
        "addressCountry": "UG",
        "addressRegion": "Uganda"
    },
    "areaServed": {
        "@type": "Country",
        "name": "Uganda"
    }
}
</script>

@if($type !== 'index' && $location)
<!-- Structured Data - Place -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Place",
    "name": "{{ $location->name }}",
    "description": "{{ $description }}",
    "url": "{{ $canonicalUrl }}",
    @if($location->parent)
    "containedInPlace": {
        "@type": "Place",
        "name": "{{ $location->parent->name }}"
    },
    @endif
    "address": {
        "@type": "PostalAddress",
        "addressCountry": "UG"
        @if($location->type === 'city' && $location->parent)
        ,"addressRegion": "{{ $location->parent->name }}"
        ,"addressLocality": "{{ $location->name }}"
        @endif
    }
}
</script>

<!-- Structured Data - ItemList for Escorts -->
@if($escorts && $escorts->count() > 0)
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": "Escorts in {{ $location->name }}",
    "description": "List of verified escorts available in {{ $location->name }}",
    "numberOfItems": {{ $escorts->count() }},
    "itemListElement": [
        @foreach($escorts->take(10) as $index => $escort)
        {
            "@type": "ListItem",
            "position": {{ $index + 1 }},
            "item": {
                "@type": "Person",
                "name": "{{ $escort->stage_name }}",
                "url": "{{ route('escorts.show', $escort->slug) }}",
                "description": "Professional escort in {{ $location->name }}"
            }
        }@if(!$loop->last),@endif
        @endforeach
    ]
}
</script>
@endif
@endif

<!-- Breadcrumb Structured Data -->
@if($type !== 'index' && $location)
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
        {
            "@type": "ListItem",
            "position": 1,
            "name": "Home",
            "item": "{{ url('/') }}"
        },
        {
            "@type": "ListItem",
            "position": 2,
            "name": "Locations",
            "item": "{{ route('locations.index') }}"
        }
        @if($location->parent)
        ,{
            "@type": "ListItem",
            "position": 3,
            "name": "{{ $location->parent->name }}",
            "item": "{{ route('locations.show', $location->parent->slug) }}"
        }
        @endif
        ,{
            "@type": "ListItem",
            "position": {{ $location->parent ? 4 : 3 }},
            "name": "{{ $location->name }}",
            "item": "{{ $canonicalUrl }}"
        }
    ]
}
</script>
@endif
