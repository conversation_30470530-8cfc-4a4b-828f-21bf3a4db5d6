@props([])

@php
    $siteName = setting('site_name', 'Get Hot Babes');
    $siteDescription = setting('site_description', 'Premium escort directory in Uganda and East Africa');

    // Contact page SEO
    $title = "Contact Us - {$siteName} | Premium Escort Directory Uganda";
    $description = "Contact {$siteName} for inquiries about our premium escort directory in Uganda. Get in touch for escort registration, agency partnerships, or general questions. Serving Kampala, Entebbe, Jinja, Mbarara and all of Uganda.";

    $keywords = [
        'contact',
        'contact us',
        'Uganda escort contact',
        'escort directory contact',
        'Kampala escort contact',
        'escort registration',
        'agency registration',
        'escort services Uganda',
        'premium escorts contact',
        'verified escorts',
        'East Africa escorts',
        'escort inquiry',
        'companion services',
        'Uganda escort directory'
    ];

    $keywordString = implode(', ', array_unique($keywords));
    $canonicalUrl = route('contact.index');
@endphp

<!-- SEO Meta Tags -->
<title>{{ $title }}</title>
<meta name="description" content="{{ $description }}">
<meta name="keywords" content="{{ $keywordString }}">
<meta name="robots" content="index, follow">
<meta name="author" content="{{ $siteName }}">

<!-- Canonical URL -->
<link rel="canonical" href="{{ $canonicalUrl }}">

<!-- Open Graph Meta Tags -->
<meta property="og:title" content="{{ $title }}">
<meta property="og:description" content="{{ $description }}">
<meta property="og:type" content="website">
<meta property="og:url" content="{{ $canonicalUrl }}">
<meta property="og:site_name" content="{{ $siteName }}">
<meta property="og:locale" content="en_UG">

<!-- Twitter Card Meta Tags -->
<meta name="twitter:card" content="summary">
<meta name="twitter:title" content="{{ $title }}">
<meta name="twitter:description" content="{{ $description }}">
<meta name="twitter:site" content="@{{ strtolower(str_replace(' ', '', $siteName)) }}">

<!-- Hreflang for Regional SEO -->
<link rel="alternate" hreflang="en-ug" href="{{ $canonicalUrl }}">
<link rel="alternate" hreflang="en-ke" href="{{ $canonicalUrl }}">
<link rel="alternate" hreflang="en-tz" href="{{ $canonicalUrl }}">
<link rel="alternate" hreflang="en-rw" href="{{ $canonicalUrl }}">
<link rel="alternate" hreflang="en" href="{{ $canonicalUrl }}">

<!-- Uganda-Specific Local SEO Meta Tags -->
<meta name="geo.region" content="UG">
<meta name="geo.country" content="Uganda">
<meta name="geo.placename" content="Kampala">
<meta name="location" content="Uganda, East Africa">
<meta name="coverage" content="Uganda">
<meta name="distribution" content="local">
<meta name="target" content="Uganda, East Africa">

<!-- Structured Data - Organization -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "{{ $siteName }}",
    "url": "{{ url('/') }}",
    "logo": "{{ asset('favicon.svg') }}",
    "description": "{{ $siteDescription }}",
    "address": {
        "@type": "PostalAddress",
        "addressCountry": "UG",
        "addressRegion": "Uganda",
        "addressLocality": "Kampala"
    },
    "areaServed": [
        {
            "@type": "Country",
            "name": "Uganda"
        },
        {
            "@type": "Country",
            "name": "East Africa"
        }
    ],
    "serviceArea": {
        "@type": "Country",
        "name": "Uganda"
    },
    "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "Customer Service",
        "areaServed": "UG",
        "availableLanguage": ["English", "Luganda"]
    }
}
</script>

<!-- Structured Data - ContactPage -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "ContactPage",
    "name": "Contact {{ $siteName }}",
    "description": "{{ $description }}",
    "url": "{{ route('contact.index') }}",
    "mainEntity": {
        "@type": "Organization",
        "name": "{{ $siteName }}",
        "contactPoint": {
            "@type": "ContactPoint",
            "contactType": "Customer Service",
            "areaServed": "UG",
            "availableLanguage": ["English", "Luganda"]
        }
    }
}
</script>

<!-- Structured Data - WebPage -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Contact Us",
    "description": "{{ $description }}",
    "url": "{{ route('contact.index') }}",
    "breadcrumb": {
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "{{ url('/') }}"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "Contact",
                "item": "{{ route('contact.index') }}"
            }
        ]
    },
    "mainEntity": {
        "@type": "ContactPage",
        "name": "Contact {{ $siteName }}"
    }
}
</script>

<!-- Structured Data - Local Business -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "{{ $siteName }}",
    "description": "{{ $siteDescription }}",
    "url": "{{ url('/') }}",
    "logo": "{{ asset('favicon.svg') }}",
    "address": {
        "@type": "PostalAddress",
        "addressCountry": "UG",
        "addressRegion": "Uganda",
        "addressLocality": "Kampala"
    },
    "geo": {
        "@type": "GeoCoordinates",
        "latitude": "0.3476",
        "longitude": "32.5825"
    },
    "areaServed": [
        "Kampala",
        "Entebbe",
        "Jinja",
        "Mbarara",
        "Gulu",
        "Mbale",
        "Fort Portal",
        "Masaka",
        "Arua",
        "Lira",
        "Uganda"
    ],
    "serviceArea": {
        "@type": "Country",
        "name": "Uganda"
    },
    "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "Customer Service",
        "areaServed": "UG",
        "availableLanguage": ["English", "Luganda"]
    },
    "openingHours": "Mo-Su 00:00-23:59",
    "currenciesAccepted": "UGX, USD",
    "priceRange": "$$$"
}
</script>

<!-- FAQ Schema for Common Questions -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
        {
            "@type": "Question",
            "name": "How do I register as an escort in Uganda?",
            "acceptedAnswer": {
                "@type": "Answer",
                "text": "To register as an escort in Uganda, contact us through our contact form. We verify all profiles to ensure quality and safety for our premium directory."
            }
        },
        {
            "@type": "Question",
            "name": "Which cities in Uganda do you cover?",
            "acceptedAnswer": {
                "@type": "Answer",
                "text": "We cover all major cities in Uganda including Kampala, Entebbe, Jinja, Mbarara, Gulu, Mbale, Fort Portal, Masaka, Arua, and Lira."
            }
        },
        {
            "@type": "Question",
            "name": "How do I register my escort agency?",
            "acceptedAnswer": {
                "@type": "Answer",
                "text": "Escort agencies can register by contacting us through our contact form. We verify all agencies to maintain our premium standards."
            }
        },
        {
            "@type": "Question",
            "name": "Is the service available throughout East Africa?",
            "acceptedAnswer": {
                "@type": "Answer",
                "text": "While our primary focus is Uganda, we also serve other East African countries. Contact us for more information about coverage in your area."
            }
        }
    ]
}
</script>
