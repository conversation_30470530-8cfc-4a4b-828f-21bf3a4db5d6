@props([
    'title' => '',
    'value' => '',
    'subtitle' => '',
    'link' => '',
    'linkText' => 'View',
    'fromColor' => 'from-indigo-500',
    'toColor' => 'to-indigo-600',
    'textColor' => 'text-indigo-100',
    'icon' => null,
    'iconBgColor' => 'bg-indigo-400 bg-opacity-30'
])

<div class="bg-gradient-to-br {{ $fromColor }} {{ $toColor }} rounded-xl shadow-lg overflow-hidden relative group transform transition-all duration-300 hover:scale-105">
    <div class="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
    <div class="p-4 sm:p-6 relative z-10">
        <div class="flex items-center justify-between">
            <div>
                <p class="{{ $textColor }} text-xs sm:text-sm font-medium uppercase tracking-wider">{{ $title }}</p>
                <h3 class="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mt-1">{{ $value }}</h3>
            </div>
            @if($icon)
            <div class="{{ $iconBgColor }} rounded-full p-2 sm:p-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 sm:h-8 sm:w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    {!! $icon !!}
                </svg>
            </div>
            @endif
        </div>
        <div class="mt-4 sm:mt-6 flex justify-between items-center">
            @if($subtitle)
            <div class="{{ $textColor }} text-xs sm:text-sm">
                {!! $subtitle !!}
            </div>
            @endif
            @if($link)
            <a href="{{ $link }}" class="inline-flex items-center text-xs sm:text-sm font-medium text-white hover:{{ $textColor }} transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" class="mr-1 h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                {{ $linkText }}
                <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
            </a>
            @endif
        </div>
    </div>
</div>
