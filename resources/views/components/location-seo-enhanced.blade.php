@props(['location', 'type' => 'show', 'escortCount' => 0, 'search' => null])

@php
    // Generate location-specific SEO content
    $locationName = $location->name ?? 'Uganda';
    $parentLocation = $location->parent?->name ?? 'Uganda';
    $fullPath = $location->full_path ?? $locationName;
    
    if ($type === 'show' && $location) {
        // Individual location page SEO
        $title = "Escorts in {$locationName}, {$parentLocation} | {$escortCount}+ Verified Companions";
        $description = "Find {$escortCount}+ verified professional escorts and companions in {$locationName}, {$parentLocation}. Premium escort services, safe platform, verified profiles with photos and rates.";
        $keywords = "escorts {$locationName}, {$locationName} escorts, professional companions {$locationName}, verified escorts {$locationName}, escort services {$locationName}, {$parentLocation} escorts";
        
        // Location-specific structured data
        $structuredData = [
            '@context' => 'https://schema.org',
            '@type' => 'Place',
            'name' => $locationName,
            'address' => [
                '@type' => 'PostalAddress',
                'addressLocality' => $locationName,
                'addressRegion' => $parentLocation,
                'addressCountry' => 'Uganda'
            ],
            'geo' => [
                '@type' => 'GeoCoordinates',
                'latitude' => $location->latitude ?? null,
                'longitude' => $location->longitude ?? null
            ],
            'url' => route('locations.show', $location->slug),
            'description' => $description,
            'containedInPlace' => [
                '@type' => 'Place',
                'name' => $parentLocation
            ]
        ];
        
    } elseif ($type === 'index') {
        // Locations index page SEO
        if ($search) {
            $title = "Search Results for '{$search}' | Escort Locations in Uganda";
            $description = "Search results for escort locations matching '{$search}' in Uganda and East Africa. Find professional companions in your preferred location.";
            $keywords = "search escorts {$search}, {$search} escorts, escort locations search, Uganda escort search";
        } else {
            $title = "Browse Escorts by Location | Uganda & East Africa Directory";
            $description = "Find professional escorts by location in Uganda and East Africa. Browse escorts in Kampala, Entebbe, Jinja, Mbarara and other major cities and towns.";
            $keywords = "escort locations Uganda, escorts by city, Kampala escorts, Entebbe escorts, Jinja escorts, Mbarara escorts, East Africa escort locations, Uganda cities escorts";
        }
        
        $structuredData = [
            '@context' => 'https://schema.org',
            '@type' => 'WebPage',
            'name' => $title,
            'description' => $description,
            'url' => route('locations.index'),
            'mainEntity' => [
                '@type' => 'ItemList',
                'name' => 'Escort Locations Directory',
                'description' => 'Directory of locations where professional escort services are available'
            ]
        ];
    }
    
    // Generate canonical URL
    $canonicalUrl = $type === 'show' 
        ? route('locations.show', $location->slug)
        : route('locations.index');
        
    // Add search parameter to canonical if searching
    if ($search && $type === 'index') {
        $canonicalUrl .= '?search=' . urlencode($search);
    }
@endphp

@push('head')
    <!-- Primary SEO Meta Tags -->
    <title>{{ $title }}</title>
    <meta name="description" content="{{ $description }}">
    <meta name="keywords" content="{{ $keywords }}">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="{{ $canonicalUrl }}">
    
    <!-- Geographic Meta Tags -->
    <meta name="geo.region" content="UG">
    <meta name="geo.placename" content="{{ $locationName }}">
    <meta name="ICBM" content="{{ $location->latitude ?? '0.3476' }}, {{ $location->longitude ?? '32.5825' }}">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="{{ $title }}">
    <meta property="og:description" content="{{ $description }}">
    <meta property="og:url" content="{{ $canonicalUrl }}">
    <meta property="og:site_name" content="{{ config('app.name') }}">
    <meta property="og:locale" content="en_UG">
    <meta property="og:image" content="{{ asset('images/location-og-' . Str::slug($locationName) . '.jpg') }}">
    <meta property="og:image:alt" content="Escorts in {{ $locationName }}, {{ $parentLocation }}">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ $title }}">
    <meta name="twitter:description" content="{{ $description }}">
    <meta name="twitter:image" content="{{ asset('images/location-twitter-' . Str::slug($locationName) . '.jpg') }}">
    
    <!-- Location-specific hreflang -->
    <link rel="alternate" hreflang="en-ug" href="{{ $canonicalUrl }}">
    <link rel="alternate" hreflang="en" href="{{ $canonicalUrl }}">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
        {!! json_encode($structuredData, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) !!}
    </script>
    
    @if($type === 'show' && $location)
        <!-- Additional Location Structured Data -->
        <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "LocalBusiness",
            "name": "Professional Escort Services in {{ $locationName }}",
            "description": "{{ $description }}",
            "address": {
                "@type": "PostalAddress",
                "addressLocality": "{{ $locationName }}",
                "addressRegion": "{{ $parentLocation }}",
                "addressCountry": "Uganda"
            },
            "geo": {
                "@type": "GeoCoordinates",
                "latitude": {{ $location->latitude ?? '0.3476' }},
                "longitude": {{ $location->longitude ?? '32.5825' }}
            },
            "url": "{{ route('locations.show', $location->slug) }}",
            "areaServed": {
                "@type": "Place",
                "name": "{{ $locationName }}"
            },
            "serviceType": "Professional Companion Services",
            "priceRange": "$$-$$$"
        }
        </script>
        
        <!-- Breadcrumb Structured Data -->
        <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "position": 1,
                    "name": "Home",
                    "item": "{{ route('home') }}"
                },
                {
                    "@type": "ListItem",
                    "position": 2,
                    "name": "Locations",
                    "item": "{{ route('locations.index') }}"
                }
                @if($location->parent)
                ,{
                    "@type": "ListItem",
                    "position": 3,
                    "name": "{{ $location->parent->name }}",
                    "item": "{{ route('locations.show', $location->parent->slug) }}"
                }
                @endif
                ,{
                    "@type": "ListItem",
                    "position": {{ $location->parent ? 4 : 3 }},
                    "name": "{{ $locationName }}",
                    "item": "{{ route('locations.show', $location->slug) }}"
                }
            ]
        }
        </script>
    @endif
    
    <!-- Preload critical resources -->
    <link rel="preload" href="{{ asset('css/app.css') }}" as="style">
    <link rel="preload" href="{{ asset('js/app.js') }}" as="script">
    
    <!-- DNS prefetch for external resources -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//www.google-analytics.com">
@endpush
