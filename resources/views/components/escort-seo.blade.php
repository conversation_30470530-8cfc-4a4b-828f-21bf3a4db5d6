@props(['escort' => null, 'type' => 'index'])

@php
    $siteName = setting('site_name', 'Get Hot Babes');
    $siteDescription = setting('site_description', 'Premium escort directory in Uganda and East Africa');

    // Generate escort-specific SEO content
    if ($type === 'show' && $escort) {
        // Individual escort page SEO
        $escortName = $escort->stage_name ?: $escort->name;
        $title = "{$escortName} - Premium Escort";

        // Add location to title if available
        if ($escort->locations->count() > 0) {
            $primaryLocation = $escort->locations->first();
            $title .= " in {$primaryLocation->name}";
            if ($primaryLocation->parent && $primaryLocation->parent->name !== 'Uganda') {
                $title .= ", {$primaryLocation->parent->name}";
            }
        }
        $title .= " | {$siteName}";

        // Generate description
        $description = "Meet {$escortName}, a premium escort";
        if ($escort->locations->count() > 0) {
            $primaryLocation = $escort->locations->first();
            $description .= " in {$primaryLocation->name}, Uganda";
        }
        $description .= ". Professional companion offering exclusive services. Age {$escort->age}, {$escort->gender}. View profile, rates, and contact information.";

        // Generate keywords
        $keywords = [
            $escortName,
            'escort',
            'companion',
            'Uganda',
            'premium',
            'verified',
            $escort->gender,
            'professional'
        ];

        // Add location-based keywords
        foreach ($escort->locations as $location) {
            $keywords[] = $location->name;
            if ($location->parent) {
                $keywords[] = $location->parent->name;
            }
        }

        // Add service-based keywords
        if ($escort->services) {
            foreach ($escort->services->take(5) as $service) {
                $keywords[] = $service->name;
            }
        }

        $canonicalUrl = route('escorts.show', $escort->slug);

    } else {
        // Escorts index page SEO
        $title = "Premium Escorts in Uganda | {$siteName}";
        $description = "Find verified premium escorts in Uganda. Browse profiles of professional companions in Kampala, Entebbe, Jinja, Mbarara and other major cities. Exclusive escort directory for Uganda and East Africa.";

        $keywords = [
            'escorts Uganda',
            'Kampala escorts',
            'Entebbe escorts',
            'Jinja escorts',
            'Mbarara escorts',
            'Uganda companions',
            'East Africa escorts',
            'premium escorts',
            'verified escorts',
            'professional companions',
            'escort directory Uganda',
            'high class escorts',
            'elite companions',
            'Uganda escort services'
        ];

        $canonicalUrl = route('escorts.index');
    }

    $keywordString = implode(', ', array_unique($keywords));
@endphp

<!-- SEO Meta Tags -->
<title>{{ $title }}</title>
<meta name="description" content="{{ $description }}">
<meta name="keywords" content="{{ $keywordString }}">
<meta name="robots" content="index, follow">
<meta name="author" content="{{ $siteName }}">

<!-- Canonical URL -->
<link rel="canonical" href="{{ $canonicalUrl }}">

<!-- Open Graph Meta Tags -->
<meta property="og:title" content="{{ $title }}">
<meta property="og:description" content="{{ $description }}">
<meta property="og:type" content="website">
<meta property="og:url" content="{{ $canonicalUrl }}">
<meta property="og:site_name" content="{{ $siteName }}">
<meta property="og:locale" content="en_UG">

@if($type === 'show' && $escort && $escort->images->count() > 0)
<meta property="og:image" content="{{ asset('storage/' . $escort->images->first()->image_path) }}">
<meta property="og:image:width" content="1200">
<meta property="og:image:height" content="630">
<meta property="og:image:alt" content="{{ $escort->stage_name }} - Premium Escort in Uganda">
@endif

<!-- Twitter Card Meta Tags -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="{{ $title }}">
<meta name="twitter:description" content="{{ $description }}">
<meta name="twitter:site" content="@{{ strtolower(str_replace(' ', '', $siteName)) }}">

<!-- Hreflang for Regional SEO -->
<link rel="alternate" hreflang="en-ug" href="{{ $canonicalUrl }}">
<link rel="alternate" hreflang="en-ke" href="{{ $canonicalUrl }}">
<link rel="alternate" hreflang="en-tz" href="{{ $canonicalUrl }}">
<link rel="alternate" hreflang="en-rw" href="{{ $canonicalUrl }}">
<link rel="alternate" hreflang="en" href="{{ $canonicalUrl }}">

@if($type === 'show' && $escort && $escort->images->count() > 0)
<meta name="twitter:image" content="{{ asset('storage/' . $escort->images->first()->image_path) }}">
<meta name="twitter:image:alt" content="{{ $escort->stage_name }} - Premium Escort in Uganda">
@endif

<!-- Uganda-Specific Local SEO Meta Tags -->
<meta name="geo.region" content="UG">
<meta name="geo.country" content="Uganda">
<meta name="location" content="Uganda, East Africa">
<meta name="coverage" content="Uganda">
<meta name="distribution" content="local">
<meta name="target" content="Uganda, East Africa">

@if($type === 'show' && $escort && $escort->locations->count() > 0)
    @php $primaryLocation = $escort->locations->first(); @endphp
    <meta name="geo.placename" content="{{ $primaryLocation->name }}">
    @if($primaryLocation->type === 'area' && $primaryLocation->parent)
        <meta name="geo.position" content="{{ $primaryLocation->parent->name }}, {{ $primaryLocation->name }}">
    @endif
@endif

<!-- Structured Data - Organization -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "{{ $siteName }}",
    "url": "{{ url('/') }}",
    "logo": "{{ asset('favicon.svg') }}",
    "description": "{{ $siteDescription }}",
    "address": {
        "@type": "PostalAddress",
        "addressCountry": "UG",
        "addressRegion": "Uganda"
    },
    "areaServed": [
        {
            "@type": "Country",
            "name": "Uganda"
        },
        {
            "@type": "Country",
            "name": "East Africa"
        }
    ],
    "serviceArea": {
        "@type": "Country",
        "name": "Uganda"
    }
}
</script>

@if($type === 'show' && $escort)
<!-- Structured Data - Person (Escort Profile) -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Person",
    "name": "{{ $escort->stage_name }}",
    "url": "{{ route('escorts.show', $escort->slug) }}",
    "description": "{{ $description }}",
    "gender": "{{ ucfirst($escort->gender) }}",
    @if($escort->images->count() > 0)
    "image": "{{ asset('storage/' . $escort->images->first()->image_path) }}",
    @endif
    @if($escort->locations->count() > 0)
    "address": {
        "@type": "PostalAddress",
        "addressLocality": "{{ $escort->locations->first()->name }}",
        @if($escort->locations->first()->parent)
        "addressRegion": "{{ $escort->locations->first()->parent->name }}",
        @endif
        "addressCountry": "UG"
    },
    @endif
    "nationality": "Uganda",
    "worksFor": {
        "@type": "Organization",
        "name": "{{ $siteName }}"
    }
}
</script>

<!-- Structured Data - Service -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Premium Escort Services by {{ $escort->stage_name }}",
    "description": "Professional companion services in Uganda",
    "provider": {
        "@type": "Person",
        "name": "{{ $escort->stage_name }}"
    },
    "areaServed": {
        "@type": "Country",
        "name": "Uganda"
    },
    @if($escort->locations->count() > 0)
    "availableAtOrFrom": {
        "@type": "Place",
        "name": "{{ $escort->locations->first()->name }}, Uganda"
    },
    @endif
    "serviceType": "Companion Services"
}
</script>

@else
<!-- Structured Data - ItemList for Escorts Index -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": "Premium Escorts in Uganda",
    "description": "Directory of verified premium escorts available in Uganda and East Africa",
    "url": "{{ route('escorts.index') }}",
    "numberOfItems": "{{ isset($escorts) ? $escorts->total() : 0 }}",
    "itemListOrder": "https://schema.org/ItemListOrderDescending"
}
</script>

<!-- Structured Data - WebPage -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Premium Escorts in Uganda",
    "description": "{{ $description }}",
    "url": "{{ route('escorts.index') }}",
    "mainEntity": {
        "@type": "ItemList",
        "name": "Uganda Escort Directory",
        "description": "Comprehensive directory of premium escorts in Uganda"
    },
    "breadcrumb": {
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "{{ url('/') }}"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "Escorts",
                "item": "{{ route('escorts.index') }}"
            }
        ]
    }
}
</script>
@endif
