@props(['location', 'faqData' => null])

<div class="bg-gray-50 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
            <p class="text-lg text-gray-600">Common questions about escort services in {{ $location->name }}</p>
        </div>
        
        <div class="max-w-4xl mx-auto">
            <div class="space-y-6">
                <!-- FAQ Item 1 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 rounded-lg" 
                            onclick="toggleFaq('faq1')">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-medium text-gray-900">
                                How many escorts are available in {{ $location->name }}?
                            </h3>
                            <svg id="faq1-icon" class="h-5 w-5 text-gray-500 transform transition-transform duration-200" 
                                 fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </div>
                    </button>
                    <div id="faq1" class="hidden px-6 pb-4">
                        <p class="text-gray-600">
                            We have a comprehensive selection of verified professional escorts available in {{ $location->name }}. 
                            All profiles are verified for authenticity and safety, ensuring you connect with genuine, professional companions.
                        </p>
                    </div>
                </div>
                
                <!-- FAQ Item 2 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 rounded-lg" 
                            onclick="toggleFaq('faq2')">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-medium text-gray-900">
                                Are the escorts in {{ $location->name }} verified?
                            </h3>
                            <svg id="faq2-icon" class="h-5 w-5 text-gray-500 transform transition-transform duration-200" 
                                 fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </div>
                    </button>
                    <div id="faq2" class="hidden px-6 pb-4">
                        <p class="text-gray-600">
                            Yes, all escorts listed in {{ $location->name }} go through our strict verification process. 
                            We verify identity, authenticity, and professionalism to ensure the highest standards of safety and quality.
                        </p>
                    </div>
                </div>
                
                <!-- FAQ Item 3 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 rounded-lg" 
                            onclick="toggleFaq('faq3')">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-medium text-gray-900">
                                What services are available in {{ $location->name }}?
                            </h3>
                            <svg id="faq3-icon" class="h-5 w-5 text-gray-500 transform transition-transform duration-200" 
                                 fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </div>
                    </button>
                    <div id="faq3" class="hidden px-6 pb-4">
                        <p class="text-gray-600">
                            Professional escorts in {{ $location->name }} offer various companion services including dinner dates, 
                            social events, business functions, and professional companionship. All services are provided with 
                            discretion and professionalism.
                        </p>
                    </div>
                </div>
                
                <!-- FAQ Item 4 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 rounded-lg" 
                            onclick="toggleFaq('faq4')">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-medium text-gray-900">
                                How do I book an escort in {{ $location->name }}?
                            </h3>
                            <svg id="faq4-icon" class="h-5 w-5 text-gray-500 transform transition-transform duration-200" 
                                 fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </div>
                    </button>
                    <div id="faq4" class="hidden px-6 pb-4">
                        <p class="text-gray-600">
                            Simply browse our verified escort profiles in {{ $location->name }}, select your preferred companion, 
                            and use the contact information provided. All communication is handled directly and discreetly.
                        </p>
                    </div>
                </div>
                
                <!-- FAQ Item 5 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 rounded-lg" 
                            onclick="toggleFaq('faq5')">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-medium text-gray-900">
                                Is the platform safe and discreet?
                            </h3>
                            <svg id="faq5-icon" class="h-5 w-5 text-gray-500 transform transition-transform duration-200" 
                                 fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </div>
                    </button>
                    <div id="faq5" class="hidden px-6 pb-4">
                        <p class="text-gray-600">
                            Absolutely. We prioritize safety, privacy, and discretion. Our platform uses secure technology, 
                            all escorts are verified, and we maintain strict privacy policies to protect all users.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- FAQ Structured Data -->
@if($faqData)
<script type="application/ld+json">
{!! json_encode($faqData, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) !!}
</script>
@endif

<script>
function toggleFaq(faqId) {
    const content = document.getElementById(faqId);
    const icon = document.getElementById(faqId + '-icon');
    
    if (content.classList.contains('hidden')) {
        content.classList.remove('hidden');
        icon.style.transform = 'rotate(180deg)';
    } else {
        content.classList.add('hidden');
        icon.style.transform = 'rotate(0deg)';
    }
}
</script>
