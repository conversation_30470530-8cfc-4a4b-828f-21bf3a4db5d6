@props(['breadcrumbs' => []])

@php
    $seoService = app(\App\Services\SeoService::class);
    $structuredData = $seoService->getBreadcrumbStructuredData($breadcrumbs);
@endphp

@if(count($breadcrumbs) > 1)
<!-- Breadcrumb Navigation -->
<div class="bg-gray-50 py-3 border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <nav class="flex text-sm" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2">
                @foreach($breadcrumbs as $index => $breadcrumb)
                    <li class="flex items-center">
                        @if($index > 0)
                            <svg class="flex-shrink-0 h-4 w-4 text-gray-400 mx-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                        @endif
                        
                        @if($index === count($breadcrumbs) - 1)
                            <!-- Current page -->
                            <span class="text-gray-900 font-medium" aria-current="page">
                                {{ $breadcrumb['name'] }}
                            </span>
                        @else
                            <!-- Link to previous pages -->
                            <a href="{{ $breadcrumb['url'] }}" class="text-gray-500 hover:text-gray-700 transition-colors duration-200">
                                {{ $breadcrumb['name'] }}
                            </a>
                        @endif
                    </li>
                @endforeach
            </ol>
        </nav>
    </div>
</div>

<!-- Breadcrumb Structured Data -->
<script type="application/ld+json">
{!! json_encode($structuredData, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) !!}
</script>
@endif
