@props(['type' => 'escort', 'location' => null, 'escort' => null, 'agency' => null])

@php
    $links = [];
    
    switch($type) {
        case 'escort':
            if ($escort && $location) {
                $links = [
                    [
                        'title' => "More Escorts in {$location->name}",
                        'url' => route('locations.show', $location->slug),
                        'description' => "Browse other verified escorts in {$location->name}"
                    ],
                    [
                        'title' => "Escort Services in {$location->name}",
                        'url' => route('locations.show', $location->slug) . '#services',
                        'description' => "Popular escort services available in {$location->name}"
                    ],
                    [
                        'title' => "All Locations",
                        'url' => route('locations.index'),
                        'description' => "Find escorts in other cities across Uganda"
                    ]
                ];
                
                if ($escort->agency) {
                    $links[] = [
                        'title' => "More from {$escort->agency->name}",
                        'url' => route('agencies.show', $escort->agency->slug),
                        'description' => "Other escorts from this verified agency"
                    ];
                }
            }
            break;
            
        case 'location':
            if ($location) {
                $links = [
                    [
                        'title' => "All Escort Locations",
                        'url' => route('locations.index'),
                        'description' => "Browse escorts in all cities and regions"
                    ],
                    [
                        'title' => "Featured Escorts",
                        'url' => route('escorts.index') . '?featured=1',
                        'description' => "Premium verified escorts across Uganda"
                    ],
                    [
                        'title' => "Escort Agencies",
                        'url' => route('agencies.index'),
                        'description' => "Professional escort agencies in Uganda"
                    ]
                ];
                
                // Add nearby locations if available
                if ($location->parent) {
                    $links[] = [
                        'title' => "Escorts in {$location->parent->name}",
                        'url' => route('locations.show', $location->parent->slug),
                        'description' => "Find escorts in the broader {$location->parent->name} region"
                    ];
                }
            }
            break;
            
        case 'agency':
            if ($agency && $location) {
                $links = [
                    [
                        'title' => "All Escort Agencies",
                        'url' => route('agencies.index'),
                        'description' => "Browse all verified escort agencies"
                    ],
                    [
                        'title' => "Escorts in {$location->name}",
                        'url' => route('locations.show', $location->slug),
                        'description' => "Independent escorts in {$location->name}"
                    ],
                    [
                        'title' => "Premium Agencies",
                        'url' => route('agencies.index') . '?premium=1',
                        'description' => "Top-rated premium escort agencies"
                    ]
                ];
            }
            break;
    }
@endphp

@if(count($links) > 0)
<div class="bg-gradient-to-r from-gray-50 to-gray-100 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Explore More</h2>
            <p class="text-gray-600">Discover related escort services and locations</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($links as $link)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-300">
                    <div class="p-6">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 bg-pink-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-5 h-5 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4 flex-1">
                                <h3 class="text-lg font-medium text-gray-900 mb-2">
                                    <a href="{{ $link['url'] }}" class="hover:text-pink-600 transition-colors duration-200">
                                        {{ $link['title'] }}
                                    </a>
                                </h3>
                                <p class="text-sm text-gray-600 mb-3">{{ $link['description'] }}</p>
                                <a href="{{ $link['url'] }}" 
                                   class="inline-flex items-center text-sm font-medium text-pink-600 hover:text-pink-700 transition-colors duration-200">
                                    Learn more
                                    <svg class="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
        
        <!-- Call to Action -->
        <div class="text-center mt-12">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
                <h3 class="text-xl font-bold text-gray-900 mb-4">Looking for Something Specific?</h3>
                <p class="text-gray-600 mb-6">
                    Can't find what you're looking for? Browse our complete directory of verified escorts and agencies.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('escorts.index') }}" 
                       class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-pink-600 hover:bg-pink-700 transition-colors duration-200">
                        Browse All Escorts
                    </a>
                    <a href="{{ route('locations.index') }}" 
                       class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                        View All Locations
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endif
