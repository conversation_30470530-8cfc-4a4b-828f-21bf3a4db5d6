@props(['page' => 'general', 'entity' => null])

@php
    $analyticsService = app(\App\Services\AnalyticsService::class);
@endphp

<!-- Google Analytics 4 -->
{!! $analyticsService->getGA4TrackingCode() !!}

<!-- Google Search Console Verification -->
{!! $analyticsService->getGSCVerificationTag() !!}

<!-- Facebook Pixel -->
{!! $analyticsService->getFacebookPixelCode() !!}

<!-- Hotjar Heat Maps -->
{!! $analyticsService->getHeatMapCode() !!}

<!-- Page-specific tracking -->
@if($page === 'escort' && $entity)
    {!! $analyticsService->trackEscortView($entity) !!}
@elseif($page === 'agency' && $entity)
    {!! $analyticsService->trackAgencyView($entity) !!}
@elseif($page === 'location' && $entity)
    {!! $analyticsService->trackLocationView($entity, $entity->escorts()->count()) !!}
@endif

<!-- Enhanced E-commerce Tracking -->
<script>
// Track user engagement
function trackEngagement(action, category, label, value) {
    if (typeof gtag !== 'undefined') {
        gtag('event', action, {
            event_category: category,
            event_label: label,
            value: value || 1
        });
    }
}

// Track scroll depth
let scrollDepthTracked = false;
window.addEventListener('scroll', function() {
    const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
    
    if (scrollPercent >= 75 && !scrollDepthTracked) {
        trackEngagement('scroll_depth', 'engagement', '75_percent', 75);
        scrollDepthTracked = true;
    }
});

// Track time on page
let timeOnPage = 0;
setInterval(function() {
    timeOnPage += 10;
    
    // Track milestones
    if (timeOnPage === 30) {
        trackEngagement('time_on_page', 'engagement', '30_seconds', 30);
    } else if (timeOnPage === 60) {
        trackEngagement('time_on_page', 'engagement', '1_minute', 60);
    } else if (timeOnPage === 180) {
        trackEngagement('time_on_page', 'engagement', '3_minutes', 180);
    }
}, 10000);

// Track contact attempts
document.addEventListener('click', function(e) {
    const target = e.target.closest('a[href]');
    if (!target) return;
    
    const href = target.getAttribute('href');
    
    // Track phone calls
    if (href && href.startsWith('tel:')) {
        trackEngagement('contact_attempt', 'conversion', 'phone_call', 10);
        
        // Facebook Pixel conversion
        if (typeof fbq !== 'undefined') {
            fbq('track', 'Contact', {
                content_category: 'phone_call',
                value: 10,
                currency: 'UGX'
            });
        }
    }
    
    // Track WhatsApp clicks
    if (href && href.includes('wa.me')) {
        trackEngagement('contact_attempt', 'conversion', 'whatsapp', 10);
        
        if (typeof fbq !== 'undefined') {
            fbq('track', 'Contact', {
                content_category: 'whatsapp',
                value: 10,
                currency: 'UGX'
            });
        }
    }
    
    // Track email clicks
    if (href && href.startsWith('mailto:')) {
        trackEngagement('contact_attempt', 'conversion', 'email', 5);
    }
    
    // Track external links
    if (target.hostname !== window.location.hostname) {
        trackEngagement('external_link', 'navigation', target.hostname, 1);
    }
});

// Track search usage
const searchForms = document.querySelectorAll('form[action*="search"], form input[name*="search"]');
searchForms.forEach(form => {
    form.addEventListener('submit', function(e) {
        const searchInput = form.querySelector('input[type="search"], input[name*="search"]');
        if (searchInput && searchInput.value.trim()) {
            trackEngagement('search', 'engagement', searchInput.value.trim(), 1);
        }
    });
});

// Track filter usage
const filterForms = document.querySelectorAll('form[action*="filter"], .filter-form');
filterForms.forEach(form => {
    form.addEventListener('submit', function(e) {
        const formData = new FormData(form);
        const filters = [];
        
        for (let [key, value] of formData.entries()) {
            if (value && value !== '') {
                filters.push(`${key}:${value}`);
            }
        }
        
        if (filters.length > 0) {
            trackEngagement('filter_use', 'engagement', filters.join(','), filters.length);
        }
    });
});

// Track image interactions
document.addEventListener('click', function(e) {
    if (e.target.tagName === 'IMG' || e.target.closest('.image-gallery')) {
        trackEngagement('image_interaction', 'engagement', 'image_click', 1);
    }
});

// Track video interactions (if any)
document.addEventListener('play', function(e) {
    if (e.target.tagName === 'VIDEO') {
        trackEngagement('video_play', 'engagement', 'video_start', 1);
    }
}, true);

// Track form abandonment
const forms = document.querySelectorAll('form');
forms.forEach(form => {
    let formStarted = false;
    let formCompleted = false;
    
    form.addEventListener('input', function() {
        if (!formStarted) {
            formStarted = true;
            trackEngagement('form_start', 'engagement', form.action || 'unknown_form', 1);
        }
    });
    
    form.addEventListener('submit', function() {
        formCompleted = true;
        trackEngagement('form_complete', 'conversion', form.action || 'unknown_form', 5);
    });
    
    window.addEventListener('beforeunload', function() {
        if (formStarted && !formCompleted) {
            trackEngagement('form_abandon', 'engagement', form.action || 'unknown_form', 1);
        }
    });
});

// Track page visibility changes
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        trackEngagement('page_hidden', 'engagement', 'tab_switch', 1);
    } else {
        trackEngagement('page_visible', 'engagement', 'tab_return', 1);
    }
});

// Track error events
window.addEventListener('error', function(e) {
    trackEngagement('javascript_error', 'error', e.message, 1);
});

// Track performance metrics
window.addEventListener('load', function() {
    setTimeout(function() {
        const navigation = performance.getEntriesByType('navigation')[0];
        if (navigation) {
            const loadTime = navigation.loadEventEnd - navigation.fetchStart;
            trackEngagement('page_load_time', 'performance', Math.round(loadTime), Math.round(loadTime));
            
            // Track specific performance metrics
            if (navigation.domContentLoadedEventEnd) {
                const domReady = navigation.domContentLoadedEventEnd - navigation.fetchStart;
                trackEngagement('dom_ready_time', 'performance', Math.round(domReady), Math.round(domReady));
            }
        }
    }, 1000);
});
</script>

<!-- Conversion Tracking Setup -->
<script>
// Set up conversion tracking for key actions
function trackConversion(type, value) {
    // Google Analytics conversion
    if (typeof gtag !== 'undefined') {
        gtag('event', 'conversion', {
            send_to: '{{ env("GOOGLE_ANALYTICS_ID") }}',
            event_category: 'conversion',
            event_label: type,
            value: value || 1
        });
    }
    
    // Facebook Pixel conversion
    if (typeof fbq !== 'undefined') {
        fbq('track', 'Lead', {
            content_category: type,
            value: value || 1,
            currency: 'UGX'
        });
    }
}

// Make conversion tracking available globally
window.trackConversion = trackConversion;
window.trackEngagement = trackEngagement;
</script>

<!-- User Identification for Analytics -->
@auth
<script>
// Set user properties for logged-in users
if (typeof gtag !== 'undefined') {
    gtag('config', '{{ env("GOOGLE_ANALYTICS_ID") }}', {
        user_id: '{{ auth()->id() }}',
        custom_map: {
            'user_type': '{{ auth()->user()->user_type }}',
            'registration_date': '{{ auth()->user()->created_at->format("Y-m-d") }}'
        }
    });
}
</script>
@endauth
