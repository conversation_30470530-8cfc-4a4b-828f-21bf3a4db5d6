@props(['criticalCss' => true, 'preloadResources' => true, 'dnsPrefetch' => true])

@php
    $performanceService = app(\App\Services\PerformanceService::class);
@endphp

@if($criticalCss)
<!-- Critical CSS -->
<style>
{!! $performanceService->getCriticalCss() !!}
</style>
@endif

@if($preloadResources)
<!-- Preload Critical Resources -->
@foreach($performanceService->getCriticalResources() as $resource)
<link rel="{{ $resource['rel'] }}" href="{{ $resource['href'] }}" as="{{ $resource['as'] }}">
@endforeach
@endif

@if($dnsPrefetch)
<!-- DNS Prefetch -->
@foreach($performanceService->getDnsPrefetchHints() as $domain)
<link rel="dns-prefetch" href="{{ $domain }}">
@endforeach

<!-- Preconnect -->
@foreach($performanceService->getPreconnectHints() as $domain)
<link rel="preconnect" href="{{ $domain }}" crossorigin>
@endforeach
@endif

<!-- Resource Hints for Performance -->
<link rel="prefetch" href="{{ route('escorts.index') }}">
<link rel="prefetch" href="{{ route('agencies.index') }}">
<link rel="prefetch" href="{{ route('locations.index') }}">

<!-- Service Worker Registration -->
<script>
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
                console.log('SW registered: ', registration);
            })
            .catch(function(registrationError) {
                console.log('SW registration failed: ', registrationError);
            });
    });
}
</script>

<!-- Performance Monitoring -->
<script>
// Monitor Core Web Vitals
function sendToAnalytics(metric) {
    if (typeof gtag !== 'undefined') {
        gtag('event', metric.name, {
            event_category: 'Web Vitals',
            event_label: metric.id,
            value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
            non_interaction: true,
        });
    }
}

// Load web-vitals library dynamically
const script = document.createElement('script');
script.src = 'https://unpkg.com/web-vitals@3/dist/web-vitals.js';
script.type = 'module';
script.onload = function() {
    import('https://unpkg.com/web-vitals@3/dist/web-vitals.js').then(({getCLS, getFID, getFCP, getLCP, getTTFB}) => {
        getCLS(sendToAnalytics);
        getFID(sendToAnalytics);
        getFCP(sendToAnalytics);
        getLCP(sendToAnalytics);
        getTTFB(sendToAnalytics);
    });
};
document.head.appendChild(script);

// Image lazy loading fallback for older browsers
if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
    });
}

// Preload next page on hover (for better perceived performance)
let preloadedPages = new Set();
document.addEventListener('mouseover', function(e) {
    const link = e.target.closest('a[href]');
    if (link && link.hostname === window.location.hostname && !preloadedPages.has(link.href)) {
        const linkElement = document.createElement('link');
        linkElement.rel = 'prefetch';
        linkElement.href = link.href;
        document.head.appendChild(linkElement);
        preloadedPages.add(link.href);
    }
});

// Optimize font loading
if ('fonts' in document) {
    document.fonts.ready.then(function() {
        document.body.classList.add('fonts-loaded');
    });
}
</script>

<!-- Optimize third-party scripts loading -->
<script>
// Delay non-critical scripts until user interaction
let userInteracted = false;
const delayedScripts = [];

function loadDelayedScripts() {
    if (userInteracted) return;
    userInteracted = true;
    
    delayedScripts.forEach(script => {
        const scriptElement = document.createElement('script');
        scriptElement.src = script.src;
        scriptElement.async = true;
        if (script.onload) scriptElement.onload = script.onload;
        document.head.appendChild(scriptElement);
    });
}

// Load scripts on first user interaction
['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'].forEach(event => {
    window.addEventListener(event, loadDelayedScripts, { once: true, passive: true });
});

// Fallback: load after 5 seconds
setTimeout(loadDelayedScripts, 5000);
</script>

<!-- Critical CSS loading optimization -->
<script>
// Load non-critical CSS asynchronously
function loadCSS(href) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    link.media = 'print';
    link.onload = function() {
        this.media = 'all';
    };
    document.head.appendChild(link);
}

// Load non-critical stylesheets
window.addEventListener('load', function() {
    // Add any non-critical CSS files here
    // loadCSS('/css/non-critical.css');
});
</script>

<!-- Image optimization hints -->
<style>
/* Optimize image loading */
img {
    max-width: 100%;
    height: auto;
}

img.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

img.lazy.loaded {
    opacity: 1;
}

/* Optimize layout shifts */
.escort-card img,
.agency-logo img {
    aspect-ratio: 1 / 1;
    object-fit: cover;
}

/* Optimize font loading */
body {
    font-display: swap;
}

/* Reduce layout shifts for dynamic content */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}
</style>
