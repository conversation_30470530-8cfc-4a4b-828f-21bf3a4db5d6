@props([
    'id' => 'confirmation-modal',
    'title' => 'Confirm Action',
    'message' => 'Are you sure you want to perform this action?',
    'confirmButtonText' => 'Confirm',
    'cancelButtonText' => 'Cancel',
    'confirmButtonClass' => 'bg-red-600 hover:bg-red-700 focus:ring-red-500',
    'formAction' => '',
    'formMethod' => 'POST',
    'formId' => 'confirmation-form'
])

<div
    x-data="{
        show: false,
        init() {
            console.log('Confirmation modal initialized with ID: {{ $id }}');
        }
    }"
    x-on:open-confirmation-modal.window="
        console.log('Received open-confirmation-modal event:', $event.detail);
        if ($event.detail.id === '{{ $id }}') {
            console.log('Opening modal with ID: {{ $id }}');
            show = true;
            $nextTick(() => {
                if ($refs.cancelButton) {
                    $refs.cancelButton.focus();
                } else {
                    console.error('Cancel button reference not found');
                }
            });
            if ($event.detail.formAction) {
                if ($refs.confirmationForm) {
                    $refs.confirmationForm.action = $event.detail.formAction;
                    console.log('Updated form action to:', $event.detail.formAction);
                } else {
                    console.error('Form reference not found');
                }
            }
            if ($event.detail.formMethod) {
                if ($refs.confirmationForm) {
                    // Handle method spoofing for non-POST methods
                    if ($event.detail.formMethod !== 'POST' && $event.detail.formMethod !== 'GET') {
                        // Check if method input already exists
                        let methodInput = $refs.confirmationForm.querySelector('input[name=_method]');
                        if (!methodInput) {
                            methodInput = document.createElement('input');
                            methodInput.type = 'hidden';
                            methodInput.name = '_method';
                            $refs.confirmationForm.appendChild(methodInput);
                        }
                        methodInput.value = $event.detail.formMethod;
                        console.log('Updated form method to:', $event.detail.formMethod);
                    }
                } else {
                    console.error('Form reference not found');
                }
            }
            if ($event.detail.message) {
                if ($refs.modalMessage) {
                    $refs.modalMessage.textContent = $event.detail.message;
                    console.log('Updated message to:', $event.detail.message);
                } else {
                    console.error('Message reference not found');
                }
            }
        }
    "
    x-on:close-confirmation-modal.window="if ($event.detail.id === '{{ $id }}') { show = false }"
    x-on:keydown.escape.window="show = false"
    x-show="show"
    class="fixed inset-0 overflow-y-auto px-4 py-6 sm:px-0 z-50"
    style="display: none;"
>
    <div
        x-show="show"
        class="fixed inset-0 transform transition-all"
        x-on:click="show = false"
        x-transition:enter="ease-out duration-300"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="ease-in duration-200"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
    >
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
    </div>

    <div
        x-show="show"
        class="mb-6 bg-white rounded-lg overflow-hidden shadow-xl transform transition-all sm:w-full sm:max-w-md sm:mx-auto"
        x-transition:enter="ease-out duration-300"
        x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
        x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
        x-transition:leave="ease-in duration-200"
        x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
        x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
    >
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
                <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                </div>
                <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">
                        {{ $title }}
                    </h3>
                    <div class="mt-2">
                        <p class="text-sm text-gray-500" x-ref="modalMessage">
                            {{ $message }}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <form x-ref="confirmationForm" id="{{ $formId }}" action="{{ $formAction }}" method="POST" class="confirmation-form">
            @csrf
            <!-- Method spoofing is handled dynamically via JavaScript -->

            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                    type="button"
                    class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 {{ $confirmButtonClass }} text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm"
                    x-on:click="
                        console.log('Confirm button clicked');
                        if ($refs.confirmationForm) {
                            console.log('Submitting form:', $refs.confirmationForm);
                            $refs.confirmationForm.submit();
                        } else {
                            console.error('Form reference not found');
                        }
                        show = false;
                    "
                >
                    {{ $confirmButtonText }}
                </button>
                <button
                    type="button"
                    class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                    x-on:click="show = false"
                    x-ref="cancelButton"
                >
                    {{ $cancelButtonText }}
                </button>
            </div>
        </form>
    </div>
</div>
