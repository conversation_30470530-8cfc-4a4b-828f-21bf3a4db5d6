@props([
    'title' => '',
    'value' => '',
    'badgeText' => '',
    'link' => '',
    'fromColor' => 'from-emerald-50',
    'toColor' => 'to-teal-50',
    'borderColor' => 'border-emerald-100',
    'textColor' => 'text-emerald-900',
    'badgeBgColor' => 'bg-emerald-100',
    'badgeTextColor' => 'text-emerald-600',
    'linkColor' => 'text-emerald-600',
    'linkHoverColor' => 'text-emerald-900',
    'iconBgColor' => 'bg-emerald-100',
    'iconColor' => 'text-emerald-600',
    'icon' => null
])

<div class="bg-gradient-to-r {{ $fromColor }} {{ $toColor }} p-3 sm:p-4 rounded-xl border {{ $borderColor }} transform transition-transform duration-300 hover:scale-105">
    <div class="flex items-center mb-2">
        <div class="{{ $iconBgColor }} p-1.5 sm:p-2 rounded-lg mr-2 sm:mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 {{ $iconColor }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {!! $icon !!}
            </svg>
        </div>
        <h4 class="text-xs sm:text-sm font-medium {{ $textColor }}">{{ $title }}</h4>
    </div>
    <p class="text-xl sm:text-2xl font-bold {{ $textColor }}">
        {{ $value }}
    </p>
    <div class="mt-2 flex justify-between items-center">
        <span class="text-xs {{ $badgeTextColor }} {{ $badgeBgColor }} px-2 py-1 rounded-full">{{ $badgeText }}</span>
        @if($link)
        <a href="{{ $link }}" class="text-xs sm:text-sm font-medium {{ $linkColor }} hover:{{ $linkHoverColor }} flex items-center">
            Details
            <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
        </a>
        @endif
    </div>
</div>
