@props(['title' => '', 'subtitle' => '', 'icon' => null, 'iconBg' => 'bg-indigo-50', 'iconColor' => 'text-indigo-500'])

<div {{ $attributes->merge(['class' => 'bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100']) }}>
    @if($title)
    <div class="border-b border-gray-100">
        <div class="p-3 sm:p-5 flex justify-between items-center">
            <div>
                <h3 class="text-base sm:text-lg font-bold text-gray-900">{{ $title }}</h3>
                @if($subtitle)
                <p class="text-xs sm:text-sm text-gray-500">{{ $subtitle }}</p>
                @endif
            </div>
            @if($icon)
            <div class="{{ $iconBg }} p-1.5 sm:p-2 rounded-lg">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 {{ $iconColor }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    {!! $icon !!}
                </svg>
            </div>
            @endif
        </div>
    </div>
    @endif
    <div class="p-3 sm:p-5">
        {{ $slot }}
    </div>
</div>
