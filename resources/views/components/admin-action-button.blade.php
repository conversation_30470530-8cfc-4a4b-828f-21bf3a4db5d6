@props([
    'title' => '',
    'subtitle' => '',
    'link' => '#',
    'fromColor' => 'from-pink-500',
    'toColor' => 'to-rose-500',
    'icon' => null
])

<a href="{{ $link }}" {{ $attributes->merge(['class' => 'group relative overflow-hidden bg-gradient-to-br ' . $fromColor . ' ' . $toColor . ' rounded-xl shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1']) }}>
    <div class="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
    <div class="p-3 sm:p-5 flex items-center">
        <div class="flex-shrink-0 bg-white bg-opacity-20 rounded-full p-2 sm:p-3 mr-2 sm:mr-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-6 sm:w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {!! $icon !!}
            </svg>
        </div>
        <div>
            <h4 class="text-sm font-bold text-white">{{ $title }}</h4>
            <p class="text-xs text-white text-opacity-80">{{ $subtitle }}</p>
        </div>
    </div>
</a>
