/**
 * Confirmation Modal Utility
 *
 * This utility provides functions to handle confirmation modals
 * instead of using browser's native confirm() dialog.
 */

console.log('Confirmation.js loaded');

window.confirmAction = function(options = {}) {
    console.log('confirmAction called with options:', options);

    const defaults = {
        id: 'confirmation-modal',
        message: 'Are you sure you want to perform this action?',
        formAction: null,
        formMethod: 'POST'
    };

    const settings = { ...defaults, ...options };

    // Dispatch the event to open the modal
    console.log('Dispatching open-confirmation-modal event with:', settings);
    window.dispatchEvent(new CustomEvent('open-confirmation-modal', {
        detail: settings
    }));

    // Prevent the default form submission
    return false;
};

// Function to set up confirmation handlers
function setupConfirmationHandlers() {
    console.log('Setting up confirmation handlers');

    // Find all elements with data-confirm attribute
    const confirmButtons = document.querySelectorAll('[data-confirm]');
    console.log('Found confirm buttons:', confirmButtons.length);

    confirmButtons.forEach(element => {
        console.log('Adding click handler to:', element);

        // Remove any existing click handlers first
        element.removeEventListener('click', handleConfirmClick);

        // Add the click handler
        element.addEventListener('click', handleConfirmClick);
    });

    // Manual form submission after confirmation
    document.querySelectorAll('.confirmation-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            console.log('Confirmation form submitted:', this);
        });
    });
}

// Handler function for confirmation clicks
function handleConfirmClick(e) {
    console.log('Confirm button clicked:', this);
    e.preventDefault();
    e.stopPropagation();

    const message = this.getAttribute('data-confirm');
    const form = this.closest('form');
    const formAction = form?.getAttribute('action') || null;
    const formMethod = form?.querySelector('input[name="_method"]')?.value || form?.getAttribute('method') || 'POST';
    const modalId = this.getAttribute('data-modal-id') || 'confirmation-modal';

    console.log('Confirm details:', { message, formAction, formMethod, modalId });

    confirmAction({
        id: modalId,
        message: message,
        formAction: formAction,
        formMethod: formMethod
    });

    return false;
}

// Add event listener to handle confirmation forms
document.addEventListener('DOMContentLoaded', setupConfirmationHandlers);

// Also set up handlers when Turbo or PJAX loads a new page
document.addEventListener('turbo:load', setupConfirmationHandlers);
document.addEventListener('turbo:render', setupConfirmationHandlers);

// For regular page changes, periodically check for new buttons
setInterval(setupConfirmationHandlers, 2000);
