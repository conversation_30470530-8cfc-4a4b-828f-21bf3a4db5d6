<?php

namespace Database\Seeders;

use App\Models\Escort;
use App\Models\EscortImage;
use App\Models\EscortRate;
use App\Models\Location;
use App\Models\Service;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class EscortSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all locations and services
        $locations = Location::all();
        $services = Service::all();

        // Create 20 escorts
        for ($i = 1; $i <= 20; $i++) {
            // Check if user already exists
            $username = 'escort' . $i;
            $email = 'escort' . $i . '@example.com';

            $user = User::where('username', $username)->orWhere('email', $email)->first();

            if (!$user) {
                // Create a user for the escort
                $user = User::create([
                    'username' => $username,
                    'email' => $email,
                    'password' => Hash::make('password'),
                    'user_type' => 'escort',
                ]);
            }

            // Determine gender (more females than others)
            $genders = ['female', 'female', 'female', 'female', 'male', 'male', 'transsexual', 'couple'];
            $gender = $genders[array_rand($genders)];

            // Generate a name based on gender
            $name = $this->generateName($gender);

            // Check if escort already exists for this user
            $existingEscort = Escort::where('user_id', $user->id)->first();

            if ($existingEscort) {
                continue; // Skip to next iteration
            }

            // Create the escort
            $escort = Escort::create([
                'user_id' => $user->id,
                'name' => $name,
                'gender' => $gender,
                'date_of_birth' => now()->subYears(rand(21, 40))->subMonths(rand(1, 12))->subDays(rand(1, 30)),
                'ethnicity' => $this->getRandomEthnicity(),
                'hair_color' => $this->getRandomHairColor(),
                'hair_length' => $this->getRandomHairLength(),
                'bust_size' => in_array($gender, ['female', 'transsexual']) ? $this->getRandomBustSize() : null,
                'height_cm' => rand(150, 190),
                'weight_kg' => rand(45, 85),
                'build' => $this->getRandomBuild(),
                'looks' => $this->getRandomLooks(),
                'smoker' => (bool)rand(0, 1),
                'about' => $this->generateAbout($name, $gender),
                'education' => rand(0, 1) ? $this->getRandomEducation() : null,
                'sports' => json_encode(array_slice($this->getRandomSports(), 0, rand(0, 3))),
                'hobbies' => json_encode(array_slice($this->getRandomHobbies(), 0, rand(0, 4))),
                'zodiac_sign' => rand(0, 1) ? $this->getRandomZodiacSign() : null,
                'sexual_orientation' => rand(0, 1) ? $this->getRandomSexualOrientation() : null,
                'occupation' => rand(0, 1) ? $this->getRandomOccupation() : null,
                'is_premium' => (bool)rand(0, 1),
                'is_verified' => (bool)rand(0, 1),
                'is_featured' => $i <= 5, // First 5 are featured
                'is_independent' => (bool)rand(0, 1),
                'incall_available' => (bool)rand(0, 1),
                'outcall_available' => (bool)rand(0, 1),
                'is_new' => $i > 15, // Last 5 are new
                'is_online' => (bool)rand(0, 1),
                'profile_views' => rand(10, 1000),
                'slug' => Str::slug($name . '-' . $i),
            ]);

            // Add locations (1-3 random locations)
            $locationCount = rand(1, 3);
            $randomLocations = $locations->random($locationCount);
            foreach ($randomLocations as $index => $location) {
                $escort->locations()->attach($location->id, ['is_primary' => $index === 0]);
            }

            // Add services (3-8 random services)
            $serviceCount = rand(3, 8);
            $randomServices = $services->random($serviceCount);
            foreach ($randomServices as $service) {
                $escort->services()->attach($service->id);
            }

            // Add rates
            $durations = ['1hour', '2hours', '3hours', '24hours'];
            foreach ($durations as $duration) {
                EscortRate::create([
                    'escort_id' => $escort->id,
                    'duration' => $duration,
                    'incall_price' => $escort->incall_available ? rand(150, 500) * 1000 : null, // UGX prices
                    'outcall_price' => $escort->outcall_available ? rand(200, 600) * 1000 : null, // UGX prices
                    'currency' => 'UGX',
                ]);
            }

            // Add a placeholder image
            EscortImage::create([
                'escort_id' => $escort->id,
                'image_path' => 'escorts/placeholder-' . $gender . '.jpg',
                'is_primary' => true,
                'sort_order' => 1,
            ]);
        }
    }

    /**
     * Generate a random name based on gender.
     */
    private function generateName($gender)
    {
        $femaleNames = ['Sophia', 'Emma', 'Olivia', 'Ava', 'Isabella', 'Mia', 'Amelia', 'Harper', 'Evelyn', 'Abigail', 'Emily', 'Elizabeth', 'Sofia', 'Madison', 'Scarlett', 'Victoria', 'Aria', 'Grace', 'Chloe', 'Camila', 'Penelope', 'Riley', 'Layla', 'Lillian', 'Nora', 'Zoey', 'Mila', 'Aubrey', 'Hannah', 'Lily'];

        $maleNames = ['Liam', 'Noah', 'William', 'James', 'Oliver', 'Benjamin', 'Elijah', 'Lucas', 'Mason', 'Logan', 'Alexander', 'Ethan', 'Jacob', 'Michael', 'Daniel', 'Henry', 'Jackson', 'Sebastian', 'Aiden', 'Matthew', 'Samuel', 'David', 'Joseph', 'Carter', 'Owen', 'Wyatt', 'John', 'Jack', 'Luke', 'Jayden'];

        $coupleNames = ['Alex & Emma', 'Jack & Rose', 'Noah & Olivia', 'Liam & Sophia', 'William & Ava', 'James & Isabella', 'Oliver & Mia', 'Benjamin & Charlotte', 'Elijah & Amelia', 'Lucas & Harper', 'Mason & Evelyn', 'Logan & Abigail', 'Alexander & Emily', 'Ethan & Elizabeth', 'Jacob & Sofia'];

        $transNames = ['Aria', 'Luna', 'Nova', 'Zara', 'Jade', 'Ruby', 'Ivy', 'Skye', 'Eden', 'Willow', 'Phoenix', 'River', 'Raven', 'Quinn', 'Sage'];

        switch ($gender) {
            case 'female':
                return $femaleNames[array_rand($femaleNames)];
            case 'male':
                return $maleNames[array_rand($maleNames)];
            case 'couple':
                return $coupleNames[array_rand($coupleNames)];
            case 'transsexual':
                return $transNames[array_rand($transNames)];
            default:
                return 'Unknown';
        }
    }

    /**
     * Generate a random about text.
     */
    private function generateAbout($name, $gender)
    {
        $intros = [
            "Hi, I'm $name! I'm a passionate and adventurous companion who loves to create unforgettable experiences.",
            "Welcome to my profile! I'm $name, a sophisticated and elegant companion with a playful side.",
            "I'm $name, a free-spirited and open-minded individual who enjoys meeting new people and creating special moments.",
            "Greetings! I'm $name, a charming and attentive companion who knows how to make you feel special.",
            "Hello there! I'm $name, a fun-loving and sensual companion who enjoys the finer things in life."
        ];

        $bodies = [
            "I'm well-educated, well-traveled, and always eager to learn new things. I enjoy stimulating conversations as much as I enjoy intimate moments.",
            "I have a passion for life and love to share exciting experiences. Whether it's a quiet dinner or a wild adventure, I'm always up for making memories.",
            "I pride myself on being a great listener and conversationalist. I believe that connection starts with understanding and grows with chemistry.",
            "I'm the perfect blend of sophistication and playfulness. I know when to be elegant and when to let loose and have fun.",
            "I'm naturally sensual and love to please. My goal is always to ensure you have an amazing time that exceeds your expectations."
        ];

        $closings = [
            "I can't wait to meet you and create our own special adventure together.",
            "If you're looking for a genuine connection and unforgettable experience, I'm just a message away.",
            "Don't hesitate to reach out if you think we'd click. I'm looking forward to hearing from you.",
            "Let's create some magic together. Contact me to arrange our special time.",
            "I'm excited about the possibility of meeting you soon. Until then, take care!"
        ];

        return $intros[array_rand($intros)] . " " . $bodies[array_rand($bodies)] . " " . $closings[array_rand($closings)];
    }

    private function getRandomEthnicity()
    {
        $ethnicities = ['Latin', 'Caucasian', 'Black', 'White', 'MiddleEast', 'Asian', 'Indian', 'Aborigine', 'Native American', 'Other'];
        return $ethnicities[array_rand($ethnicities)];
    }

    private function getRandomHairColor()
    {
        $hairColors = ['Black', 'Blonde', 'Brown', 'Brunette', 'Chestnut', 'Auburn', 'Dark-blonde', 'Golden', 'Red', 'Grey', 'Silver', 'White', 'Other'];
        return $hairColors[array_rand($hairColors)];
    }

    private function getRandomHairLength()
    {
        $hairLengths = ['Bald', 'Short', 'Shoulder', 'Long', 'Very Long'];
        return $hairLengths[array_rand($hairLengths)];
    }

    private function getRandomBustSize()
    {
        $bustSizes = ['Very small', 'Small(A)', 'Medium(B)', 'Large(C)', 'Very Large(D)', 'Enormous(E+)'];
        return $bustSizes[array_rand($bustSizes)];
    }

    private function getRandomBuild()
    {
        $builds = ['Skinny', 'Slim', 'Regular', 'Curvy', 'Fat'];
        return $builds[array_rand($builds)];
    }

    private function getRandomLooks()
    {
        $looks = ['Nothing Special', 'Average', 'Sexy', 'Ultra Sexy'];
        return $looks[array_rand($looks)];
    }

    private function getRandomEducation()
    {
        $education = ['High School', 'College', 'Bachelor Degree', 'Master Degree', 'PhD'];
        return $education[array_rand($education)];
    }

    private function getRandomSports()
    {
        return ['Swimming', 'Yoga', 'Running', 'Tennis', 'Volleyball', 'Basketball', 'Cycling', 'Fitness', 'Dancing', 'Pilates'];
    }

    private function getRandomHobbies()
    {
        return ['Reading', 'Cooking', 'Traveling', 'Photography', 'Painting', 'Music', 'Movies', 'Shopping', 'Fashion', 'Art'];
    }

    private function getRandomZodiacSign()
    {
        $zodiacSigns = ['Aries', 'Taurus', 'Gemini', 'Cancer', 'Leo', 'Virgo', 'Libra', 'Scorpio', 'Sagittarius', 'Capricorn', 'Aquarius', 'Pisces'];
        return $zodiacSigns[array_rand($zodiacSigns)];
    }

    private function getRandomSexualOrientation()
    {
        $orientations = ['Straight', 'Bisexual', 'Gay', 'Lesbian', 'Pansexual'];
        return $orientations[array_rand($orientations)];
    }

    private function getRandomOccupation()
    {
        $occupations = ['Student', 'Model', 'Dancer', 'Actress', 'Fitness Trainer', 'Massage Therapist', 'Yoga Instructor', 'Entrepreneur', 'Artist', 'Musician'];
        return $occupations[array_rand($occupations)];
    }
}
