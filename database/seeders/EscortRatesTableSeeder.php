<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class EscortRatesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all escort IDs
        $escortIds = DB::table('escorts')->pluck('id')->toArray();

        // Define durations and price ranges
        $durations = ['30min', '1hour', '2hours', '3hours', '6hours', '12hours', '24hours'];
        $currencies = ['USD', 'EUR', 'GBP', 'UGX', 'KES', 'TZS', 'RWF'];

        // Create rates for each escort
        foreach ($escortIds as $escortId) {
            // Get escort details to determine pricing tier
            $escort = DB::table('escorts')->where('id', $escortId)->first();
            $isPremium = $escort->is_premium;
            $currency = 'USD'; // Default currency

            // Set different price ranges based on premium status
            $baseIncallPrice = $isPremium ? 150 : 80;
            $baseOutcallPrice = $isPremium ? 200 : 100;

            // Create rates for different durations
            foreach ($durations as $duration) {
                // Calculate price multiplier based on duration
                $multiplier = 1;
                switch ($duration) {
                    case '30min':
                        $multiplier = 0.6;
                        break;
                    case '1hour':
                        $multiplier = 1;
                        break;
                    case '2hours':
                        $multiplier = 1.8;
                        break;
                    case '3hours':
                        $multiplier = 2.5;
                        break;
                    case '6hours':
                        $multiplier = 4;
                        break;
                    case '12hours':
                        $multiplier = 7;
                        break;
                    case '24hours':
                        $multiplier = 12;
                        break;
                }

                $incallPrice = round($baseIncallPrice * $multiplier);
                $outcallPrice = round($baseOutcallPrice * $multiplier);

                // Insert rate
                DB::table('escort_rates')->insert([
                    'escort_id' => $escortId,
                    'duration' => $duration,
                    'incall_price' => $incallPrice,
                    'outcall_price' => $outcallPrice,
                    'currency' => $currency,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }
}
