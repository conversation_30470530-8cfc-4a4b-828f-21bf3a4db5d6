<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General settings
            [
                'key' => 'site_name',
                'value' => 'Get Hot Babes',
                'group' => 'general',
                'type' => 'text',
                'description' => 'The name of the website',
                'is_public' => true,
            ],
            [
                'key' => 'site_description',
                'value' => 'The premier platform for high-class escorts and elite companions. We connect discerning clients with sophisticated escorts for unforgettable experiences.',
                'group' => 'general',
                'type' => 'textarea',
                'description' => 'A short description of the website',
                'is_public' => true,
            ],
            [
                'key' => 'site_tagline',
                'value' => 'Find Your Perfect Companion',
                'group' => 'general',
                'type' => 'text',
                'description' => 'The tagline of the website',
                'is_public' => true,
            ],
            [
                'key' => 'currency',
                'value' => 'UGX',
                'group' => 'general',
                'type' => 'text',
                'description' => 'The currency used on the website',
                'is_public' => true,
            ],
            [
                'key' => 'currency_symbol',
                'value' => 'UGX',
                'group' => 'general',
                'type' => 'text',
                'description' => 'The currency symbol used on the website',
                'is_public' => true,
            ],
            [
                'key' => 'site_watermark',
                'value' => 'GHB',
                'group' => 'general',
                'type' => 'text',
                'description' => 'Watermark text displayed on images',
                'is_public' => true,
            ],
            [
                'key' => 'no_image_text',
                'value' => 'No image available',
                'group' => 'general',
                'type' => 'text',
                'description' => 'Text displayed when no image is available',
                'is_public' => true,
            ],
            [
                'key' => 'no_description_text',
                'value' => 'No description provided.',
                'group' => 'general',
                'type' => 'text',
                'description' => 'Text displayed when no description is available',
                'is_public' => true,
            ],

            // Contact information
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'group' => 'contact',
                'type' => 'email',
                'description' => 'The contact email address',
                'is_public' => true,
            ],
            [
                'key' => 'contact_phone',
                'value' => '+256 700 000000',
                'group' => 'contact',
                'type' => 'text',
                'description' => 'The contact phone number',
                'is_public' => true,
            ],

            // Footer settings
            [
                'key' => 'footer_text',
                'value' => 'Must be 18+ to use this service.',
                'group' => 'footer',
                'type' => 'text',
                'description' => 'Text displayed in the footer',
                'is_public' => true,
            ],
            [
                'key' => 'footer_about',
                'value' => 'The premier platform for high-class escorts and elite companions. We connect discerning clients with sophisticated escorts for unforgettable experiences.',
                'group' => 'footer',
                'type' => 'textarea',
                'description' => 'About text displayed in the footer',
                'is_public' => true,
            ],

            // CTA Section settings
            [
                'key' => 'cta_title',
                'value' => 'Are You an',
                'group' => 'cta',
                'type' => 'text',
                'description' => 'CTA section title (first part)',
                'is_public' => true,
            ],
            [
                'key' => 'cta_highlight',
                'value' => 'Escort or Agency',
                'group' => 'cta',
                'type' => 'text',
                'description' => 'CTA section highlighted text',
                'is_public' => true,
            ],
            [
                'key' => 'cta_description',
                'value' => 'Join our platform today to showcase your services and connect with potential clients.',
                'group' => 'cta',
                'type' => 'textarea',
                'description' => 'CTA section description',
                'is_public' => true,
            ],
            [
                'key' => 'cta_button_primary',
                'value' => 'Register as Escort/Agency',
                'group' => 'cta',
                'type' => 'text',
                'description' => 'Primary CTA button text',
                'is_public' => true,
            ],
            [
                'key' => 'cta_button_secondary',
                'value' => 'Contact Us',
                'group' => 'cta',
                'type' => 'text',
                'description' => 'Secondary CTA button text',
                'is_public' => true,
            ],

            // Social Media settings
            [
                'key' => 'social_facebook',
                'value' => '',
                'group' => 'social',
                'type' => 'url',
                'description' => 'Facebook page URL',
                'is_public' => true,
            ],
            [
                'key' => 'social_twitter',
                'value' => '',
                'group' => 'social',
                'type' => 'url',
                'description' => 'Twitter profile URL',
                'is_public' => true,
            ],
            [
                'key' => 'social_instagram',
                'value' => '',
                'group' => 'social',
                'type' => 'url',
                'description' => 'Instagram profile URL',
                'is_public' => true,
            ],

            // Tab labels
            [
                'key' => 'tab_about',
                'value' => 'About',
                'group' => 'ui',
                'type' => 'text',
                'description' => 'About tab label',
                'is_public' => true,
            ],
            [
                'key' => 'tab_services',
                'value' => 'Services',
                'group' => 'ui',
                'type' => 'text',
                'description' => 'Services tab label',
                'is_public' => true,
            ],
            [
                'key' => 'tab_rates',
                'value' => 'Rates',
                'group' => 'ui',
                'type' => 'text',
                'description' => 'Rates tab label',
                'is_public' => true,
            ],
        ];

        foreach ($settings as $setting) {
            Setting::firstOrCreate(['key' => $setting['key']], $setting);
        }
    }
}
