<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\UserProfile;
use App\Models\Escort;
use App\Models\Agency;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class TestUsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user if it doesn't exist
        $admin = User::where('username', 'admin')->first();
        if (!$admin) {
            $admin = User::create([
                'username' => 'admin',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'user_type' => 'admin',
                'is_active' => true,
            ]);
        }

        // Create admin profile if it doesn't exist
        if (!UserProfile::where('user_id', $admin->id)->exists()) {
            UserProfile::create([
                'user_id' => $admin->id,
                'first_name' => 'Admin',
                'last_name' => 'User',
                'phone' => '+1234567890',
            ]);
        }

        // Create regular user if it doesn't exist
        $user = User::where('username', 'client')->first();
        if (!$user) {
            $user = User::create([
                'username' => 'client',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'user_type' => 'regular',
                'is_active' => true,
            ]);
        }

        // Create user profile if it doesn't exist
        if (!UserProfile::where('user_id', $user->id)->exists()) {
            UserProfile::create([
                'user_id' => $user->id,
                'first_name' => 'Regular',
                'last_name' => 'Client',
                'phone' => '+1234567891',
            ]);
        }

        // Create escort user if it doesn't exist
        $escort_user = User::where('username', 'escort')->first();
        if (!$escort_user) {
            $escort_user = User::create([
                'username' => 'escort',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'user_type' => 'escort',
                'is_active' => true,
            ]);
        }

        // Create escort profile if it doesn't exist
        if (!UserProfile::where('user_id', $escort_user->id)->exists()) {
            UserProfile::create([
                'user_id' => $escort_user->id,
                'first_name' => 'Escort',
                'last_name' => 'User',
                'phone' => '+1234567892',
                'whatsapp_available' => true,
            ]);
        }

        // Create escort details if it doesn't exist
        if (!Escort::where('user_id', $escort_user->id)->exists()) {
            Escort::create([
                'user_id' => $escort_user->id,
                'name' => 'Sophia Escort',
                'gender' => 'female',
                'date_of_birth' => now()->subYears(25),
                'ethnicity' => 'Caucasian',
                'hair_color' => 'Blonde',
                'hair_length' => 'Long',
                'bust_size' => 'Large(C)',
                'height_cm' => 170,
                'weight_kg' => 55,
                'build' => 'Slim',
                'looks' => 'Sexy',
                'smoker' => false,
                'about' => 'Professional escort with a friendly personality.',
                'is_premium' => true,
                'is_verified' => true,
                'is_featured' => true,
                'is_independent' => true,
                'incall_available' => true,
                'outcall_available' => true,
                'is_new' => false,
                'is_online' => true,
                'profile_views' => 150,
                'slug' => 'sophia-escort',
            ]);
        }

        // Create agency user if it doesn't exist
        $agency_user = User::where('username', 'agency')->first();
        if (!$agency_user) {
            $agency_user = User::create([
                'username' => 'agency',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'user_type' => 'agency',
                'is_active' => true,
            ]);
        }

        // Create agency profile if it doesn't exist
        if (!UserProfile::where('user_id', $agency_user->id)->exists()) {
            UserProfile::create([
                'user_id' => $agency_user->id,
                'first_name' => 'Agency',
                'last_name' => 'Owner',
                'phone' => '+1234567893',
                'website' => 'https://example.com',
            ]);
        }

        // Create agency details if it doesn't exist
        if (!Agency::where('user_id', $agency_user->id)->exists()) {
            Agency::create([
                'user_id' => $agency_user->id,
                'name' => 'Elite Escorts Agency',
                'slug' => 'elite-escorts-agency',
                'description' => 'Premium escort agency providing high-class companionship services.',
                'website' => 'https://elite-escorts.example.com',
                'phone' => '+1234567894',
                'email' => '<EMAIL>',
                'address' => '123 Main St, New York, NY',
                'is_verified' => true,
                'is_premium' => true,
                'profile_views' => 250,
            ]);
        }

        $this->command->info('Test users created successfully!');
    }
}
