<?php

namespace Database\Seeders;

use App\Models\Location;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class LocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // First create countries
        $countries = [
            'Uganda',
            'Kenya',
            'Tanzania',
            'Rwanda',
            'South Sudan',
            'Democratic Republic of Congo',
        ];

        $countryIds = [];
        foreach ($countries as $country) {
            $location = Location::create([
                'name' => $country,
                'type' => 'country',
                'parent_id' => null,
                'slug' => Str::slug($country),
                'is_active' => true,
            ]);
            $countryIds[$country] = $location->id;
        }

        // Then create cities for Uganda (primary focus)
        $ugandaCities = [
            'Kampala',
            'Entebbe',
            'Jinja',
            'Mbarara',
            'Gulu',
            'Lira',
            'Mbale',
            'Kasese',
            'Soroti',
            'Arua',
            'Fort Portal',
            'Masaka',
            'Hoima',
            'Kabale',
            'Mukono',
        ];

        $ugandaCityIds = [];
        foreach ($ugandaCities as $city) {
            $location = Location::create([
                'name' => $city,
                'type' => 'city',
                'parent_id' => $countryIds['Uganda'],
                'slug' => Str::slug($city),
                'is_active' => true,
            ]);
            $ugandaCityIds[$city] = $location->id;
        }

        // Create areas for major Ugandan cities
        $areas = [
            // Kampala areas
            'Kampala' => [
                'Ntinda', 'Kololo', 'Nakasero', 'Bugolobi', 'Muyenga', 'Buziga',
                'Najera', 'Kansanga', 'Kabalagala', 'Wandegeya', 'Makerere',
                'Mulago', 'Kawempe', 'Rubaga', 'Makindye', 'Bukoto', 'Kisaasi',
                'Kyanja', 'Kira', 'Bweyogerere', 'Namugongo', 'Gayaza'
            ],
            // Entebbe areas
            'Entebbe' => [
                'Kitoro', 'Kigungu', 'Nakiwogo', 'Bugonga', 'Abaita Ababiri',
                'Katabi', 'Wakiso', 'Nkumba', 'Ziru'
            ],
            // Jinja areas
            'Jinja' => [
                'Walukuba', 'Mpumudde', 'Kimaka', 'Bugembe', 'Kakira',
                'Buwenge', 'Njeru', 'Budondo'
            ],
            // Mbarara areas
            'Mbarara' => [
                'Kakoba', 'Kamukuzi', 'Nyamitanga', 'Biharwe', 'Rwebikoona',
                'Katete', 'Kagongi', 'Rwizi'
            ],
            // Mukono areas
            'Mukono' => [
                'Seeta', 'Namilyango', 'Kyaliwajjala', 'Namuganga', 'Goma',
                'Ntawo', 'Kimenyedde'
            ],
        ];

        foreach ($areas as $cityName => $cityAreas) {
            if (isset($ugandaCityIds[$cityName])) {
                foreach ($cityAreas as $area) {
                    Location::create([
                        'name' => $area,
                        'type' => 'area',
                        'parent_id' => $ugandaCityIds[$cityName],
                        'slug' => Str::slug($area),
                        'is_active' => true,
                    ]);
                }
            }
        }

        // Create cities for other East African countries
        $otherCities = [
            ['name' => 'Nairobi', 'country' => 'Kenya'],
            ['name' => 'Mombasa', 'country' => 'Kenya'],
            ['name' => 'Kisumu', 'country' => 'Kenya'],
            ['name' => 'Nakuru', 'country' => 'Kenya'],
            ['name' => 'Eldoret', 'country' => 'Kenya'],

            ['name' => 'Dar es Salaam', 'country' => 'Tanzania'],
            ['name' => 'Arusha', 'country' => 'Tanzania'],
            ['name' => 'Mwanza', 'country' => 'Tanzania'],
            ['name' => 'Dodoma', 'country' => 'Tanzania'],
            ['name' => 'Zanzibar', 'country' => 'Tanzania'],

            ['name' => 'Kigali', 'country' => 'Rwanda'],
            ['name' => 'Butare', 'country' => 'Rwanda'],
            ['name' => 'Gisenyi', 'country' => 'Rwanda'],
            ['name' => 'Ruhengeri', 'country' => 'Rwanda'],

            ['name' => 'Juba', 'country' => 'South Sudan'],
            ['name' => 'Wau', 'country' => 'South Sudan'],
            ['name' => 'Malakal', 'country' => 'South Sudan'],

            ['name' => 'Kinshasa', 'country' => 'Democratic Republic of Congo'],
            ['name' => 'Lubumbashi', 'country' => 'Democratic Republic of Congo'],
            ['name' => 'Goma', 'country' => 'Democratic Republic of Congo'],
        ];

        foreach ($otherCities as $city) {
            if (isset($countryIds[$city['country']])) {
                Location::create([
                    'name' => $city['name'],
                    'type' => 'city',
                    'parent_id' => $countryIds[$city['country']],
                    'slug' => Str::slug($city['name']),
                    'is_active' => true,
                ]);
            }
        }
    }
}
