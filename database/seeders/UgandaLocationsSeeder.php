<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Location;
use Illuminate\Support\Str;

class UgandaLocationsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Uganda as the main country if it doesn't exist
        $uganda = Location::firstOrCreate([
            'name' => 'Uganda',
            'type' => 'country',
            'slug' => 'uganda',
            'parent_id' => null,
            'is_active' => true,
        ]);

        // Major cities and their areas
        $cities = [
            'Kampala' => [
                'areas' => [
                    'Wandegeya', 'Makerere', 'Mulago', 'Kawempe', 'Nakawa', 'Rubaga',
                    'Central Division', 'Makindye', 'Ntinda', 'Najera', 'Kansanga',
                    'Bugolobi', 'Kololo', 'Nakasero', 'Mengo', 'Katwe', 'Kisenyi',
                    'Bwaise', 'Kabalagala', 'Muyenga', 'Bukoto', 'Kyanja', 'Kira',
                    '<PERSON><PERSON><PERSON>', 'Kyaliwajjala', 'Namugongo', 'Gayaza', 'Kasangati'
                ]
            ],
            'Entebbe' => [
                'areas' => [
                    'Entebbe Town', 'Kitoro', 'Nakiwogo', 'Lugonjo', 'Katabi',
                    'Abaita Ababiri', 'Kigungu', 'Bugonga', 'Nkumba'
                ]
            ],
            'Jinja' => [
                'areas' => [
                    'Jinja Town', 'Bugembe', 'Walukuba', 'Kimaka', 'Mpumudde',
                    'Kakira', 'Buwenge', 'Njeru'
                ]
            ],
            'Mbarara' => [
                'areas' => [
                    'Mbarara Town', 'Kakoba', 'Kamukuzi', 'Nyamitanga', 'Biharwe',
                    'Ruharo', 'Kakiika', 'Nyakayojo'
                ]
            ],
            'Gulu' => [
                'areas' => [
                    'Gulu Town', 'Bardege', 'Layibi', 'Pece', 'Laroo',
                    'Unyama', 'Bungatira'
                ]
            ],
            'Lira' => [
                'areas' => [
                    'Lira Town', 'Adyel', 'Ojwina', 'Railways', 'Central Ward',
                    'Ngetta', 'Barr'
                ]
            ],
            'Mbale' => [
                'areas' => [
                    'Mbale Town', 'Industrial', 'Northern', 'Wanale', 'Namakwekwe',
                    'Malukhu', 'Namatala'
                ]
            ],
            'Kasese' => [
                'areas' => [
                    'Kasese Town', 'Nyamwamba', 'Rukoki', 'Kilembe', 'Maliba',
                    'Muhokya', 'Katwe'
                ]
            ],
            'Masaka' => [
                'areas' => [
                    'Masaka Town', 'Kimaanya', 'Katwe', 'Nyendo', 'Kijabwemi',
                    'Mukungwe', 'Ssaza'
                ]
            ],
            'Fort Portal' => [
                'areas' => [
                    'Fort Portal Town', 'Mpanga', 'East Division', 'North Division',
                    'South Division', 'Buhinga', 'Karambi'
                ]
            ],
            'Arua' => [
                'areas' => [
                    'Arua Town', 'Hill', 'Tanganyika', 'Oli', 'Mvara',
                    'Pajulu', 'Ayivu'
                ]
            ],
            'Soroti' => [
                'areas' => [
                    'Soroti Town', 'Opiyai', 'Arapai', 'Tubur', 'Gweri',
                    'Kamuda', 'Asuret'
                ]
            ],
            'Hoima' => [
                'areas' => [
                    'Hoima Town', 'Mparo', 'Kiryatete', 'Bujumbura', 'Kahoora',
                    'Kyangwali', 'Buseruka'
                ]
            ],
            'Kabale' => [
                'areas' => [
                    'Kabale Town', 'Kamuganguzi', 'Kikungiri', 'Kamukira', 'Kitumba',
                    'Maziba', 'Rubaya'
                ]
            ],
            'Mityana' => [
                'areas' => [
                    'Mityana Town', 'Busimbi', 'Ttamu', 'Maanyi', 'Zigoti',
                    'Kalangalo', 'Sekanyonyi'
                ]
            ]
        ];

        foreach ($cities as $cityName => $cityData) {
            // Create or get the city
            $city = Location::firstOrCreate([
                'name' => $cityName,
                'type' => 'city',
                'slug' => Str::slug($cityName),
                'parent_id' => $uganda->id,
                'is_active' => true,
            ]);

            // Create areas for this city
            foreach ($cityData['areas'] as $areaName) {
                $baseSlug = Str::slug($areaName);
                $slug = $baseSlug;
                $counter = 1;

                // Check for duplicate slugs and append city name if needed
                while (Location::where('slug', $slug)->exists()) {
                    $slug = $baseSlug . '-' . Str::slug($cityName);
                    if (Location::where('slug', $slug)->exists()) {
                        $slug = $baseSlug . '-' . Str::slug($cityName) . '-' . $counter;
                        $counter++;
                    } else {
                        break;
                    }
                }

                Location::firstOrCreate([
                    'name' => $areaName,
                    'type' => 'area',
                    'slug' => $slug,
                    'parent_id' => $city->id,
                    'is_active' => true,
                ]);
            }
        }

        $this->command->info('Uganda locations seeded successfully!');
    }
}
