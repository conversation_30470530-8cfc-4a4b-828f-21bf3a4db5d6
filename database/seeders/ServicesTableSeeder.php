<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ServicesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $services = [
            ['name' => 'Massage', 'slug' => 'massage', 'description' => 'Professional massage services', 'category' => 'Physical'],
            ['name' => 'Escort', 'slug' => 'escort', 'description' => 'Companionship for events and outings', 'category' => 'Social'],
            ['name' => 'Overnight', 'slug' => 'overnight', 'description' => 'Overnight companionship', 'category' => 'Extended'],
            ['name' => 'GFE', 'slug' => 'gfe', 'description' => 'Girlfriend Experience', 'category' => 'Social'],
            ['name' => 'Dinner Date', 'slug' => 'dinner-date', 'description' => 'Companionship for dinner', 'category' => 'Social'],
            ['name' => 'Travel Companion', 'slug' => 'travel-companion', 'description' => 'Accompaniment for travel', 'category' => 'Extended'],
            ['name' => 'BDSM', 'slug' => 'bdsm', 'description' => 'BDSM services', 'category' => 'Specialty'],
            ['name' => 'Roleplay', 'slug' => 'roleplay', 'description' => 'Fantasy roleplay services', 'category' => 'Specialty'],
            ['name' => 'Couples', 'slug' => 'couples', 'description' => 'Services for couples', 'category' => 'Social'],
            ['name' => 'Fetish', 'slug' => 'fetish', 'description' => 'Various fetish services', 'category' => 'Specialty'],
        ];

        foreach ($services as $service) {
            DB::table('services')->insert([
                'name' => $service['name'],
                'slug' => $service['slug'],
                'description' => $service['description'],
                'category' => $service['category'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
