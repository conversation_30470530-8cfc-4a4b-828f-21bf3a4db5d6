<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class EscortServicesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all escort IDs
        $escortIds = DB::table('escorts')->pluck('id')->toArray();

        // Get all service IDs
        $serviceIds = DB::table('services')->pluck('id')->toArray();

        // Define specific services for each escort
        $escortServices = [
            1 => [1, 2, 3, 4, 5, 6], // Sophia: Massage, Escort, Overnight, GFE, Dinner Date, Travel Companion
            2 => [2, 4, 5, 8, 9], // Emma: Escort, GFE, Dinner Date, Roleplay, Couples
            3 => [1, 2, 3, 4, 7, 10], // Olivia: Massage, Escort, Overnight, GFE, BDSM, Fetish
            4 => [1, 2, 4, 5], // Ava: Massage, Escort, GF<PERSON>, Dinner Date
            5 => [2, 3, 4, 6, 9], // Isabella: Escort, Overnight, GF<PERSON>, Travel Companion, Couples
        ];

        // Assign services to escorts
        foreach ($escortIds as $escortId) {
            // Get services for this escort or assign random services
            $services = $escortServices[$escortId] ?? array_rand(array_flip($serviceIds), rand(3, 6));

            if (!is_array($services)) {
                $services = [$services];
            }

            foreach ($services as $serviceId) {
                DB::table('escort_services')->insert([
                    'escort_id' => $escortId,
                    'service_id' => $serviceId,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }
}
