<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Storage;
use App\Models\Agency;
use Illuminate\Support\Str;

class AgencyLogoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create the agencies directory if it doesn't exist
        if (!Storage::disk('public')->exists('agencies')) {
            Storage::disk('public')->makeDirectory('agencies');
        }

        // Define logo paths
        $logos = [
            'agencies/vip-escorts-agency.png',
            'agencies/elite-companions.png',
            'agencies/luxury-escorts.png',
            'agencies/diamond-escorts.png',
            'agencies/elite-models-agency.png',
            'agencies/luxury-escorts-agency.png',
            'agencies/premium-companions.png',
            'agencies/royal-escorts-agency.png',
            'agencies/vip-companions.png'
        ];

        // Generate sample logo images and update agencies
        $agencies = Agency::all();
        foreach ($agencies as $index => $agency) {
            $logoIndex = $index % count($logos);
            $logoPath = $logos[$logoIndex];

            // Generate a sample logo image
            $this->generateLogoImage($logoPath, $agency->name);

            // Update the agency with the logo path
            $agency->logo_path = $logoPath;
            $agency->save();

            if ($this->command) {
                $this->command->info("Generated logo for {$agency->name}");
            } else {
                echo "Generated logo for {$agency->name}\n";
            }
        }
    }

    /**
     * Generate a sample logo image with the agency name
     */
    private function generateLogoImage($path, $agencyName)
    {
        // Create a blank image
        $width = 600;
        $height = 300;
        $image = imagecreatetruecolor($width, $height);

        // Set background color (light gradient)
        $bgColor = imagecolorallocate($image, 245, 245, 250);
        imagefill($image, 0, 0, $bgColor);

        // Add some styling
        $accentColor = imagecolorallocate($image, 255, 90, 140); // Pink
        $textColor = imagecolorallocate($image, 50, 50, 50); // Dark gray

        // Draw a decorative border
        imagefilledrectangle($image, 0, 0, $width, 10, $accentColor);
        imagefilledrectangle($image, 0, $height - 10, $width, $height, $accentColor);
        imagefilledrectangle($image, 0, 0, 10, $height, $accentColor);
        imagefilledrectangle($image, $width - 10, 0, $width, $height, $accentColor);

        // Add agency name using built-in font
        $text = strtoupper($agencyName);
        $text = wordwrap($text, 30, "\n", true);
        $lines = explode("\n", $text);
        $lineHeight = 30;
        $y = ($height - (count($lines) * $lineHeight)) / 2;

        foreach ($lines as $line) {
            $x = ($width - (strlen($line) * 9)) / 2; // Approximate width of characters
            imagestring($image, 5, $x, $y, $line, $textColor);
            $y += $lineHeight;
        }

        // Add "ESCORTS" text below
        $subText = "ESCORTS & COMPANIONS";
        $x = ($width - (strlen($subText) * 7)) / 2;
        imagestring($image, 4, $x, $y + 10, $subText, $accentColor);

        // Save the image
        $fullPath = Storage::disk('public')->path($path);
        $directory = dirname($fullPath);

        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }

        imagepng($image, $fullPath);
        imagedestroy($image);
    }
}
