<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class AgenciesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // First create users for the agencies
        $users = [
            [
                'username' => 'vipescorts',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'user_type' => 'agency',
                'is_active' => true,
            ],
            [
                'username' => 'elitecompanions',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'user_type' => 'agency',
                'is_active' => true,
            ],
            [
                'username' => 'luxuryescorts',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'user_type' => 'agency',
                'is_active' => true,
            ],
        ];

        $userIds = [];
        foreach ($users as $user) {
            $userId = DB::table('users')->insertGetId([
                'username' => $user['username'],
                'email' => $user['email'],
                'password' => $user['password'],
                'user_type' => $user['user_type'],
                'is_active' => $user['is_active'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            $userIds[] = $userId;
        }

        // Now create agencies linked to these users
        $agencies = [
            [
                'user_id' => $userIds[0],
                'name' => 'VIP Escorts Agency',
                'description' => 'Providing high-class escort services for discerning clients. Our escorts are carefully selected for their beauty, intelligence, and charm.',
                'logo_path' => 'agencies/vip-escorts-logo.jpg',
                'website' => 'https://vipescorts.example.com',
                'phone' => '+1234567890',
                'email' => '<EMAIL>',
                'address' => 'Kampala, Uganda',
                'is_verified' => true,
                'is_premium' => true,
            ],
            [
                'user_id' => $userIds[1],
                'name' => 'Elite Companions',
                'description' => 'Elite Companions offers the most exclusive escort service in East Africa. Our models are sophisticated, educated, and discreet.',
                'logo_path' => 'agencies/elite-companions-logo.jpg',
                'website' => 'https://elitecompanions.example.com',
                'phone' => '+1987654321',
                'email' => '<EMAIL>',
                'address' => 'Nairobi, Kenya',
                'is_verified' => true,
                'is_premium' => true,
            ],
            [
                'user_id' => $userIds[2],
                'name' => 'Luxury Escorts',
                'description' => 'Luxury Escorts provides premium companionship services for business events, travel, and private engagements. Discretion guaranteed.',
                'logo_path' => 'agencies/luxury-escorts-logo.jpg',
                'website' => 'https://luxuryescorts.example.com',
                'phone' => '+1122334455',
                'email' => '<EMAIL>',
                'address' => 'Dar es Salaam, Tanzania',
                'is_verified' => true,
                'is_premium' => false,
            ],
        ];

        foreach ($agencies as $agency) {
            DB::table('agencies')->insert([
                'user_id' => $agency['user_id'],
                'name' => $agency['name'],
                'description' => $agency['description'],
                'logo_path' => $agency['logo_path'],
                'website' => $agency['website'],
                'phone' => $agency['phone'],
                'email' => $agency['email'],
                'address' => $agency['address'],
                'is_verified' => $agency['is_verified'],
                'is_premium' => $agency['is_premium'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
