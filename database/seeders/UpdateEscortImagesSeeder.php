<?php

namespace Database\Seeders;

use App\Models\Escort;
use App\Models\EscortImage;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class UpdateEscortImagesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all escorts
        $escorts = Escort::all();

        // Define available images for each gender
        $femaleImages = [];
        $maleImages = [];
        $transImages = [];
        $coupleImages = [];

        // Get all downloaded images
        for ($i = 1; $i <= 5; $i++) {
            $femaleImages[] = "escorts/female/female-{$i}.jpg";
            $maleImages[] = "escorts/male/male-{$i}.jpg";
            $transImages[] = "escorts/transsexual/trans-{$i}.jpg";
            $coupleImages[] = "escorts/couple/couple-{$i}.jpg";
        }

        // Process each escort
        foreach ($escorts as $escort) {
            // Delete existing images
            EscortImage::where('escort_id', $escort->id)->delete();

            // Determine which image set to use based on gender
            $imageSet = [];
            switch ($escort->gender) {
                case 'female':
                    $imageSet = $femaleImages;
                    break;
                case 'male':
                    $imageSet = $maleImages;
                    break;
                case 'transsexual':
                    $imageSet = $transImages;
                    break;
                case 'couple':
                    $imageSet = $coupleImages;
                    break;
                default:
                    $imageSet = $femaleImages;
            }

            // Randomly select 2-4 images
            $numImages = rand(2, 4);
            // Shuffle the array and take the first $numImages elements
            shuffle($imageSet);
            $selectedImages = array_slice($imageSet, 0, $numImages);

            // Add images for this escort
            foreach ($selectedImages as $index => $imagePath) {
                // Create the image record
                EscortImage::create([
                    'escort_id' => $escort->id,
                    'image_path' => $imagePath,
                    'is_primary' => $index === 0, // First image is primary
                    'sort_order' => $index + 1,
                ]);
            }
        }

        $this->command->info('Escort images have been updated!');
    }
}
