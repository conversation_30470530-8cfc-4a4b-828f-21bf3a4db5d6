<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LocationsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // First add countries
        $countries = [
            ['name' => 'Uganda', 'type' => 'country', 'slug' => 'uganda', 'is_active' => true],
            ['name' => 'Kenya', 'type' => 'country', 'slug' => 'kenya', 'is_active' => true],
            ['name' => 'Tanzania', 'type' => 'country', 'slug' => 'tanzania', 'is_active' => true],
            ['name' => 'Rwanda', 'type' => 'country', 'slug' => 'rwanda', 'is_active' => true],
        ];

        $countryIds = [];
        foreach ($countries as $country) {
            $countryId = DB::table('locations')->insertGetId([
                'name' => $country['name'],
                'type' => $country['type'],
                'slug' => $country['slug'],
                'is_active' => $country['is_active'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            $countryIds[$country['name']] = $countryId;
        }

        // Then add cities
        $cities = [
            ['name' => 'Kampala', 'parent_id' => $countryIds['Uganda'], 'type' => 'city', 'slug' => 'kampala', 'is_active' => true],
            ['name' => 'Entebbe', 'parent_id' => $countryIds['Uganda'], 'type' => 'city', 'slug' => 'entebbe', 'is_active' => true],
            ['name' => 'Jinja', 'parent_id' => $countryIds['Uganda'], 'type' => 'city', 'slug' => 'jinja', 'is_active' => true],
            ['name' => 'Mbarara', 'parent_id' => $countryIds['Uganda'], 'type' => 'city', 'slug' => 'mbarara', 'is_active' => true],
            ['name' => 'Gulu', 'parent_id' => $countryIds['Uganda'], 'type' => 'city', 'slug' => 'gulu', 'is_active' => true],
            ['name' => 'Mbale', 'parent_id' => $countryIds['Uganda'], 'type' => 'city', 'slug' => 'mbale', 'is_active' => true],
            ['name' => 'Nairobi', 'parent_id' => $countryIds['Kenya'], 'type' => 'city', 'slug' => 'nairobi', 'is_active' => true],
            ['name' => 'Mombasa', 'parent_id' => $countryIds['Kenya'], 'type' => 'city', 'slug' => 'mombasa', 'is_active' => true],
            ['name' => 'Dar es Salaam', 'parent_id' => $countryIds['Tanzania'], 'type' => 'city', 'slug' => 'dar-es-salaam', 'is_active' => true],
            ['name' => 'Kigali', 'parent_id' => $countryIds['Rwanda'], 'type' => 'city', 'slug' => 'kigali', 'is_active' => true],
        ];

        foreach ($cities as $city) {
            DB::table('locations')->insert([
                'name' => $city['name'],
                'parent_id' => $city['parent_id'],
                'type' => $city['type'],
                'slug' => $city['slug'],
                'is_active' => $city['is_active'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
