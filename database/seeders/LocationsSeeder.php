<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class LocationsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Add Uganda as country
        $ugandaId = DB::table('locations')->insertGetId([
            'name' => 'Uganda',
            'type' => 'country',
            'parent_id' => null,
            'slug' => 'uganda',
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Add major cities in Uganda
        $cities = [
            'Kampala', 'Entebbe', 'Jinja', 'Mbarara', 'Gulu', 
            'Lira', 'Mbale', 'Fort Portal', 'Masaka', 'Arua'
        ];

        $cityIds = [];
        foreach ($cities as $city) {
            $cityIds[$city] = DB::table('locations')->insertGetId([
                'name' => $city,
                'type' => 'city',
                'parent_id' => $ugandaId,
                'slug' => Str::slug($city),
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // Add areas in Kampala
        $kampalaAreas = [
            'Ntinda', 'Bukoto', 'Kololo', 'Nakasero', 'Bugolobi',
            'Muyenga', 'Naguru', 'Buziga', 'Munyonyo', 'Kansanga',
            'Makindye', 'Nsambya', 'Kibuli', 'Kabalagala', 'Kisementi',
            'Nakawa', 'Kyambogo', 'Banda', 'Kireka', 'Bweyogerere',
            'Naalya', 'Namugongo', 'Kiwatule', 'Najjera', 'Kulambiro',
            'Wandegeya', 'Makerere', 'Mulago', 'Kamwokya', 'Bukesa'
        ];

        foreach ($kampalaAreas as $area) {
            DB::table('locations')->insert([
                'name' => $area,
                'type' => 'area',
                'parent_id' => $cityIds['Kampala'],
                'slug' => Str::slug($area),
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // Add areas in Entebbe
        $entebbeAreas = [
            'Kitooro', 'Katabi', 'Lunyo', 'Nkumba', 'Abaita Ababiri',
            'Kitubulu', 'Bugonga', 'Kiwafu', 'State House', 'Airport'
        ];

        foreach ($entebbeAreas as $area) {
            DB::table('locations')->insert([
                'name' => $area,
                'type' => 'area',
                'parent_id' => $cityIds['Entebbe'],
                'slug' => Str::slug($area),
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // Add areas in Jinja
        $jinjaAreas = [
            'Main Street', 'Iganga Road', 'Clive Road', 'Nile Avenue', 'Nalufenya',
            'Mbulamuti', 'Bugembe', 'Kakira', 'Njeru', 'Buwenge'
        ];

        foreach ($jinjaAreas as $area) {
            DB::table('locations')->insert([
                'name' => $area,
                'type' => 'area',
                'parent_id' => $cityIds['Jinja'],
                'slug' => Str::slug($area),
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
