<?php

namespace Database\Seeders;

use App\Models\Service;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class ServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $services = [
            // Standard services
            ['name' => 'Massage', 'category' => 'Standard', 'description' => 'Professional massage service'],
            ['name' => 'Dinner Date', 'category' => 'Standard', 'description' => 'Companionship for dinner and events'],
            ['name' => 'Travel Companion', 'category' => 'Standard', 'description' => 'Accompaniment for travel and trips'],
            ['name' => 'Overnight', 'category' => 'Standard', 'description' => 'Extended overnight companionship'],
            ['name' => 'Weekend', 'category' => 'Standard', 'description' => 'Weekend companionship package'],

            // Special services
            ['name' => 'GFE', 'category' => 'Special', 'description' => 'Girlfriend Experience'],
            ['name' => 'PSE', 'category' => 'Special', 'description' => 'Pornstar Experience'],
            ['name' => 'BDSM', 'category' => 'Special', 'description' => 'BDSM and role play services'],
            ['name' => 'Couples', 'category' => 'Special', 'description' => 'Services for couples'],
            ['name' => 'Fetish', 'category' => 'Special', 'description' => 'Various fetish services'],

            // Additional services
            ['name' => 'Striptease', 'category' => 'Additional', 'description' => 'Private striptease performance'],
            ['name' => 'Roleplay', 'category' => 'Additional', 'description' => 'Various roleplay scenarios'],
            ['name' => 'Toys', 'category' => 'Additional', 'description' => 'Use of toys and accessories'],
            ['name' => 'Costumes', 'category' => 'Additional', 'description' => 'Various costume options available'],
            ['name' => 'Shower Together', 'category' => 'Additional', 'description' => 'Shared shower experience'],
        ];

        foreach ($services as $service) {
            // Check if service already exists
            $slug = Str::slug($service['name']);
            $existingService = Service::where('slug', $slug)->first();

            if (!$existingService) {
                Service::create([
                    'name' => $service['name'],
                    'category' => $service['category'],
                    'description' => $service['description'],
                    'slug' => $slug,
                ]);
            }
        }
    }
}
