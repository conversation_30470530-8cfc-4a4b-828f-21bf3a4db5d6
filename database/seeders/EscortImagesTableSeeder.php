<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class EscortImagesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all escort IDs
        $escortIds = DB::table('escorts')->pluck('id')->toArray();

        // Define image paths for each escort
        $escortImages = [
            1 => [ // Sophia
                ['image_path' => 'escorts/sophia/sophia-1.jpg', 'is_primary' => true, 'sort_order' => 1],
                ['image_path' => 'escorts/sophia/sophia-2.jpg', 'is_primary' => false, 'sort_order' => 2],
                ['image_path' => 'escorts/sophia/sophia-3.jpg', 'is_primary' => false, 'sort_order' => 3],
                ['image_path' => 'escorts/sophia/sophia-4.jpg', 'is_primary' => false, 'sort_order' => 4],
            ],
            2 => [ // Emma
                ['image_path' => 'escorts/emma/emma-1.jpg', 'is_primary' => true, 'sort_order' => 1],
                ['image_path' => 'escorts/emma/emma-2.jpg', 'is_primary' => false, 'sort_order' => 2],
                ['image_path' => 'escorts/emma/emma-3.jpg', 'is_primary' => false, 'sort_order' => 3],
            ],
            3 => [ // Olivia
                ['image_path' => 'escorts/olivia/olivia-1.jpg', 'is_primary' => true, 'sort_order' => 1],
                ['image_path' => 'escorts/olivia/olivia-2.jpg', 'is_primary' => false, 'sort_order' => 2],
                ['image_path' => 'escorts/olivia/olivia-3.jpg', 'is_primary' => false, 'sort_order' => 3],
                ['image_path' => 'escorts/olivia/olivia-4.jpg', 'is_primary' => false, 'sort_order' => 4],
                ['image_path' => 'escorts/olivia/olivia-5.jpg', 'is_primary' => false, 'sort_order' => 5],
            ],
            4 => [ // Ava
                ['image_path' => 'escorts/ava/ava-1.jpg', 'is_primary' => true, 'sort_order' => 1],
                ['image_path' => 'escorts/ava/ava-2.jpg', 'is_primary' => false, 'sort_order' => 2],
                ['image_path' => 'escorts/ava/ava-3.jpg', 'is_primary' => false, 'sort_order' => 3],
            ],
            5 => [ // Isabella
                ['image_path' => 'escorts/isabella/isabella-1.jpg', 'is_primary' => true, 'sort_order' => 1],
                ['image_path' => 'escorts/isabella/isabella-2.jpg', 'is_primary' => false, 'sort_order' => 2],
                ['image_path' => 'escorts/isabella/isabella-3.jpg', 'is_primary' => false, 'sort_order' => 3],
                ['image_path' => 'escorts/isabella/isabella-4.jpg', 'is_primary' => false, 'sort_order' => 4],
            ],
        ];

        // Insert images for each escort
        foreach ($escortIds as $escortId) {
            if (isset($escortImages[$escortId])) {
                foreach ($escortImages[$escortId] as $image) {
                    DB::table('escort_images')->insert([
                        'escort_id' => $escortId,
                        'image_path' => $image['image_path'],
                        'is_primary' => $image['is_primary'],
                        'sort_order' => $image['sort_order'],
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            } else {
                // For any escort without specific images, add generic placeholders
                for ($i = 1; $i <= 3; $i++) {
                    DB::table('escort_images')->insert([
                        'escort_id' => $escortId,
                        'image_path' => 'escorts/placeholder/escort-' . $i . '.jpg',
                        'is_primary' => $i === 1, // First image is primary
                        'sort_order' => $i,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }
    }
}
