<?php

namespace Database\Seeders;

use App\Models\Escort;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class FeaturedEscortsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Set the first 4 escorts as featured
        $escorts = Escort::take(4)->get();

        foreach ($escorts as $escort) {
            $escort->is_featured = true;
            $escort->save();
        }

        echo "Set " . count($escorts) . " escorts as featured.\n";
    }
}
