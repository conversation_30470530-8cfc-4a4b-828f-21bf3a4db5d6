<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\StatusPricing;

class AgencyPricingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Agency Approval Pricing
        $approvalPricing = [
            [
                'request_type' => 'agency_approval',
                'duration' => 'week',
                'price' => 30000,
                'is_active' => true,
            ],
            [
                'request_type' => 'agency_approval',
                'duration' => 'month',
                'price' => 100000,
                'is_active' => true,
            ],
            [
                'request_type' => 'agency_approval',
                'duration' => 'annual',
                'price' => 800000,
                'is_active' => true,
            ],
        ];

        // Agency Featured Pricing
        $featuredPricing = [
            [
                'request_type' => 'agency_featured',
                'duration' => 'week',
                'price' => 20000,
                'is_active' => true,
            ],
            [
                'request_type' => 'agency_featured',
                'duration' => 'month',
                'price' => 60000,
                'is_active' => true,
            ],
            [
                'request_type' => 'agency_featured',
                'duration' => 'annual',
                'price' => 500000,
                'is_active' => true,
            ],
        ];

        // Insert pricing data
        foreach ($approvalPricing as $pricing) {
            StatusPricing::updateOrCreate(
                [
                    'request_type' => $pricing['request_type'],
                    'duration' => $pricing['duration'],
                ],
                $pricing
            );
        }

        foreach ($featuredPricing as $pricing) {
            StatusPricing::updateOrCreate(
                [
                    'request_type' => $pricing['request_type'],
                    'duration' => $pricing['duration'],
                ],
                $pricing
            );
        }
    }
}
