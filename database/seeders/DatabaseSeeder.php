<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user if it doesn't exist
        if (!\App\Models\User::where('email', '<EMAIL>')->exists()) {
            \App\Models\User::factory()->create([
                'username' => 'admin',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'user_type' => 'admin',
                'email_verified_at' => now(),
            ]);
        }

        // Run other seeders
        $this->call([
            // Base data
            LocationSeeder::class,
            ServiceSeeder::class,
            SettingsSeeder::class,

            // Escorts data (includes relationships and details)
            EscortSeeder::class,

            // Update escort images with more realistic ones
            UpdateEscortImagesSeeder::class,

            // Test users for development
            TestUsersSeeder::class,
        ]);
    }
}
