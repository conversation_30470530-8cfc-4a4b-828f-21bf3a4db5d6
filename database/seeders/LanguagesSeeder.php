<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LanguagesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $languages = [
            ['name' => 'English', 'code' => 'en'],
            ['name' => 'French', 'code' => 'fr'],
            ['name' => 'Spanish', 'code' => 'es'],
            ['name' => 'German', 'code' => 'de'],
            ['name' => 'Italian', 'code' => 'it'],
            ['name' => 'Portuguese', 'code' => 'pt'],
            ['name' => 'Russian', 'code' => 'ru'],
            ['name' => 'Chinese', 'code' => 'zh'],
            ['name' => 'Japanese', 'code' => 'ja'],
            ['name' => 'Arabic', 'code' => 'ar'],
            ['name' => 'Hindi', 'code' => 'hi'],
            ['name' => 'Swahili', 'code' => 'sw'],
            ['name' => 'Luganda', 'code' => 'lg'],
            ['name' => 'Kinyarwanda', 'code' => 'rw'],
            ['name' => 'Yoruba', 'code' => 'yo'],
            ['name' => 'Igbo', 'code' => 'ig'],
            ['name' => 'Hausa', 'code' => 'ha'],
            ['name' => 'Amharic', 'code' => 'am'],
            ['name' => 'Somali', 'code' => 'so'],
        ];

        foreach ($languages as $language) {
            DB::table('languages')->insert([
                'name' => $language['name'],
                'code' => $language['code'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
