<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Setting;

class AdminContactSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $adminContactSettings = [
            [
                'key' => 'admin_phone',
                'value' => '+256 XXX XXX XXX',
                'group' => 'contact',
                'type' => 'text',
                'description' => 'Primary admin contact phone number',
                'is_public' => true,
            ],
            [
                'key' => 'admin_whatsapp',
                'value' => '+256 XXX XXX XXX',
                'group' => 'contact',
                'type' => 'text',
                'description' => 'Admin WhatsApp contact number',
                'is_public' => true,
            ],
            [
                'key' => 'admin_email',
                'value' => '<EMAIL>',
                'group' => 'contact',
                'type' => 'email',
                'description' => 'Primary admin contact email address',
                'is_public' => true,
            ],
            [
                'key' => 'admin_telegram',
                'value' => '@admin',
                'group' => 'contact',
                'type' => 'text',
                'description' => 'Admin Telegram username (optional)',
                'is_public' => true,
            ],
            [
                'key' => 'payment_instructions',
                'value' => 'Please contact our admin team for payment processing instructions. We accept various payment methods and will guide you through the process.',
                'group' => 'general',
                'type' => 'textarea',
                'description' => 'Instructions shown to users for payment processing',
                'is_public' => true,
            ],
            [
                'key' => 'request_confirmation_message',
                'value' => 'Your request has been submitted successfully. Our admin team will contact you shortly to confirm payment and process your request.',
                'group' => 'general',
                'type' => 'textarea',
                'description' => 'Message displayed after request submission',
                'is_public' => true,
            ],
        ];

        foreach ($adminContactSettings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
