<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Agency;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Escort>
 */
class EscortFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $gender = fake()->randomElement(['female', 'male', 'couple', 'gay', 'transsexual']);
        $name = $gender === 'female' ? fake()->firstNameFemale() : fake()->firstNameMale();
        $name .= ' ' . fake()->randomElement(['Hot', 'Sexy', 'Sweet', 'Naughty', 'Wild', 'Exotic', 'Sensual']);
        
        return [
            'user_id' => User::factory()->create(['user_type' => 'escort'])->id,
            'agency_id' => fake()->boolean(30) ? Agency::factory() : null,
            'name' => $name,
            'gender' => $gender,
            'date_of_birth' => fake()->dateTimeBetween('-40 years', '-18 years')->format('Y-m-d'),
            'ethnicity' => fake()->randomElement([
                'Latin', 'Caucasian', 'Black', 'White', 'MiddleEast', 
                'Asian', 'Indian', 'Aborigine', 'Native American', 'Other'
            ]),
            'hair_color' => fake()->randomElement([
                'Black', 'Blonde', 'Brown', 'Brunette', 'Chestnut', 
                'Auburn', 'Dark-blonde', 'Golden', 'Red', 'Grey', 
                'Silver', 'White', 'Other'
            ]),
            'hair_length' => fake()->randomElement(['Bald', 'Short', 'Shoulder', 'Long', 'Very Long']),
            'bust_size' => $gender === 'female' ? fake()->randomElement([
                'Very small', 'Small(A)', 'Medium(B)', 'Large(C)', 
                'Very Large(D)', 'Enormous(E+)'
            ]) : null,
            'height_cm' => fake()->numberBetween(150, 190),
            'weight_kg' => fake()->numberBetween(45, 90),
            'build' => fake()->randomElement(['Skinny', 'Slim', 'Regular', 'Curvy', 'Fat']),
            'looks' => fake()->randomElement(['Nothing Special', 'Average', 'Sexy', 'Ultra Sexy']),
            'smoker' => fake()->boolean(30),
            'about' => fake()->paragraphs(3, true),
            'education' => fake()->randomElement(['High School', 'College', 'University', 'Graduate']),
            'sports' => fake()->randomElements(['Swimming', 'Yoga', 'Running', 'Gym', 'Dancing', 'Tennis'], fake()->numberBetween(0, 3)),
            'hobbies' => fake()->randomElements(['Reading', 'Cooking', 'Traveling', 'Music', 'Movies', 'Shopping'], fake()->numberBetween(0, 4)),
            'zodiac_sign' => fake()->randomElement([
                'Aries', 'Taurus', 'Gemini', 'Cancer', 'Leo', 'Virgo', 
                'Libra', 'Scorpio', 'Sagittarius', 'Capricorn', 'Aquarius', 'Pisces'
            ]),
            'sexual_orientation' => fake()->randomElement(['Straight', 'Bisexual', 'Gay', 'Lesbian']),
            'occupation' => fake()->randomElement(['Student', 'Model', 'Dancer', 'Massage Therapist', 'Secretary', 'Teacher']),
            'is_premium' => fake()->boolean(20),
            'is_verified' => fake()->boolean(60),
            'is_featured' => fake()->boolean(10),
            'is_independent' => fake()->boolean(70),
            'incall_available' => fake()->boolean(80),
            'outcall_available' => fake()->boolean(70),
            'is_new' => fake()->boolean(30),
            'is_online' => fake()->boolean(50),
            'profile_views' => fake()->numberBetween(0, 1000),
            'slug' => function (array $attributes) {
                return Str::slug($attributes['name']);
            },
        ];
    }
}
