<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Agency>
 */
class AgencyFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory()->create(['user_type' => 'agency'])->id,
            'name' => fake()->company() . ' ' . fake()->randomElement(['Escorts', 'Models', 'Companions', 'VIP']),
            'description' => fake()->paragraphs(2, true),
            'logo_path' => 'agencies/logos/default.png',
            'website' => fake()->optional(0.7)->url(),
            'phone' => fake()->phoneNumber(),
            'email' => fake()->companyEmail(),
            'address' => fake()->optional(0.5)->address(),
            'is_verified' => fake()->boolean(70),
            'is_premium' => fake()->boolean(30),
        ];
    }
}
