<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('escort_images', function (Blueprint $table) {
            $table->boolean('is_main')->default(false)->after('is_primary');
        });

        // Set is_main to match is_primary for existing records
        DB::table('escort_images')
            ->where('is_primary', true)
            ->update(['is_main' => true]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('escort_images', function (Blueprint $table) {
            $table->dropColumn('is_main');
        });
    }
};
