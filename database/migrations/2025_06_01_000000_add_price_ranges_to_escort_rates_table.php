<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('escort_rates', function (Blueprint $table) {
            // Add new price range columns
            $table->decimal('min_incall_price', 10, 2)->nullable()->after('incall_price');
            $table->decimal('max_incall_price', 10, 2)->nullable()->after('min_incall_price');
            $table->decimal('min_outcall_price', 10, 2)->nullable()->after('outcall_price');
            $table->decimal('max_outcall_price', 10, 2)->nullable()->after('min_outcall_price');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('escort_rates', function (Blueprint $table) {
            // Remove price range columns
            $table->dropColumn([
                'min_incall_price',
                'max_incall_price',
                'min_outcall_price',
                'max_outcall_price',
            ]);
        });
    }
};
