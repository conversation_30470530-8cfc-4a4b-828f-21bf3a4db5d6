<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('escorts', function (Blueprint $table) {
            $table->string('phone_number')->nullable()->after('occupation');
            $table->string('whatsapp_number')->nullable()->after('phone_number');
            $table->boolean('show_phone_number')->default(true)->after('whatsapp_number');
            $table->boolean('show_whatsapp')->default(true)->after('show_phone_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('escorts', function (Blueprint $table) {
            $table->dropColumn(['phone_number', 'whatsapp_number', 'show_phone_number', 'show_whatsapp']);
        });
    }
};
