<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('escort_languages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('escort_id')->constrained()->onDelete('cascade');
            $table->foreignId('language_id')->constrained()->onDelete('cascade');
            $table->enum('proficiency', ['minimal', 'conversational', 'fluent'])->default('minimal');
            $table->timestamps();

            // Prevent duplicate entries
            $table->unique(['escort_id', 'language_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('escort_languages');
    }
};
