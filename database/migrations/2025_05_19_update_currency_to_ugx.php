<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing rates to use UGX
        DB::table('escort_rates')->update(['currency' => 'UGX']);
        
        // Update the default value for the currency column
        Schema::table('escort_rates', function (Blueprint $table) {
            $table->string('currency', 3)->default('UGX')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert existing rates to use USD
        DB::table('escort_rates')->update(['currency' => 'USD']);
        
        // Revert the default value for the currency column
        Schema::table('escort_rates', function (Blueprint $table) {
            $table->string('currency', 3)->default('USD')->change();
        });
    }
};
