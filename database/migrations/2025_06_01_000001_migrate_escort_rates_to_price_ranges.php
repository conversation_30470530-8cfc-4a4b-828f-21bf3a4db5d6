<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Migrate existing fixed prices to price ranges
        // For each rate, set min_price to 90% of price and max_price to 110% of price
        // This creates a reasonable range around the existing price
        
        $rates = DB::table('escort_rates')->get();
        
        foreach ($rates as $rate) {
            $updates = [];
            
            if ($rate->incall_price) {
                $minIncall = round($rate->incall_price * 0.9);
                $maxIncall = round($rate->incall_price * 1.1);
                
                $updates['min_incall_price'] = $minIncall;
                $updates['max_incall_price'] = $maxIncall;
            }
            
            if ($rate->outcall_price) {
                $minOutcall = round($rate->outcall_price * 0.9);
                $maxOutcall = round($rate->outcall_price * 1.1);
                
                $updates['min_outcall_price'] = $minOutcall;
                $updates['max_outcall_price'] = $maxOutcall;
            }
            
            if (!empty($updates)) {
                DB::table('escort_rates')
                    ->where('id', $rate->id)
                    ->update($updates);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No need to reverse this migration as it's just data transformation
        // The original price columns are still intact
    }
};
