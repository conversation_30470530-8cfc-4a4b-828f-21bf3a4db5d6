<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if the path column doesn't exist
        if (!Schema::hasColumn('escort_images', 'path')) {
            Schema::table('escort_images', function (Blueprint $table) {
                $table->string('path')->nullable()->after('image_path');
            });

            // Copy image_path to path for existing records
            DB::table('escort_images')->update([
                'path' => DB::raw('image_path')
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('escort_images', function (Blueprint $table) {
            $table->dropColumn('path');
        });
    }
};
