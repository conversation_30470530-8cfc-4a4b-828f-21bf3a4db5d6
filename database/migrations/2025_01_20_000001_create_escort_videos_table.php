<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('escort_videos', function (Blueprint $table) {
            $table->id();
            $table->foreignId('escort_id')->constrained()->onDelete('cascade');
            $table->string('video_path');
            $table->string('path')->nullable(); // For compatibility with existing path structure
            $table->string('thumbnail_path')->nullable(); // For video thumbnails
            $table->boolean('is_primary')->default(false);
            $table->boolean('is_main')->default(false); // For compatibility with existing main structure
            $table->integer('sort_order')->default(0);
            $table->integer('file_size')->nullable(); // Store file size in bytes
            $table->integer('duration')->nullable(); // Store duration in seconds
            $table->string('mime_type')->nullable(); // Store video mime type
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('escort_videos');
    }
};
