<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('agencies', function (Blueprint $table) {
            // Check if slug column exists
            if (!Schema::hasColumn('agencies', 'slug')) {
                $table->string('slug')->nullable()->after('name');
                // Add a unique index to the slug column
                $table->unique('slug');
            }

            // Add profile_views column
            if (!Schema::hasColumn('agencies', 'profile_views')) {
                $table->unsignedInteger('profile_views')->default(0)->after('is_premium');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('agencies', function (Blueprint $table) {
            // Drop columns if they exist
            if (Schema::hasColumn('agencies', 'slug')) {
                $table->dropUnique(['slug']);
                $table->dropColumn('slug');
            }

            if (Schema::hasColumn('agencies', 'profile_views')) {
                $table->dropColumn('profile_views');
            }
        });
    }
};
