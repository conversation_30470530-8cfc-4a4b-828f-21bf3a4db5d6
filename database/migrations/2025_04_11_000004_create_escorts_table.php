<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('escorts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('agency_id')->nullable()->constrained()->onDelete('set null');
            $table->string('name')->comment('display name');
            $table->enum('gender', ['female', 'male', 'couple', 'gay', 'transsexual']);
            $table->date('date_of_birth');
            $table->enum('ethnicity', [
                'Latin', 'Caucasian', 'Black', 'White', 'MiddleEast', 
                'Asian', 'Indian', 'Aborigine', 'Native American', 'Other'
            ]);
            $table->enum('hair_color', [
                'Black', 'Blonde', 'Brown', 'Brunette', 'Chestnut', 
                'Auburn', 'Dark-blonde', 'Golden', 'Red', 'Grey', 
                'Silver', 'White', 'Other'
            ]);
            $table->enum('hair_length', ['Bald', 'Short', 'Shoulder', 'Long', 'Very Long']);
            $table->enum('bust_size', [
                'Very small', 'Small(A)', 'Medium(B)', 'Large(C)', 
                'Very Large(D)', 'Enormous(E+)'
            ])->nullable();
            $table->integer('height_cm');
            $table->integer('weight_kg');
            $table->enum('build', ['Skinny', 'Slim', 'Regular', 'Curvy', 'Fat']);
            $table->enum('looks', ['Nothing Special', 'Average', 'Sexy', 'Ultra Sexy']);
            $table->boolean('smoker')->default(false);
            $table->text('about')->nullable();
            $table->string('education')->nullable();
            $table->string('sports')->nullable();
            $table->string('hobbies')->nullable();
            $table->string('zodiac_sign')->nullable();
            $table->string('sexual_orientation')->nullable();
            $table->string('occupation')->nullable();
            $table->boolean('is_premium')->default(false);
            $table->boolean('is_verified')->default(false);
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_independent')->default(true);
            $table->boolean('incall_available')->default(false);
            $table->boolean('outcall_available')->default(false);
            $table->boolean('is_new')->default(true);
            $table->boolean('is_online')->default(false);
            $table->integer('profile_views')->default(0);
            $table->string('slug')->unique();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('escorts');
    }
};
