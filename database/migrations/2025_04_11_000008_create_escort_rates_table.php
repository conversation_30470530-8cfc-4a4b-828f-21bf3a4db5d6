<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('escort_rates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('escort_id')->constrained()->onDelete('cascade');
            $table->enum('duration', [
                '30min', '1hour', '2hours', '3hours', 
                '6hours', '12hours', '24hours'
            ]);
            $table->decimal('incall_price', 10, 2)->nullable();
            $table->decimal('outcall_price', 10, 2)->nullable();
            $table->string('currency', 3)->default('USD');
            $table->timestamps();

            // Prevent duplicate entries
            $table->unique(['escort_id', 'duration']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('escort_rates');
    }
};
