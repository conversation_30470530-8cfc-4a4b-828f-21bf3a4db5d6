<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, we need to modify the enum column to include 'annual'
        // Since MySQL doesn't support directly altering enum values, we need to modify the column
        Schema::table('escort_status_requests', function (Blueprint $table) {
            // Change the duration column to allow null temporarily
            $table->string('duration', 10)->nullable()->change();
        });

        // Now update the column type to be an enum with the new values
        DB::statement("ALTER TABLE escort_status_requests MODIFY duration ENUM('day', 'week', 'month', 'annual') NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert the changes
        Schema::table('escort_status_requests', function (Blueprint $table) {
            // Change the duration column to allow null temporarily
            $table->string('duration', 10)->nullable()->change();
        });

        // Now update the column type to be an enum with the original values
        DB::statement("ALTER TABLE escort_status_requests MODIFY duration ENUM('day', 'week', 'month') NULL");
    }
};
