<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, update any existing 'regular' users to 'escort' type
        DB::table('users')
            ->where('user_type', 'regular')
            ->update(['user_type' => 'escort']);

        // Then modify the enum to remove 'regular' option
        DB::statement("ALTER TABLE users MODIFY COLUMN user_type ENUM('escort', 'agency', 'admin') DEFAULT 'escort'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Add 'regular' back to the enum
        DB::statement("ALTER TABLE users MODIFY COLUMN user_type ENUM('regular', 'escort', 'agency', 'admin') DEFAULT 'escort'");
    }
};
