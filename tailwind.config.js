import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';
import aspectRatio from '@tailwindcss/aspect-ratio';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
    ],

    theme: {
        extend: {
            fontFamily: {
                sans: ['Montserrat', ...defaultTheme.fontFamily.sans],
                serif: ['Playfair Display', ...defaultTheme.fontFamily.serif],
            },
            colors: {
                pink: {
                    DEFAULT: '#FF5A8C',
                    '50': '#FFF0F5',
                    '100': '#FFE0EB',
                    '200': '#FFC1D6',
                    '300': '#FFA2C2',
                    '400': '#FF83AD',
                    '500': '#FF5A8C',
                    '600': '#FF2767',
                    '700': '#F3003D',
                    '800': '#C0002F',
                    '900': '#8D0023',
                },
                dark: {
                    DEFAULT: '#1A1A1A',
                    '50': '#F2F2F2',
                    '100': '#E6E6E6',
                    '200': '#CCCCCC',
                    '300': '#B3B3B3',
                    '400': '#999999',
                    '500': '#808080',
                    '600': '#666666',
                    '700': '#4D4D4D',
                    '800': '#333333',
                    '900': '#1A1A1A',
                },
                primary: {
                    DEFAULT: '#FF5A8C', // Same as pink
                    '50': '#FFF0F5',
                    '100': '#FFE0EB',
                    '200': '#FFC1D6',
                    '300': '#FFA2C2',
                    '400': '#FF83AD',
                    '500': '#FF5A8C',
                    '600': '#FF2767',
                    '700': '#F3003D',
                    '800': '#C0002F',
                    '900': '#8D0023',
                },
            },
        },
    },

    plugins: [forms, aspectRatio],
};
